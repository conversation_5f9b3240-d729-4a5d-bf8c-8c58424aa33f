#!bin/python
#coding=gbk
import os
import re
import sys
import configparse_pgs
import calc_pgs
import write_csv_pgs


taskstr = sys.argv[1]

dict_ = {}
dict_ = configparse_pgs.Pgsta(taskstr)

for testset in dict_:
    pgsfile = testset
    mlfile = dict_[testset][0]
    outdir = dict_[testset][1]
    #print ('statisticsing in %s'%outdir)
    calc_pgs.getPgsCorrResult(mlfile, pgsfile)
    print ('statisticsing in %s'%outdir)

write_csv_pgs.CsvGetFromStaEachTask(taskstr,"sta.sub")