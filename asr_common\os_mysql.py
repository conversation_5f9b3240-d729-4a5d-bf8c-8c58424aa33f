﻿#-*- coding: utf-8 -*-
# 只需要在顶部声明 CurrentConfig.ONLINE_HOST 即可
from pyecharts.globals import CurrentConfig
CurrentConfig.ONLINE_HOST = "http://localhost:8082/assets/"
import os
import time
from datetime import date

import pymysql
from PIL import Image
from pyecharts import options as opts
from pyecharts.charts import Line, Page

from selenium import webdriver

from asr_common.global_variables import image_dict
from asr_common.wxwork_notification import WxWorkRobot


def full_screenshot(driver, file):
    file_names = []

    print("Starting chrome full page screenshot workaround ...")

    driver.maximize_window()
    s = driver.get_window_size()

    total_width = driver.execute_script("return document.body.offsetWidth")
    total_height = driver.execute_script("return document.body.parentNode.scrollHeight")
    viewport_width = driver.execute_script("return document.body.clientWidth")
    viewport_height = driver.execute_script("return window.innerHeight")
    print("Total: ({0}, {1}), Viewport: ({2},{3})".format(total_width, total_height,viewport_width,viewport_height))
    rectangles = []

    j = 0
    while j < total_height:
        ii = 0
        top_height = j + viewport_height

        if top_height > total_height:
            top_height = total_height

        while ii < total_width:
            top_width = ii + viewport_width
            if top_width > total_width:
                top_width = total_width

            # print("Appending rectangle ({0},{1},{2},{3})".format(ii, i, top_width, top_height))
            rectangles.append((ii, j, top_width, top_height))
            ii = ii + viewport_width
        j = j + viewport_height

    part = 0
    for rectangle in rectangles:
        driver.execute_script("window.scrollTo({0}, {1})".format(rectangle[0], rectangle[1]))
        # print("Scrolled To ({0},{1})".format(rectangle[0], rectangle[1]))
        time.sleep(2)

        file_name = "./bat/part_{0}.png".format(part)
        # print("Capturing {0} ...".format(file_name))

        if part == len(rectangles) - 2:
            driver.set_window_size(s['width'], total_height % viewport_height + 150)
        else:
            driver.set_window_size(s['width'], s['height'])

        driver.get_screenshot_as_file(file_name)

        part = part + 1
        file_names.append(file_name)

    #将所有图片粘贴到一个大图中
    images = [Image.open(x) for x in file_names]
    widths, heights = zip(*(i.size for i in images))

    total_width = max(widths)
    total_height = sum(heights)

    new_img = Image.new('RGB', (total_width, total_height))

    y_offset = 0
    for img in images:
        new_img.paste(img, (0, y_offset))
        y_offset += img.height

    new_img.save(file)


def get_chart(data, image_name):
    line_charts = []  # Store all line charts
    for every_key, every_data in data.items():
        chart1 = []
        chart2 = []
        chart3 = []

        for _, value in every_data.items():
            chart1.append(value[0])
            if len(value) > 1:
                chart2.append(value[1])
            if len(value) > 2:
                chart3.append(value[2])

        line_chart = Line()
        line_chart.add_xaxis(xaxis_data=list(every_data.keys()))

        if image_name in ['海尔tts合成', '美的tts合成', 'tts流式合成', 'tts流式合成_http接口']:
            if image_name == 'tts流式合成_http接口':
                vcn, stream = every_key.split(",")
                if stream.split('=')[1] == '0':
                    title = f"{image_name}_非流式"
                else:
                    title = f"{image_name}_流式"

                line_chart.add_yaxis(f"{vcn.split('=')[1]}发送请求至收到url耗时", chart3)
                line_chart.add_yaxis(f"{vcn.split('=')[1]}请求url至收到首帧耗时", chart1)
                line_chart.add_yaxis(f"{vcn.split('=')[1]}请求url至收到4k首帧耗时", chart2)

            else:
                vcn, tts_res_type = every_key.split(",")
                if image_name != 'tts流式合成':
                    if tts_res_type.split('=')[1] != "":
                        title = f"{image_name}_{tts_res_type.split('=')[1]}方式"
                        line_chart.add_yaxis(f"{vcn.split('=')[1]}发送请求至收到url耗时", chart2)
                        line_chart.add_yaxis(f"{vcn.split('=')[1]}请求url至收到首帧耗时", chart1)
                    else:
                        title = f"{image_name}"
                        line_chart.add_yaxis(f"{vcn.split('=')[1]}发送文本至收到首帧耗时", chart1)
                else:
                    if tts_res_type.split('=')[1] != "":
                        title = f"{image_name}_{tts_res_type.split('=')[1]}方式"
                        line_chart.add_yaxis(f"{vcn.split('=')[1]}请求url至收到首帧耗时", chart1)
                    else:
                        title = f"{image_name}"
                        line_chart.add_yaxis(f"{vcn.split('=')[1]}发送文本至收到首帧耗时", chart1)

        else:
            title = f"{image_name}{every_key}"
            if image_name in ['极致公板识别', '美的识别']:
                if image_name == '极致公板识别':
                    line_chart.add_yaxis("连接建立的时间", chart3)
                line_chart.add_yaxis("vad到finish为true的时间", chart1)
                line_chart.add_yaxis("asr到nlp的时间", chart2)
            else:
                line_chart.add_yaxis("vad到finish为true的时间", chart1)

        line_chart.set_global_opts(
                title_opts=opts.TitleOpts(title=title),
                yaxis_opts=opts.AxisOpts(axislabel_opts=opts.LabelOpts(formatter="{value} ms")),
                legend_opts=opts.LegendOpts(legend_icon="circle", pos_left="40%"),
            )

        line_charts.append(line_chart)  # Add the line chart to the list

    page = Page(layout=Page.SimplePageLayout)
    for chart in line_charts:
        page.add(chart)
    page.render(f"./result/{image_name}.html")

    driver = webdriver.Chrome(f"{os.getcwd()}/html_to_jpg/chromedriver.exe")
    driver.get(f"{os.getcwd()}/result/{image_name}.html")
    driver.maximize_window()  # Maximize the browser window
    time.sleep(10)  # Wait for the page to load
    full_screenshot(driver, f"./result/{image_name}.png")
    driver.quit()


def send_chart_png_to_wxwork(wxwork_appid, image_path):
    wx = WxWorkRobot(wxwork_appid)
    wx.send_image_to_wxwork(image_path)


def send_chart_file_to_wxwork(wxwork_appid, file_path, rename, message = ""):
    wx = WxWorkRobot(wxwork_appid)
    wx.send_file_to_wxwork(file_path, rename, message)


def connect_mysql(host,port,user,password,database):
    # 连接database
    conn = pymysql.connect(host=host, user=user, password=password, port=port, database=database)
    # 得到一个可以执行SQL语句的光标对象
    return conn


def read_mysql(conn, datasheet, select_time=date.today().strftime("%Y-%m-%d"), limit=None, vcn='x4_yezi', svad='1', scene="main", nlp_type="aiui", fullduplex=0, tts_res_type="", stream="0"):
    with conn.cursor() as cursor:
        if datasheet == image_dict['海尔识别']:
            if limit:
                select_data = "select text, end_to_isfinish from {} where date = %s AND svad = %s AND scene = %s ORDER BY time DESC LIMIT %s".format(datasheet)
                cursor.execute(select_data, (select_time, svad, scene, limit))
            else:
                select_data = "select round(avg(end_to_isfinish),2) from {} where date = %s AND svad = %s AND scene = %s".format(datasheet)
                cursor.execute(select_data, (select_time, svad, scene))

        elif datasheet == image_dict['美的识别']:
            if limit:
                select_data = "select text, end_to_isfinish, asr_to_nlp from {} where date = %s AND scene = %s ORDER BY time DESC LIMIT %s".format(datasheet)
                cursor.execute(select_data, (select_time, scene, limit))
            else:
                select_data = "select round(avg(end_to_isfinish),2), round(avg(asr_to_nlp),2) from {} where date = %s AND scene = %s".format(datasheet)
                cursor.execute(select_data, (select_time, scene))

        elif datasheet == image_dict['极致公板识别']:
            if limit:
                select_data = "select text, end_to_isfinish, asr_to_nlp, connect_time from {} where date = %s AND nlp_type = %s AND fullduplex = %s ORDER BY time DESC LIMIT %s".format(datasheet)
                cursor.execute(select_data, (select_time, nlp_type, fullduplex, limit))
            else:
                select_data = "select round(avg(end_to_isfinish),2), round(avg(asr_to_nlp),2), round(avg(connect_time),2) from {} where date = %s AND nlp_type = %s AND fullduplex = %s".format(datasheet)
                cursor.execute(select_data, (select_time, nlp_type, fullduplex))

        elif datasheet == image_dict['tts流式合成_2种格式']:
            if limit:
                select_data = "select text, sid, exp_format, act_format, env from {} where date = %s AND vcn = %s AND tts_res_type = %s ORDER BY time DESC LIMIT %s".format(datasheet)
                cursor.execute(select_data, (select_time, vcn, tts_res_type, limit))
            else:
                select_data = "select text, sid, exp_format, act_format, env from {} where date = %s AND vcn = %s AND tts_res_type = %s".format(datasheet)
                cursor.execute(select_data, (select_time, vcn, tts_res_type))

        elif datasheet in [image_dict['海尔tts合成'], image_dict['美的tts合成']]:
            if limit:
                select_data = "select text, req_to_rec, send_to_get_url from {} where date = %s AND vcn = %s AND tts_res_type = %s ORDER BY time DESC LIMIT %s".format(datasheet)
                cursor.execute(select_data, (select_time, vcn, tts_res_type, limit))
            else:
                select_data = "select round(avg(req_to_rec),2), round(avg(send_to_get_url),2) from {} where date = %s AND vcn = %s AND tts_res_type = %s".format(datasheet)
                cursor.execute(select_data, (select_time, vcn, tts_res_type))

        elif datasheet == image_dict['tts流式合成_http接口']:
            if limit:
                select_data = "select text, req_to_rec, req_to_rec_4k, send_to_get_url from {} where date = %s AND vcn = %s AND stream = %s ORDER BY time DESC LIMIT %s".format(datasheet)
                cursor.execute(select_data, (select_time, vcn, stream, limit))
            else:
                select_data = "select round(avg(req_to_rec),2), round(avg(req_to_rec_4k),2), round(avg(send_to_get_url),2) from {} where date = %s AND vcn = %s AND stream = %s".format(datasheet)
                cursor.execute(select_data, (select_time, vcn, stream))

        else:
            if limit:
                select_data = "select text, req_to_rec from {} where date = %s AND vcn = %s AND tts_res_type = %s ORDER BY time DESC LIMIT %s".format(datasheet)
                cursor.execute(select_data, (select_time, vcn, tts_res_type, limit))
            else:
                select_data = "select round(avg(req_to_rec),2) from {} where date = %s AND vcn = %s AND tts_res_type = %s".format(datasheet)
                cursor.execute(select_data, (select_time, vcn, tts_res_type))

        data = cursor.fetchall()
    return data


def write_mysql_haier(conn, datasheet, datalist):
    # Create a cursor object to execute SQL statements
    with conn.cursor() as cursor:
        sql_insert = """
        INSERT INTO {} (time, text, sid, asr_result, receive_vad_ms, first_frame_time, vad_end_time, first_frame_to_end, isfinish_time, end_to_isfinish, svad, scene, date)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """.format(datasheet)
        cursor.execute(sql_insert, datalist)
        conn.commit()


def write_mysql_midea(conn, datasheet, datalist):
    # Create a cursor object to execute SQL statements
    with conn.cursor() as cursor:
        sql_insert = """
        INSERT INTO {} (time, text, requestId, mid, asr_result, receive_vad_ms, first_frame_time, vad_end_time, first_frame_to_end, isfinish_time, end_to_isfinish, asr_to_nlp, scene, date)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """.format(datasheet)
        cursor.execute(sql_insert, datalist)
        conn.commit()


def write_mysql_common(conn, datasheet, datalist):
    # Create a cursor object to execute SQL statements
    with conn.cursor() as cursor:
        sql_insert = """
        INSERT INTO {} (time, text, connect_time, sid, asr_result, nlp_result, receive_vad_ms, first_frame_time, vad_end_time, first_frame_to_end, isfinish_time, end_to_isfinish, nlp_time, asr_to_nlp, nlp_type, fullduplex, date)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """.format(datasheet)
        cursor.execute(sql_insert, datalist)
        conn.commit()


def write_mysql_tts(conn, datasheet, datalist):
    # Create a cursor object to execute SQL statements
    with conn.cursor() as cursor:
        sql_insert = """
        INSERT INTO {} (time, text, vcn, sid, req_tts_time, rec_first_frame_time, req_to_rec, date, tts_res_type) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        """.format(datasheet)
        cursor.execute(sql_insert, datalist)
        conn.commit()


def write_mysql_aiui_tts(conn, datasheet, datalist):
    # Create a cursor object to execute SQL statements
    with conn.cursor() as cursor:
        sql_insert = """
        INSERT INTO {} (date, time, text, vcn, sid, send_text_time, get_tts_url_time, send_to_get_url, req_tts_url_time, rec_first_frame_time, req_to_rec, tts_res_type) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """.format(datasheet)
        cursor.execute(sql_insert, datalist)
        conn.commit()


def write_mysql_tts_http(conn, datasheet, datalist):
    # Create a cursor object to execute SQL statements
    with conn.cursor() as cursor:
        sql_insert = """
        INSERT INTO {} (date, time, text, stream, vcn, sid, send_text_time, get_tts_url_time, send_to_get_url, req_tts_url_time, rec_first_frame_time, req_to_rec, rec_first_frame_4k_time, req_to_rec_4k) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """.format(datasheet)
        cursor.execute(sql_insert, datalist)
        conn.commit()


def write_mysql_tts_two_format(conn, datasheet, datalist):
    # Create a cursor object to execute SQL statements
    with conn.cursor() as cursor:
        sql_insert = """
        INSERT INTO {} (time, text, vcn, sid, exp_format, act_format, date, tts_res_type, env) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        """.format(datasheet)
        cursor.execute(sql_insert, datalist)
        conn.commit()


