Starting Midea test script in Go...
Thread No (from arg): None
Processing sheet: 313sp_off_a2
Sheet 313sp_off_a2: Processing data (Excel row 2), Audio: ctm0001030e@hu17b4cb4fb36020c902#46275217.pcm
Processing task: Sheet=313sp_off_a2, Audio=ctm0001030e@hu17b4cb4fb36020c902#46275217.pcm, Row=2, Expected=电视怎么调台
Connecting to: wss://beijing-midea.listenai.com/midea/proxy?appid=5e017b34&checksum=8d042bb86426a5e400ec7d90a31d3aec&curtime=1748251987&param=eyJhdXRoX2lkIjoiODA5MDIyZWYtYTM3MS00YWIyLWI1OGEtMmRmYzVkMmNjN2EyIiwiZGF0YV90eXBlIjoiYXVkaW8ifQ==&signtype=md5
Received for ctm0001030e@hu17b4cb4fb36020c902#46275217.pcm: {"action":"connected","cid":"cidee99c50d@dxf98d1b8f10d43eef00","code":"0","data":"","desc":"success"}
Sending createSession for ctm0001030e@hu17b4cb4fb36020c902#46275217.pcm: {"action":"start","scene":"asr","params":{"mid":"79c50b99-2673-4d4e-9a28-b582b8c974c2","asr":{"eos":500,"enable_vpr_rec":true,"enable_vpr_rec_info":true,"enable_vpr_cluster":true,"vpr_rec":{"group_id":"abc_123"},"encoding":"raw","channels":1,"bit_depth":16,"sample_rate":16000,"pd":"midea|midea-dragon"},"nlu":{"vcn":"x2_xiaojuan","pitch":60,"speed":60,"volume":50,"tts_aue":"raw"}}}
Error unmarshalling message for ctm0001030e@hu17b4cb4fb36020c902#46275217.pcm: json: cannot unmarshal string into Go struct field WebSocketReceivePayload.code of type int. Raw: {"action":"connected","cid":"cidee99c50d@dxf98d1b8f10d43eef00","code":"0","data":"","desc":"success"}
Received for ctm0001030e@hu17b4cb4fb36020c902#46275217.pcm: {"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3cb3@dx1970bf05208b8a9532"}
Error unmarshalling message for ctm0001030e@hu17b4cb4fb36020c902#46275217.pcm: json: cannot unmarshal string into Go struct field WebSocketReceivePayload.code of type int. Raw: {"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3cb3@dx1970bf05208b8a9532"}
WebSocket read error for ctm0001030e@hu17b4cb4fb36020c902#46275217.pcm: websocket: close 1006 (abnormal closure): unexpected EOF
Finished processing for audio: ctm0001030e@hu17b4cb4fb36020c902#46275217.pcm
New Excel file saved as: ./data/a1&a2&a3_结果汇总_20250526.xlsx
#####运行完成,点击查看结果按钮查看结果!##########
