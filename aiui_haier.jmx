<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2" properties="5.0" jmeter="5.6.3">
  <hashTree>
    <TestPlan guiclass="TestPlanGui" testclass="TestPlan" testname="vad_interface" enabled="true">
      <stringProp name="TestPlan.comments"></stringProp>
      <boolProp name="TestPlan.functional_mode">false</boolProp>
      <boolProp name="TestPlan.serialize_threadgroups">false</boolProp>
      <elementProp name="TestPlan.user_defined_variables" elementType="Arguments" guiclass="ArgumentsPanel" testclass="Arguments" testname="用户定义的变量" enabled="true">
        <collectionProp name="Arguments.arguments"/>
      </elementProp>
      <stringProp name="TestPlan.user_define_classpath"></stringProp>
      <boolProp name="TestPlan.tearDown_on_shutdown">true</boolProp>
    </TestPlan>
    <hashTree>
      <Arguments guiclass="ArgumentsPanel" testclass="Arguments" testname="运行根目录">
        <collectionProp name="Arguments.arguments">
          <elementProp name="root.path" elementType="Argument">
            <stringProp name="Argument.name">root.path</stringProp>
            <stringProp name="Argument.value">E:\lxy\jmeter\evs_perf_tool\effect_jmeter</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
          </elementProp>
        </collectionProp>
      </Arguments>
      <hashTree/>
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="vad测试">
        <intProp name="ThreadGroup.num_threads">10</intProp>
        <intProp name="ThreadGroup.ramp_time">1</intProp>
        <longProp name="ThreadGroup.duration">600</longProp>
        <boolProp name="ThreadGroup.same_user_on_next_iteration">true</boolProp>
        <boolProp name="ThreadGroup.scheduler">true</boolProp>
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController" guiclass="LoopControlPanel" testclass="LoopController" testname="循环控制器">
          <intProp name="LoopController.loops">-1</intProp>
          <boolProp name="LoopController.continue_forever">false</boolProp>
        </elementProp>
      </ThreadGroup>
      <hashTree>
        <GenericController guiclass="LogicControllerGui" testclass="GenericController" testname="vad"/>
        <hashTree>
          <SystemSampler guiclass="SystemSamplerGui" testclass="SystemSampler" testname="midea_压测_${__threadNum}">
            <boolProp name="SystemSampler.checkReturnCode">false</boolProp>
            <stringProp name="SystemSampler.expectedReturnCode">0</stringProp>
            <stringProp name="SystemSampler.command">python</stringProp>
            <elementProp name="SystemSampler.arguments" elementType="Arguments" guiclass="ArgumentsPanel" testclass="Arguments" testname="用户定义的变量">
              <collectionProp name="Arguments.arguments">
                <elementProp name="" elementType="Argument">
                  <stringProp name="Argument.name"></stringProp>
                  <stringProp name="Argument.value">midea_test.py</stringProp>
                  <stringProp name="Argument.metadata">=</stringProp>
                </elementProp>
                <elementProp name="" elementType="Argument">
                  <stringProp name="Argument.name"></stringProp>
                  <stringProp name="Argument.value">--thread-no</stringProp>
                  <stringProp name="Argument.metadata">=</stringProp>
                </elementProp>
                <elementProp name="" elementType="Argument">
                  <stringProp name="Argument.name"></stringProp>
                  <stringProp name="Argument.value">${__threadNum}</stringProp>
                  <stringProp name="Argument.metadata">=</stringProp>
                </elementProp>
              </collectionProp>
            </elementProp>
            <elementProp name="SystemSampler.environment" elementType="Arguments" guiclass="ArgumentsPanel" testclass="Arguments" testname="用户定义的变量">
              <collectionProp name="Arguments.arguments"/>
            </elementProp>
            <stringProp name="SystemSampler.directory">C:\Users\<USER>\Documents\midea_test</stringProp>
            <longProp name="SystemSampler.timeout">5000000</longProp>
          </SystemSampler>
          <hashTree>
            <BeanShellPreProcessor guiclass="TestBeanGUI" testclass="BeanShellPreProcessor" testname="BeanShell PreProcessor" enabled="false">
              <stringProp name="filename"></stringProp>
              <stringProp name="parameters"></stringProp>
              <boolProp name="resetInterpreter">false</boolProp>
              <stringProp name="script">import java.util.ArrayList;
import java.util.List;


String listenResult = vars.get(&quot;_listenResult&quot;);
String[] strAry=listenResult.split(&quot;&quot;);
String regSep = vars.get(&quot;reg_sep&quot;);
StringBuffer sb=new StringBuffer();
for(int i=0;i&lt;strAry.length;i++){
   if(i==(strAry.length-1)){
       sb.append(strAry[i]);
   }else{
       //sb.append(strAry[i]).append(&quot;.*&quot;);
       sb.append(strAry[i]).append(regSep);
   }
}
//log.info(&quot;string:&quot;+new String(sb));
vars.put(&quot;listenResult&quot;,new String(sb));

String _voiceEnd = vars.get(&quot;_voiceEnd&quot;);

if ((_voiceEnd!=null)&amp;&amp;(_voiceEnd.length())!=0){
	vars.put(&quot;voiceEnd&quot;,_voiceEnd);
}else{
	vars.put(&quot;voiceEnd&quot;,&quot;0&quot;);
}

vars.put(&quot;_voiceEnd&quot;,&quot;0&quot;);



String _voiceStart = vars.get(&quot;_voiceStart&quot;);

if ((_voiceStart!=null)&amp;&amp;(_voiceStart.length())!=0){
	vars.put(&quot;voiceStart&quot;,_voiceStart);
}else{
	vars.put(&quot;voiceStart&quot;,&quot;0&quot;);
}

vars.put(&quot;_voiceStart&quot;,&quot;0&quot;);</stringProp>
            </BeanShellPreProcessor>
            <hashTree/>
            <PoissonRandomTimer guiclass="PoissonRandomTimerGui" testclass="PoissonRandomTimer" testname="Poisson Random Timer" enabled="false">
              <stringProp name="ConstantTimer.delay">1000</stringProp>
              <stringProp name="RandomTimer.range">1500</stringProp>
            </PoissonRandomTimer>
            <hashTree/>
            <GaussianRandomTimer guiclass="GaussianRandomTimerGui" testclass="GaussianRandomTimer" testname="高斯随机定时器" enabled="false">
              <stringProp name="ConstantTimer.delay">300</stringProp>
              <stringProp name="RandomTimer.range">100.0</stringProp>
            </GaussianRandomTimer>
            <hashTree/>
            <ResponseAssertion guiclass="AssertionGui" testclass="ResponseAssertion" testname="听写内容验证" enabled="false">
              <collectionProp name="Asserion.test_strings">
                <stringProp name="-600809534">.*${listenResult}.*</stringProp>
              </collectionProp>
              <stringProp name="Assertion.test_field">Assertion.response_data</stringProp>
              <boolProp name="Assertion.assume_success">false</boolProp>
              <intProp name="Assertion.test_type">2</intProp>
              <stringProp name="Assertion.custom_message"></stringProp>
            </ResponseAssertion>
            <hashTree/>
            <DebugPostProcessor guiclass="TestBeanGUI" testclass="DebugPostProcessor" testname="Debug PostProcessor" enabled="false">
              <boolProp name="displayJMeterProperties">false</boolProp>
              <boolProp name="displayJMeterVariables">true</boolProp>
              <boolProp name="displaySamplerProperties">true</boolProp>
              <boolProp name="displaySystemProperties">false</boolProp>
            </DebugPostProcessor>
            <hashTree/>
          </hashTree>
          <BeanShellPostProcessor guiclass="TestBeanGUI" testclass="BeanShellPostProcessor" testname="BeanShell PostProcessor" enabled="true">
            <stringProp name="filename"></stringProp>
            <stringProp name="parameters"></stringProp>
            <boolProp name="resetInterpreter">false</boolProp>
            <stringProp name="script">prev.setDataEncoding(&quot;utf-8&quot;)</stringProp>
          </BeanShellPostProcessor>
          <hashTree/>
        </hashTree>
        <ResultCollector guiclass="ViewResultsFullVisualizer" testclass="ResultCollector" testname="察看结果树">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>true</responseData>
              <samplerData>false</samplerData>
              <xml>true</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>true</responseHeaders>
              <requestHeaders>true</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <sentBytes>true</sentBytes>
              <url>true</url>
              <fileName>true</fileName>
              <hostname>true</hostname>
              <threadCounts>true</threadCounts>
              <sampleCount>true</sampleCount>
              <idleTime>true</idleTime>
              <connectTime>true</connectTime>
            </value>
          </objProp>
          <stringProp name="filename"></stringProp>
        </ResultCollector>
        <hashTree/>
        <ResultCollector guiclass="StatVisualizer" testclass="ResultCollector" testname="聚合报告">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>false</responseData>
              <samplerData>false</samplerData>
              <xml>false</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <threadCounts>true</threadCounts>
              <idleTime>true</idleTime>
            </value>
          </objProp>
          <stringProp name="filename"></stringProp>
        </ResultCollector>
        <hashTree/>
      </hashTree>
    </hashTree>
  </hashTree>
</jmeterTestPlan>
