#!bin/python
#coding=gbk
import os
import re
import sys
import configparser
import time


cfgname = 'mtrec.cfg'
outputdir = '../../output'



class CfgParser:
    def __init__(self,cfgname):
        self.cfgname = cfgname
        self.cp = configparser.ConfigParser(strict=False)
        self.cp.read(self.cfgname)
    def ErrPrint(self):
        print ("ERR: please check your config is rigit ?")
    def GetOptionVau(self,section,option):
        vau = self.cp.get(section,option)
        return vau
    def GetOptionName(self,section):
        vau = self.cp.options(section)
        return vau
    def AddOption(self,section,option,vau):
        self.cp.set(section,option,vau)
        self.cp.write(open(self.cfgname,'w'))
    def DelOption(self,section,option):
        self.cp.remove_option(section,option)
        self.cp.write(open(self.cfgname,'w'))    

def GetMlfile(inpath):
    list_ = os.listdir(inpath)
    for file_ in list_:
        if file_.endswith('.mlf'):
            file_path = os.path.join(inpath,file_)
            return file_path
        else:
            pass

def Pgsta(taskstr):
    rlt_mlf_dict = {}
    cfghandle = CfgParser(cfgname)
    tasklist = taskstr.split(',')
    for each in tasklist:
        testsetlist = cfghandle.GetOptionVau("task", each).split('@')[1].strip().split(',')
        testset_rlt_dir = os.path.join(outputdir,each)  
        for testset in testsetlist:
            vau_list = []
            testset_mlf = GetMlfile(testset.lstrip())
            testset_rlt = os.path.join(testset_rlt_dir,'pgs_rlt_' + testset.split('/')[-1] + '.txt')
            vau_list.append(testset_mlf)
            vau_list.append(testset_rlt_dir)
            rlt_mlf_dict[testset_rlt] = vau_list
    return rlt_mlf_dict

    
    
    
    
    
    