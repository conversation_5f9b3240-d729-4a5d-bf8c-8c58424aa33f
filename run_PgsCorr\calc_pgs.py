import os
import sys
import argparse
import chardet,codecs
import re
import difflib
import platform

'''
统计PGS效果
'''
__author__  = "huiyang2"
__date__    = "2022/6/23"
__update__    = "2022/7/1" # 新增本地PGS效果统计
__version__ = "1.0"

def getPgsCorrResult(mlf_file, pgs_file):
    print("mlf_file = %s" % mlf_file)
    print("pgs_file = %s " % pgs_file)
    last_pgs_file = pgs_file + '.sta.last'
    sub_pgs_file =  pgs_file + '.sta.sub'
    f_submlf = open("tmp.sub.mlf.txt","w", encoding='GBK', errors='ignore')
    f_submlf.write("#!MLF!#\n")
    f_subrlt = open("tmp.sub.rlt.txt","w", encoding='GBK', errors='ignore')
    f_subrlt.write("#!MLF!#\n")
    f_last_rlt = open("tmp.last.rlt.txt", "w", encoding='GBK', errors='ignore')
    f_last_rlt.write("#!MLF!#\n")
    d_pgs = analysisRecPgsFile(pgs_file)
    if not d_pgs:
        d_pgs = analysisLocalRecPgsFile(pgs_file)
    d_lab = mlf2Dict(mlf_file)
    for key in d_pgs.keys():
        l_pgs = d_pgs[key]
        try:
            l_lab = d_lab[key]
            for i in range(len(l_pgs)):
                init_min = 10000
                list_pgs_word = getWordList(l_pgs[i]) 
                if i == (len(l_pgs) - 1):
                    f_last_rlt.write('"' + key + "_" + str(i) + '.pcm"' + "\n")
                    for word in list_pgs_word:
                        f_last_rlt.write("%s\n" % word)
                    f_last_rlt.write(".\n")
                
                f_subrlt.write('"' + key + "_" + str(i) + '.pcm"' + "\n")
                f_submlf.write('"*/' + key + "_" + str(i) + '.lab"' + "\n")
                tmp_lab_linefeed = ""
                for lab in l_lab:
                    distance = calcDifflibLeven(l_pgs[i], lab)
                    if distance <= init_min:
                        init_min = distance
                        tmp_lab_linefeed = lab
                
                if not list_pgs_word:
                    list_pgs_word = ['*']
                    
                for word in list_pgs_word:
                    f_subrlt.writelines("%s\n" % word)
                f_subrlt.write(".\n")
                
                list_lab_word = getWordList(tmp_lab_linefeed)
                if not list_lab_word:
                    list_lab_word = ['*']
                for word in list_lab_word:
                    f_submlf.write("%s\n" % word)
                f_submlf.write(".\n")
        except KeyError:
            print("%s does not exist in lab" % key)
    f_last_rlt.close()
    f_submlf.close()
    f_subrlt.close()
    cmdStr("tmp.sub.rlt.txt", "tmp.sub.mlf.txt", sub_pgs_file)
    cmdStr("tmp.last.rlt.txt", "tmp.sub.mlf.txt", last_pgs_file)
    return
    
def cmdStr(rec_file, mlf_lile, out_file):
    if isLinux():
        os.system(
            './HResults -f -t -d  32 -e \"???\" \"sil\" -e \"???\" \"<s>\" -e \"???\" \"!Start\" -e \"???\" \"!End\" -e \"???\" \"</s>\" -I %s hmmlist %s >> %s' %(mlf_lile, rec_file, out_file))
    else:
        os.system(
            '.\\HResults.exe -t -d 32 -e \"???\" \"sil\" -e \"???\" \"<s>\" -e \"???\" \"!Start\" -e \"???\" \"!End\" -e \"???\" \"</s>\" -I %s .\hmmlist %s >> %s'%(mlf_lile, rec_file, out_file))
    return

def mlf2Dict(mlf_file):
    zimu_regex = re.compile(r'[a-zA-z]')
    linesmlf = readFile(mlf_file)
    d_lab = {}
    f_log = open("tmp.log","a")
    for i in range(len(linesmlf)):
        if type(linesmlf[i]) == bytes:
            linesmlf[i] = linesmlf[i].decode('gbk', "ignore")
        if re.search(".lab",linesmlf[i]):
            lab_name = linesmlf[i].split("/")[-1].split(".lab")[0]
            f_log.write("%s\n" % lab_name)
            lab_content_list_tmp = []
            lab_content_list = []
            tmp_str = ""
            for j in range(i+1,len(linesmlf)):
                if type(linesmlf[j]) == bytes:
                    linesmlf[j] = linesmlf[j].decode('gbk', "ignore")
                if linesmlf[j].startswith("."):
                    for k in range(len(lab_content_list_tmp)):
                        if zimu_regex.findall(lab_content_list_tmp[k].strip()) and zimu_regex.findall(lab_content_list_tmp[k-1].strip()):
                            tmp_str += " " + lab_content_list_tmp[k]
                        else:
                            tmp_str += lab_content_list_tmp[k]
                        lab_content_list.append(tmp_str)
                        f_log.write("%s\n" % tmp_str)
                    break
                else:
                    lab_content_list_tmp.append(linesmlf[j].strip())
            d_lab[lab_name] = lab_content_list
            f_log.write("=========================================================\n")
    f_log.close()
    return d_lab
    
    
def analysisRecPgsFile(pgs_file):
    lines = readFile(pgs_file)
    d_pgs = {}
    wav_name = ""
    for line in lines:
        line = line.strip()
        if line.endswith(".pcm") or line.endswith(".wav") or line.endswith(".PCM") or line.endswith(".WAV"):
            tmp_list = line.split('/')[-1].split('\\')[-1].split('.')
            del tmp_list[-1]
            wav_name = '.'.join(tmp_list)
        elif line.startswith("Pgs |"):
            l_pgs = line.split("| ")[-1].split('\t')
            if wav_name not in d_pgs.keys():
                d_pgs[wav_name] = l_pgs
        else:
            continue
    return d_pgs
    
def analysisLocalRecPgsFile(pgs_file):
    lines = readFile(pgs_file)
    d_pgs = {}
    wav_name = ""
    l_pgs = []
    for line in lines:
        line = line.strip()
        if line.endswith(".pcm") or line.endswith(".wav") or line.endswith(".PCM") or line.endswith(".WAV"):
            tmp_list = line.split('/')[-1].split('\\')[-1].split('.')
            del tmp_list[-1]
            wav_name = '.'.join(tmp_list)
        elif '.' == line:
            if wav_name not in d_pgs.keys():
                d_pgs[wav_name] = l_pgs
            l_pgs = []
        elif "====" in line or "Log Begin" in line or "Log End" in line:
            continue
        else:
            l_pgs.append(line)
    return d_pgs  
    
def readFile(file):
    '''
    Determine file encoding format
    '''
    with codecs.open(file, 'rb') as fp:
        cur_encoding = chardet.detect(fp.read())['encoding']
        if cur_encoding == "GB2312" or cur_encoding == "ISO-8859-9" or cur_encoding == "Big5":
            cur_encoding = "GBK"
            
    with codecs.open(file, 'rb', encoding=cur_encoding, errors='ignore') as fp_file:
        lines = fp_file.readlines()
        return lines
        
def calcDifflibLeven(str1, str2):
    distance = 0
    s = difflib.SequenceMatcher(None, str1, str2)
    for tag, i1, i2, j1, j2 in s.get_opcodes():
        if tag == 'replace':
            distance += max(i2-i1, j2-j1)
        elif tag == 'insert':
            distance += (j2-j1)
        elif tag == 'delete':
            distance += (i2-i1)
    return distance
    
def getWordList(s):
    # 把句子按字分开，中文按字分，英文按单词，数字按空格
    list_word = []
    regEx = re.compile('[\\W]+')
    res = re.compile(r"([\u4e00-\u9fa5])")
    p1 = regEx.split(s.lower())
    str1_list = []
    for str in p1:
        if res.split(str) == None:
            str1_list.append(str)
        else:
            ret = res.split(str)
            for ch in ret:
                str1_list.append(ch)
                list_word = [w for w in str1_list if len(w.strip()) > 0]
    return list_word
    
def isLinux():
    osName=platform.system()
    linux=True
    if osName=='Windows':
        linux=False
    return linux
