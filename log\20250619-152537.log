1750317937
param:b'{\n            "auth_id": "490f6b60662845308a8bda3a8cd8e69a",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid472a6cd5@dx91301bae96f13eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "5a69abe28f2f48dc93de00db55143cf2","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000ba66d@dx19787143650b8a9532"}
started:
ws start
####################
测试进行: ctm0001030e@hu17b4cb4fb36020c902#46275217.pcm
{"recordId":"ase000efd5b@hu1978714458905c2882:5a69abe28f2f48dc93de00db55143cf2","requestId":"ase000efd5b@hu1978714458905c2882","sessionId":"cid000ba66d@dx19787143650b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1750317942}
{"recordId":"gty000ba66e@dx19787143761b8a9532:5a69abe28f2f48dc93de00db55143cf2","requestId":"gty000ba66e@dx19787143761b8a9532","sessionId":"cid000ba66d@dx19787143650b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1750317943}
{"recordId":"gty000ba66e@dx19787143761b8a9532:5a69abe28f2f48dc93de00db55143cf2","requestId":"gty000ba66e@dx19787143761b8a9532","sessionId":"cid000ba66d@dx19787143650b8a9532","eof":"1","text":"电视怎么调台","pinyin":"","topic":"asr.speech.result","age":"elder","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1750317943}
send data finished:1750317943.2580483
{"recordId":"gty000ba66e@dx19787143761b8a9532:5a69abe28f2f48dc93de00db55143cf2","requestId":"gty000ba66e@dx19787143761b8a9532","sessionId":"cid000ba66d@dx19787143650b8a9532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"电视怎么调台","intentId":"chat","intentName":"闲聊","nlg":"这个问题有点难，你还是换一个吧。","shouldEndSession":true},"nlu":{"input":"电视怎么调台","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1750317943}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidb2df2180@dxe1031bae96f73eef00"}
连接正常关闭
#####运行完成,点击查看结果按钮查看结果!##########
