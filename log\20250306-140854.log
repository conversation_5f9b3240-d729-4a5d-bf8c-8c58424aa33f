1741241338
param:b'{\n            "auth_id": "8266192a38284f56bbb4d7e7f692ec7a",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidc0278256@dx34081b2487fb3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "b9095d3a33684d5bb2f569208bc2952c","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b15c9@dx1956a124d757844532"}
started:
ws start
####################
测试进行: 1.pcm_handle_0.pcm
{"recordId":"ase000f6bfc@hu1956a12521505c2882:b9095d3a33684d5bb2f569208bc2952c","requestId":"ase000f6bfc@hu1956a12521505c2882","sessionId":"cid000b15c9@dx1956a124d757844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741241340}
{"recordId":"gty000b15ca@dx1956a124e007844532:b9095d3a33684d5bb2f569208bc2952c","requestId":"gty000b15ca@dx1956a124e007844532","sessionId":"cid000b15c9@dx1956a124d757844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741241343}
{"recordId":"gty000b15ca@dx1956a124e007844532:b9095d3a33684d5bb2f569208bc2952c","requestId":"gty000b15ca@dx1956a124e007844532","sessionId":"cid000b15c9@dx1956a124d757844532","eof":"1","text":"准备好了吗一起跟随记者进行实地探访","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741241343}
send data finished:1741241343.1251767
{"recordId":"gty000b15ca@dx1956a124e007844532:b9095d3a33684d5bb2f569208bc2952c","requestId":"gty000b15ca@dx1956a124e007844532","sessionId":"cid000b15c9@dx1956a124d757844532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"准备好了吗一起跟随记者进行实地探访","intentId":"chat","intentName":"闲聊","nlg":"我的理想，是做一名记者，造福社会。","shouldEndSession":true},"nlu":{"input":"准备好了吗一起跟随记者进行实地探访","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741241343}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid14b6078c@dx98841b2487ff3eef00"}
连接正常关闭
1741241343
param:b'{\n            "auth_id": "8f96bb0b509c4d1894c3a14b49f6ef90",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid17e0f467@dx68111b2487ff3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "a90427181f5a41578a297cdb80f1990e","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b0f98@dx1956a125ea1b8a9532"}
started:
ws start
####################
测试进行: 1.pcm_handle_1.pcm
{"recordId":"ase000d131f@hu1956a1263f004d3882:a90427181f5a41578a297cdb80f1990e","requestId":"ase000d131f@hu1956a1263f004d3882","sessionId":"cid000b0f98@dx1956a125ea1b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741241345}
{"recordId":"gty000b0f9a@dx1956a125f26b8a9532:a90427181f5a41578a297cdb80f1990e","requestId":"gty000b0f9a@dx1956a125f26b8a9532","sessionId":"cid000b0f98@dx1956a125ea1b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741241349}
{"recordId":"gty000b0f9a@dx1956a125f26b8a9532:a90427181f5a41578a297cdb80f1990e","requestId":"gty000b0f9a@dx1956a125f26b8a9532","sessionId":"cid000b0f98@dx1956a125ea1b8a9532","eof":"1","text":"车辆不得不绕行现场也被轻视围栏围起施工车辆和工人","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741241349}
send data finished:1741241349.581915
{"recordId":"gty000b0f9a@dx1956a125f26b8a9532:a90427181f5a41578a297cdb80f1990e","requestId":"gty000b0f9a@dx1956a125f26b8a9532","sessionId":"cid000b0f98@dx1956a125ea1b8a9532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"车辆不得不绕行现场也被轻视围栏围起施工车辆和工人","intentId":"chat","intentName":"闲聊","nlg":"我长大也想做一名工人。","shouldEndSession":true},"nlu":{"input":"车辆不得不绕行现场也被轻视围栏围起施工车辆和工人","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741241349}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid317138a0@dx21051b2488053eef00"}
连接正常关闭
1741241349
param:b'{\n            "auth_id": "c8ae634a6b464a3e81a64fbb10d10917",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidc15c9bf3@dx169f1b2488053eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "0fd0c7d498e34f5786d4f17eaf9486b3","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b0d82@dx1956a1277ecb8aa532"}
started:
ws start
####################
测试进行: 1.pcm_handle_10.pcm
{"recordId":"ase000ef14b@hu1956a12837505bf882:0fd0c7d498e34f5786d4f17eaf9486b3","requestId":"ase000ef14b@hu1956a12837505bf882","sessionId":"cid000b0d82@dx1956a1277ecb8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741241353}
{"recordId":"gty000b0d83@dx1956a127898b8aa532:0fd0c7d498e34f5786d4f17eaf9486b3","requestId":"gty000b0d83@dx1956a127898b8aa532","sessionId":"cid000b0d82@dx1956a1277ecb8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741241355}
{"recordId":"gty000b0d83@dx1956a127898b8aa532:0fd0c7d498e34f5786d4f17eaf9486b3","requestId":"gty000b0d83@dx1956a127898b8aa532","sessionId":"cid000b0d82@dx1956a1277ecb8aa532","eof":"1","text":"路面的还是有积水的情况存在的","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741241355}
send data finished:1741241355.8187904
{"recordId":"gty000b0d83@dx1956a127898b8aa532:0fd0c7d498e34f5786d4f17eaf9486b3","requestId":"gty000b0d83@dx1956a127898b8aa532","sessionId":"cid000b0d82@dx1956a1277ecb8aa532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"路面的还是有积水的情况存在的","intentId":"chat","intentName":"闲聊","nlg":"这样高难度的问题还是给我撞上啦，换一个简单的呗。","shouldEndSession":true},"nlu":{"input":"路面的还是有积水的情况存在的","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741241355}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid4d43c9b5@dxedd71b24880b3eef00"}
连接正常关闭
1741241356
param:b'{\n            "auth_id": "fe8abf174fab48dc8eac111678566806",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid80f49ed6@dxa1ff1b24880c3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "bd802898e40745dfb32fa93fdb92ca24","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b0fa2@dx1956a129021b8a9532"}
started:
ws start
####################
测试进行: 1.pcm_handle_11.pcm
{"recordId":"gty000b0fa3@dx1956a1290acb8a9532:bd802898e40745dfb32fa93fdb92ca24","requestId":"gty000b0fa3@dx1956a1290acb8a9532","sessionId":"cid000b0fa2@dx1956a129021b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741241357}
{"recordId":"ase000e027b@hu1956a1294751323882:bd802898e40745dfb32fa93fdb92ca24","requestId":"ase000e027b@hu1956a1294751323882","sessionId":"cid000b0fa2@dx1956a129021b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741241357}
{"recordId":"gty000b0fa3@dx1956a1290acb8a9532:bd802898e40745dfb32fa93fdb92ca24","requestId":"gty000b0fa3@dx1956a1290acb8a9532","sessionId":"cid000b0fa2@dx1956a129021b8a9532","eof":"1","text":"很清楚","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741241357}
{"recordId":"gty000b0fa3@dx1956a1290acb8a9532:bd802898e40745dfb32fa93fdb92ca24","requestId":"gty000b0fa3@dx1956a1290acb8a9532","sessionId":"cid000b0fa2@dx1956a129021b8a9532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"很清楚","intentId":"chat","intentName":"闲聊","nlg":"我都知道呀，我也很清楚了。","shouldEndSession":true},"nlu":{"input":"很清楚","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741241357}
发送结束标识
发送断开连接标识
send data finished:1741241357.7049756
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid656296f0@dx56e21b24880d3eef00"}
连接正常关闭
1741241357
param:b'{\n            "auth_id": "ef08fbf075374833adb0cf44a20217ab",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidfbefdc43@dxbc8b1b24880d3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "91d802e78dd74c1c9de9d4eef86cece8","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b15d6@dx1956a1296ba7844532"}
started:
ws start
####################
测试进行: 1.pcm_handle_12.pcm
{"recordId":"ase000d8cac@hu1956a129bc60427882:91d802e78dd74c1c9de9d4eef86cece8","requestId":"ase000d8cac@hu1956a129bc60427882","sessionId":"cid000b15d6@dx1956a1296ba7844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741241359}
{"recordId":"gty000b15d7@dx1956a1297447844532:91d802e78dd74c1c9de9d4eef86cece8","requestId":"gty000b15d7@dx1956a1297447844532","sessionId":"cid000b15d6@dx1956a1296ba7844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741241359}
{"recordId":"gty000b15d7@dx1956a1297447844532:91d802e78dd74c1c9de9d4eef86cece8","requestId":"gty000b15d7@dx1956a1297447844532","sessionId":"cid000b15d6@dx1956a1296ba7844532","eof":"1","text":"从江西省","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741241359}
send data finished:1741241359.588275
{"recordId":"gty000b15d7@dx1956a1297447844532:91d802e78dd74c1c9de9d4eef86cece8","requestId":"gty000b15d7@dx1956a1297447844532","sessionId":"cid000b15d6@dx1956a1296ba7844532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"从江西省","intentId":"chat","intentName":"闲聊","nlg":"想破了我的小脑袋都没想出来，你换个问题吧！","shouldEndSession":true},"nlu":{"input":"从江西省","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741241359}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid8f3a1b04@dx38771b24880f3eef00"}
连接正常关闭
1741241359
param:b'{\n            "auth_id": "4a991478891f404596fafbe8cffb16f6",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid3b5e1b20@dxd9501b24880f3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "100b7c301f464cf2bceaa3e748dae011","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b0d75@dx1956a129ec5b86a532"}
started:
ws start
####################
测试进行: 1.pcm_handle_13.pcm
{"recordId":"ase000f7abf@hu1956a12b9f205c2882:100b7c301f464cf2bceaa3e748dae011","requestId":"ase000f7abf@hu1956a12b9f205c2882","sessionId":"cid000b0d75@dx1956a129ec5b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741241367}
{"recordId":"ase000d1f83@hu1956a12bab204d3882:100b7c301f464cf2bceaa3e748dae011","requestId":"ase000d1f83@hu1956a12bab204d3882","sessionId":"cid000b0d75@dx1956a129ec5b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741241367}
{"recordId":"gty000b0d76@dx1956a129f50b86a532:100b7c301f464cf2bceaa3e748dae011","requestId":"gty000b0d76@dx1956a129f50b86a532","sessionId":"cid000b0d75@dx1956a129ec5b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741241374}
{"recordId":"gty000b0d76@dx1956a129f50b86a532:100b7c301f464cf2bceaa3e748dae011","requestId":"gty000b0d76@dx1956a129f50b86a532","sessionId":"cid000b0d75@dx1956a129ec5b86a532","eof":"1","text":"据当地气象预报今天广西北部和沿海部分地区决定有暴雨到大暴雨并伴有短时雷暴大风等强对流天气","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741241374}
send data finished:1741241374.5428002
{"recordId":"gty000b0d76@dx1956a129f50b86a532:100b7c301f464cf2bceaa3e748dae011","requestId":"gty000b0d76@dx1956a129f50b86a532","sessionId":"cid000b0d75@dx1956a129ec5b86a532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"据当地气象预报今天广西北部和沿海部分地区决定有暴雨到大暴雨并伴有短时雷暴大风等强对流天气","intentId":"chat","intentName":"闲聊","nlg":"想破了我的小脑袋都没想出来，你换个问题吧！","shouldEndSession":true},"nlu":{"input":"据当地气象预报今天广西北部和沿海部分地区决定有暴雨到大暴雨并伴有短时雷暴大风等强对流天气","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741241374}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid0bea4d7c@dx29f81b24881e3eef00"}
连接正常关闭
1741241374
param:b'{\n            "auth_id": "163f11b724b946a3877ae3024644e70d",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cide69434c7@dxe2a21b24881e3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "d62874a770964bae8e0ed6b3eab14834","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b0fb3@dx1956a12d993b8a9532"}
started:
ws start
####################
测试进行: 1.pcm_handle_14.pcm
{"recordId":"gty000b0fb4@dx1956a12da36b8a9532:d62874a770964bae8e0ed6b3eab14834","requestId":"gty000b0fb4@dx1956a12da36b8a9532","sessionId":"cid000b0fb3@dx1956a12d993b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741241376}
{"recordId":"gty000b0fb4@dx1956a12da36b8a9532:d62874a770964bae8e0ed6b3eab14834","requestId":"gty000b0fb4@dx1956a12da36b8a9532","sessionId":"cid000b0fb3@dx1956a12d993b8a9532","eof":"1","text":"","pinyin":"","topic":"asr.speech.result","age":"elder","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741241376}
{"recordId":"gty000b0fb4@dx1956a12da36b8a9532:d62874a770964bae8e0ed6b3eab14834","requestId":"gty000b0fb4@dx1956a12da36b8a9532","sessionId":"cid000b0fb3@dx1956a12d993b8a9532","topic":"dm.output","skill":"","skillId":"","speakUrl":"","error":{"errId":"010305","errMsg":"asr result is null"},"dm":{},"nlu":{"input":"","skill":"","skillId":""},"timestamp":1741241376}
send data finished:1741241376.291151
{"recordId":"ase000f4192@hu1956a12ddb505c4882:d62874a770964bae8e0ed6b3eab14834","requestId":"ase000f4192@hu1956a12ddb505c4882","sessionId":"cid000b0fb3@dx1956a12d993b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741241376}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidb02807d9@dxf32a1b2488203eef00"}
连接正常关闭
1741241376
param:b'{\n            "auth_id": "44057bc4ebe84986a83d037644d31f23",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid1fc1149a@dx47631b2488203eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "82ebe83b8ece4bc7912d25e1662c52dd","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b15e2@dx1956a12e0087844532"}
started:
ws start
####################
测试进行: 1.pcm_handle_15.pcm
{"recordId":"ase000d2590@hu1956a12e57104d3882:82ebe83b8ece4bc7912d25e1662c52dd","requestId":"ase000d2590@hu1956a12e57104d3882","sessionId":"cid000b15e2@dx1956a12e0087844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741241378}
{"recordId":"gty000b15e3@dx1956a12e0ac7844532:82ebe83b8ece4bc7912d25e1662c52dd","requestId":"gty000b15e3@dx1956a12e0ac7844532","sessionId":"cid000b15e2@dx1956a12e0087844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741241381}
{"recordId":"gty000b15e3@dx1956a12e0ac7844532:82ebe83b8ece4bc7912d25e1662c52dd","requestId":"gty000b15e3@dx1956a12e0ac7844532","sessionId":"cid000b15e2@dx1956a12e0087844532","eof":"1","text":"斯政治局领导人伊斯梅尔哈尼亚的多名亲属身亡","pinyin":"","topic":"asr.speech.result","age":"elder","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741241381}
send data finished:1741241381.671182
{"recordId":"gty000b15e3@dx1956a12e0ac7844532:82ebe83b8ece4bc7912d25e1662c52dd","requestId":"gty000b15e3@dx1956a12e0ac7844532","sessionId":"cid000b15e2@dx1956a12e0087844532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"斯政治局领导人伊斯梅尔哈尼亚的多名亲属身亡","intentId":"chat","intentName":"闲聊","nlg":"我还小，不知道你在说什么，我们聊点别的吧。","shouldEndSession":true},"nlu":{"input":"斯政治局领导人伊斯梅尔哈尼亚的多名亲属身亡","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741241381}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidb7e2521f@dxb8bf1b2488253eef00"}
连接正常关闭
1741241381
param:b'{\n            "auth_id": "9346000871a24df1a477494bd0a8c7b5",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid4faa316e@dxac8f1b2488263eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "7b1d74260b0e4cce895c77b3d9b6bdb4","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b0fbc@dx1956a12f53fb8a9532"}
started:
ws start
####################
测试进行: 1.pcm_handle_16.pcm
{"recordId":"ase000d99d4@hu1956a12f9a10427882:7b1d74260b0e4cce895c77b3d9b6bdb4","requestId":"ase000d99d4@hu1956a12f9a10427882","sessionId":"cid000b0fbc@dx1956a12f53fb8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741241383}
{"recordId":"gty000b0fbd@dx1956a12f5d9b8a9532:7b1d74260b0e4cce895c77b3d9b6bdb4","requestId":"gty000b0fbd@dx1956a12f5d9b8a9532","sessionId":"cid000b0fbc@dx1956a12f53fb8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741241388}
{"recordId":"gty000b0fbd@dx1956a12f5d9b8a9532:7b1d74260b0e4cce895c77b3d9b6bdb4","requestId":"gty000b0fbd@dx1956a12f5d9b8a9532","sessionId":"cid000b0fbc@dx1956a12f53fb8a9532","eof":"1","text":"的活动利用校园为其掩护曾参与策划多起针对以色列的袭击","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741241388}
send data finished:1741241388.5195754
{"recordId":"gty000b0fbd@dx1956a12f5d9b8a9532:7b1d74260b0e4cce895c77b3d9b6bdb4","requestId":"gty000b0fbd@dx1956a12f5d9b8a9532","sessionId":"cid000b0fbc@dx1956a12f53fb8a9532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"的活动利用校园为其掩护曾参与策划多起针对以色列的袭击","intentId":"chat","intentName":"闲聊","nlg":"这样高难度的问题还是给我撞上啦，换一个简单的呗。","shouldEndSession":true},"nlu":{"input":"的活动利用校园为其掩护曾参与策划多起针对以色列的袭击","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741241388}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cida9c4152f@dx53421b24882c3eef00"}
连接正常关闭
1741241388
param:b'{\n            "auth_id": "f4a736a2683e4d95ae918814c9e8b5e8",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidb6769b67@dx6e901b24882c3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "20624021b04343888102c5a254d84d9d","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b15f0@dx1956a130fe47844532"}
started:
ws start
####################
测试进行: 1.pcm_handle_17.pcm
{"recordId":"ase000f7a99@hu1956a13178e05c0882:20624021b04343888102c5a254d84d9d","requestId":"ase000f7a99@hu1956a13178e05c0882","sessionId":"cid000b15f0@dx1956a130fe47844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741241391}
{"recordId":"ase000d2cfe@hu1956a13189904d3882:20624021b04343888102c5a254d84d9d","requestId":"ase000d2cfe@hu1956a13189904d3882","sessionId":"cid000b15f0@dx1956a130fe47844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741241391}
{"recordId":"gty000b15f1@dx1956a13109e7844532:20624021b04343888102c5a254d84d9d","requestId":"gty000b15f1@dx1956a13109e7844532","sessionId":"cid000b15f0@dx1956a130fe47844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741241394}
{"recordId":"gty000b15f1@dx1956a13109e7844532:20624021b04343888102c5a254d84d9d","requestId":"gty000b15f1@dx1956a13109e7844532","sessionId":"cid000b15f0@dx1956a130fe47844532","eof":"1","text":"一切现在马吉德和家人在一所学校中临时避难","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741241394}
send data finished:1741241394.306441
{"recordId":"gty000b15f1@dx1956a13109e7844532:20624021b04343888102c5a254d84d9d","requestId":"gty000b15f1@dx1956a13109e7844532","sessionId":"cid000b15f0@dx1956a130fe47844532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"一切现在马吉德和家人在一所学校中临时避难","intentId":"chat","intentName":"闲聊","nlg":"咱们聊聊生活上的事情怎么样？","shouldEndSession":true},"nlu":{"input":"一切现在马吉德和家人在一所学校中临时避难","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741241394}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid853e37e9@dx5a361b2488323eef00"}
连接正常关闭
1741241394
param:b'{\n            "auth_id": "4f309d4ce48341c683a53a1b1a51d3a0",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidc8512d80@dxaa4b1b2488323eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "736fb2501cf646d5992e12e6d522d46d","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b15f6@dx1956a1326837844532"}
started:
ws start
####################
测试进行: 1.pcm_handle_18.pcm
{"recordId":"gty000b15f7@dx1956a13273a7844532:736fb2501cf646d5992e12e6d522d46d","requestId":"gty000b15f7@dx1956a13273a7844532","sessionId":"cid000b15f6@dx1956a1326837844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741241396}
{"recordId":"gty000b15f7@dx1956a13273a7844532:736fb2501cf646d5992e12e6d522d46d","requestId":"gty000b15f7@dx1956a13273a7844532","sessionId":"cid000b15f6@dx1956a1326837844532","eof":"1","text":"在艰难度日","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741241396}
send data finished:1741241396.805415
{"recordId":"ase000e1870@hu1956a132d8f1323882:736fb2501cf646d5992e12e6d522d46d","requestId":"ase000e1870@hu1956a132d8f1323882","sessionId":"cid000b15f6@dx1956a1326837844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741241396}
{"recordId":"gty000b15f7@dx1956a13273a7844532:736fb2501cf646d5992e12e6d522d46d","requestId":"gty000b15f7@dx1956a13273a7844532","sessionId":"cid000b15f6@dx1956a1326837844532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"在艰难度日","intentId":"chat","intentName":"闲聊","nlg":"你成功难住我啦，换个简单的问我吧。","shouldEndSession":true},"nlu":{"input":"在艰难度日","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741241396}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid314d65b2@dx3a451b2488353eef00"}
连接正常关闭
1741241397
param:b'{\n            "auth_id": "08973297ff6848a2a024f9e00792b5f7",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid7a9f0b1f@dxe48d1b2488353eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "e13999a01eb74cf09d99146756246100","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b0fc7@dx1956a133089b8a9532"}
started:
ws start
####################
测试进行: 1.pcm_handle_19.pcm
{"recordId":"ase000da429@hu1956a133e100427882:e13999a01eb74cf09d99146756246100","requestId":"ase000da429@hu1956a133e100427882","sessionId":"cid000b0fc7@dx1956a133089b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741241400}
{"recordId":"gty000b0fc9@dx1956a133149b8a9532:e13999a01eb74cf09d99146756246100","requestId":"gty000b0fc9@dx1956a133149b8a9532","sessionId":"cid000b0fc7@dx1956a133089b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741241401}
{"recordId":"gty000b0fc9@dx1956a133149b8a9532:e13999a01eb74cf09d99146756246100","requestId":"gty000b0fc9@dx1956a133149b8a9532","sessionId":"cid000b0fc7@dx1956a133089b8a9532","eof":"1","text":"","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741241401}
{"recordId":"gty000b0fc9@dx1956a133149b8a9532:e13999a01eb74cf09d99146756246100","requestId":"gty000b0fc9@dx1956a133149b8a9532","sessionId":"cid000b0fc7@dx1956a133089b8a9532","topic":"dm.output","skill":"","skillId":"","speakUrl":"","error":{"errId":"010305","errMsg":"asr result is null"},"dm":{},"nlu":{"input":"","skill":"","skillId":""},"timestamp":1741241401}
发送结束标识
发送断开连接标识
send data finished:1741241401.1228175
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid15cf8c77@dxc8791b2488393eef00"}
连接正常关闭
1741241401
param:b'{\n            "auth_id": "f7c2e73d5fa5422487e62663951f3fb9",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid1416b2d0@dx26b41b2488393eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "38b2d8c222504e36931834139d21d11a","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b0db2@dx1956a134041b8aa532"}
started:
ws start
####################
测试进行: 1.pcm_handle_2.pcm
{"recordId":"ase000e0dae@hu1956a1347cc05bf882:38b2d8c222504e36931834139d21d11a","requestId":"ase000e0dae@hu1956a1347cc05bf882","sessionId":"cid000b0db2@dx1956a134041b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741241403}
{"recordId":"gty000b0db3@dx1956a1340ffb8aa532:38b2d8c222504e36931834139d21d11a","requestId":"gty000b0db3@dx1956a1340ffb8aa532","sessionId":"cid000b0db2@dx1956a134041b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741241404}
{"recordId":"gty000b0db3@dx1956a1340ffb8aa532:38b2d8c222504e36931834139d21d11a","requestId":"gty000b0db3@dx1956a1340ffb8aa532","sessionId":"cid000b0db2@dx1956a134041b8aa532","eof":"1","text":"该区域将被移交给政府","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741241404}
send data finished:1741241404.2759109
{"recordId":"gty000b0db3@dx1956a1340ffb8aa532:38b2d8c222504e36931834139d21d11a","requestId":"gty000b0db3@dx1956a1340ffb8aa532","sessionId":"cid000b0db2@dx1956a134041b8aa532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"该区域将被移交给政府","intentId":"chat","intentName":"闲聊","nlg":"太难了，不知道你在说什么","shouldEndSession":true},"nlu":{"input":"该区域将被移交给政府","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741241404}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid5d993db3@dxdbe01b24883c3eef00"}
连接正常关闭
1741241404
param:b'{\n            "auth_id": "4cf6afe1e5fb43b38e56ba7545801520",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid8d6a7ba1@dx09691b24883c3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "ac3043438f7c4799a6ddffa3b10bdb99","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b0db8@dx1956a134d47b8aa532"}
started:
ws start
####################
测试进行: 1.pcm_handle_20.pcm
{"recordId":"ase000ee581@hu1956a13549e05c3882:ac3043438f7c4799a6ddffa3b10bdb99","requestId":"ase000ee581@hu1956a13549e05c3882","sessionId":"cid000b0db8@dx1956a134d47b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741241406}
{"recordId":"gty000b0db9@dx1956a134df7b8aa532:ac3043438f7c4799a6ddffa3b10bdb99","requestId":"gty000b0db9@dx1956a134df7b8aa532","sessionId":"cid000b0db8@dx1956a134d47b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741241408}
{"recordId":"gty000b0db9@dx1956a134df7b8aa532:ac3043438f7c4799a6ddffa3b10bdb99","requestId":"gty000b0db9@dx1956a134df7b8aa532","sessionId":"cid000b0db8@dx1956a134d47b8aa532","eof":"1","text":"不要把整个中医地区","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741241408}
send data finished:1741241408.5059013
{"recordId":"gty000b0db9@dx1956a134df7b8aa532:ac3043438f7c4799a6ddffa3b10bdb99","requestId":"gty000b0db9@dx1956a134df7b8aa532","sessionId":"cid000b0db8@dx1956a134d47b8aa532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"不要把整个中医地区","intentId":"chat","intentName":"闲聊","nlg":"这样高难度的问题还是给我撞上啦，换一个简单的呗。","shouldEndSession":true},"nlu":{"input":"不要把整个中医地区","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741241408}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cida219c179@dx3a101b2488403eef00"}
连接正常关闭
1741241408
param:b'{\n            "auth_id": "d83d192e36cf42f284af8029b6f112b2",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidf01d7b94@dx54d71b2488403eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "4f82dca9d5ea4657a18314ff88c8dee8","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b0dba@dx1956a135dc1b8aa532"}
started:
ws start
####################
测试进行: 1.pcm_handle_21.pcm
{"recordId":"gty000b0dbb@dx1956a135e7cb8aa532:4f82dca9d5ea4657a18314ff88c8dee8","requestId":"gty000b0dbb@dx1956a135e7cb8aa532","sessionId":"cid000b0dba@dx1956a135dc1b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741241412}
{"recordId":"gty000b0dbb@dx1956a135e7cb8aa532:4f82dca9d5ea4657a18314ff88c8dee8","requestId":"gty000b0dbb@dx1956a135e7cb8aa532","sessionId":"cid000b0dba@dx1956a135dc1b8aa532","eof":"1","text":"领导在出发","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741241412}
send data finished:1741241412.8984118
{"recordId":"ase000daaa8@hu1956a136ca40427882:4f82dca9d5ea4657a18314ff88c8dee8","requestId":"ase000daaa8@hu1956a136ca40427882","sessionId":"cid000b0dba@dx1956a135dc1b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741241412}
{"recordId":"gty000b0dbb@dx1956a135e7cb8aa532:4f82dca9d5ea4657a18314ff88c8dee8","requestId":"gty000b0dbb@dx1956a135e7cb8aa532","sessionId":"cid000b0dba@dx1956a135dc1b8aa532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"领导在出发","intentId":"chat","intentName":"闲聊","nlg":"这样高难度的问题还是给我撞上啦，换一个简单的呗。","shouldEndSession":true},"nlu":{"input":"领导在出发","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741241412}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid543f3d74@dxdf8c1b2488453eef00"}
连接正常关闭
1741241413
param:b'{\n            "auth_id": "9f77a21fc749467f96f73067ac4e80a9",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid73155d24@dx5ad81b2488453eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "dc768fac779d404db5bae851f93441c5","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b0d94@dx1956a136ecdb86a532"}
started:
ws start
####################
测试进行: 1.pcm_handle_22.pcm
{"recordId":"gty000b0d95@dx1956a136f76b86a532:dc768fac779d404db5bae851f93441c5","requestId":"gty000b0d95@dx1956a136f76b86a532","sessionId":"cid000b0d94@dx1956a136ecdb86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741241414}
{"recordId":"ase000dab6e@hu1956a1372980427882:dc768fac779d404db5bae851f93441c5","requestId":"ase000dab6e@hu1956a1372980427882","sessionId":"cid000b0d94@dx1956a136ecdb86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741241414}
{"recordId":"gty000b0d95@dx1956a136f76b86a532:dc768fac779d404db5bae851f93441c5","requestId":"gty000b0d95@dx1956a136f76b86a532","sessionId":"cid000b0d94@dx1956a136ecdb86a532","eof":"1","text":"","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741241414}
{"recordId":"gty000b0d95@dx1956a136f76b86a532:dc768fac779d404db5bae851f93441c5","requestId":"gty000b0d95@dx1956a136f76b86a532","sessionId":"cid000b0d94@dx1956a136ecdb86a532","topic":"dm.output","skill":"","skillId":"","speakUrl":"","error":{"errId":"010305","errMsg":"asr result is null"},"dm":{},"nlu":{"input":"","skill":"","skillId":""},"timestamp":1741241414}
发送结束标识
发送断开连接标识
send data finished:1741241414.5862935
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidb34d333e@dx716a1b2488463eef00"}
连接正常关闭
1741241414
param:b'{\n            "auth_id": "e0dd6dbe66fd4c62a1c46cdf08226ebe",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid2cebd17a@dx806e1b2488463eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "e04ec1a146404a89872295d6cef321c4","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b1606@dx1956a1374e67844532"}
started:
ws start
####################
测试进行: 1.pcm_handle_23.pcm
{"recordId":"ase000d3c03@hu1956a137dc704d3882:e04ec1a146404a89872295d6cef321c4","requestId":"ase000d3c03@hu1956a137dc704d3882","sessionId":"cid000b1606@dx1956a1374e67844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741241417}
{"recordId":"gty000b1608@dx1956a1375977844532:e04ec1a146404a89872295d6cef321c4","requestId":"gty000b1608@dx1956a1375977844532","sessionId":"cid000b1606@dx1956a1374e67844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741241420}
{"recordId":"gty000b1608@dx1956a1375977844532:e04ec1a146404a89872295d6cef321c4","requestId":"gty000b1608@dx1956a1375977844532","sessionId":"cid000b1606@dx1956a1374e67844532","eof":"1","text":"对持有就诊方便卡的人员医疗机构应当优先整治","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741241420}
send data finished:1741241420.6335258
{"recordId":"gty000b1608@dx1956a1375977844532:e04ec1a146404a89872295d6cef321c4","requestId":"gty000b1608@dx1956a1375977844532","sessionId":"cid000b1606@dx1956a1374e67844532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"对持有就诊方便卡的人员医疗机构应当优先整治","intentId":"chat","intentName":"闲聊","nlg":"这样高难度的问题还是给我撞上啦，换一个简单的呗。","shouldEndSession":true},"nlu":{"input":"对持有就诊方便卡的人员医疗机构应当优先整治","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741241420}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid0c4cb6af@dxbaea1b24884c3eef00"}
连接正常关闭
1741241420
param:b'{\n            "auth_id": "c5d2c44fc6fe4156846648ebbddc9acc",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid54996d6f@dxc05a1b24884c3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "f816bef0d36840868045f14fa4160ca4","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b0d9a@dx1956a138d57b86a532"}
started:
ws start
####################
测试进行: 1.pcm_handle_24.pcm
{"recordId":"gty000b0d9b@dx1956a138e05b86a532:f816bef0d36840868045f14fa4160ca4","requestId":"gty000b0d9b@dx1956a138e05b86a532","sessionId":"cid000b0d9a@dx1956a138d57b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741241422}
{"recordId":"gty000b0d9b@dx1956a138e05b86a532:f816bef0d36840868045f14fa4160ca4","requestId":"gty000b0d9b@dx1956a138e05b86a532","sessionId":"cid000b0d9a@dx1956a138d57b86a532","eof":"1","text":"","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741241422}
{"recordId":"gty000b0d9b@dx1956a138e05b86a532:f816bef0d36840868045f14fa4160ca4","requestId":"gty000b0d9b@dx1956a138e05b86a532","sessionId":"cid000b0d9a@dx1956a138d57b86a532","topic":"dm.output","skill":"","skillId":"","speakUrl":"","error":{"errId":"010305","errMsg":"asr result is null"},"dm":{},"nlu":{"input":"","skill":"","skillId":""},"timestamp":1741241422}
send data finished:1741241422.3293264
{"recordId":"ase000eee1c@hu1956a1391e805c3882:f816bef0d36840868045f14fa4160ca4","requestId":"ase000eee1c@hu1956a1391e805c3882","sessionId":"cid000b0d9a@dx1956a138d57b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741241422}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidae704d89@dx83dc1b24884e3eef00"}
连接正常关闭
1741241422
param:b'{\n            "auth_id": "72eee244c37142368d65178e67210e22",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid650a02c3@dxd7c71b24884e3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "7c3d0f5e5323453da37ddd4553a85d61","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b1612@dx1956a1394107844532"}
started:
ws start
####################
测试进行: 1.pcm_handle_25.pcm
{"recordId":"ase000f5d99@hu1956a139b3d05c4882:7c3d0f5e5323453da37ddd4553a85d61","requestId":"ase000f5d99@hu1956a139b3d05c4882","sessionId":"cid000b1612@dx1956a1394107844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741241424}
{"recordId":"gty000b1613@dx1956a1394c37844532:7c3d0f5e5323453da37ddd4553a85d61","requestId":"gty000b1613@dx1956a1394c37844532","sessionId":"cid000b1612@dx1956a1394107844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741241431}
{"recordId":"gty000b1613@dx1956a1394c37844532:7c3d0f5e5323453da37ddd4553a85d61","requestId":"gty000b1613@dx1956a1394c37844532","sessionId":"cid000b1612@dx1956a1394107844532","eof":"1","text":"其中实物商品网上零售额占社会消费品零售总额比重为24.7:1~4%月","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741241431}
send data finished:1741241432.0391443
{"recordId":"gty000b1613@dx1956a1394c37844532:7c3d0f5e5323453da37ddd4553a85d61","requestId":"gty000b1613@dx1956a1394c37844532","sessionId":"cid000b1612@dx1956a1394107844532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"其中实物商品网上零售额占社会消费品零售总额比重为24.7:1~4%月","intentId":"chat","intentName":"闲聊","nlg":"这样高难度的问题还是给我撞上啦，换一个简单的呗。","shouldEndSession":true},"nlu":{"input":"其中实物商品网上零售额占社会消费品零售总额比重为24.7:1~4%月","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741241432}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid5c8abce0@dx0bc61b2488583eef00"}
连接正常关闭
1741241432
param:b'{\n            "auth_id": "0da7efc040884a2ab53c13a53a68ee7a",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidf1900dc5@dx9c211b2488583eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "fecd3be0124a42529589ebe9f465060e","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b161c@dx1956a13ba067844532"}
started:
ws start
####################
测试进行: 1.pcm_handle_26.pcm
{"recordId":"ase000e1f39@hu1956a13c0fe05bf882:fecd3be0124a42529589ebe9f465060e","requestId":"ase000e1f39@hu1956a13c0fe05bf882","sessionId":"cid000b161c@dx1956a13ba067844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741241434}
{"recordId":"gty000b161d@dx1956a13babc7844532:fecd3be0124a42529589ebe9f465060e","requestId":"gty000b161d@dx1956a13babc7844532","sessionId":"cid000b161c@dx1956a13ba067844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741241440}
{"recordId":"gty000b161d@dx1956a13babc7844532:fecd3be0124a42529589ebe9f465060e","requestId":"gty000b161d@dx1956a13babc7844532","sessionId":"cid000b161c@dx1956a13ba067844532","eof":"1","text":"光华工程科技奖昨天在中国工程院第17次院士大会上揭晓共有41位专家获奖","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741241440}
send data finished:1741241440.8370807
{"recordId":"gty000b161d@dx1956a13babc7844532:fecd3be0124a42529589ebe9f465060e","requestId":"gty000b161d@dx1956a13babc7844532","sessionId":"cid000b161c@dx1956a13ba067844532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"光华工程科技奖昨天在中国工程院第17次院士大会上揭晓共有41位专家获奖","intentId":"chat","intentName":"闲聊","nlg":"你成功难住我啦，换个简单的问我吧。","shouldEndSession":true},"nlu":{"input":"光华工程科技奖昨天在中国工程院第17次院士大会上揭晓共有41位专家获奖","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741241440}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid09d54db7@dxa0071b2488613eef00"}
连接正常关闭
1741241441
param:b'{\n            "auth_id": "2f5525de5a714f42be288bd4abfacbe3",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidbcb141f8@dx29281b2488613eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "1f9616259da74475835bc5dde56744df","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b0da8@dx1956a13dc5cb86a532"}
started:
ws start
####################
测试进行: 1.pcm_handle_27.pcm
{"recordId":"ase000e3321@hu1956a13e36d1323882:1f9616259da74475835bc5dde56744df","requestId":"ase000e3321@hu1956a13e36d1323882","sessionId":"cid000b0da8@dx1956a13dc5cb86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741241443}
{"recordId":"gty000b0da9@dx1956a13dd0bb86a532:1f9616259da74475835bc5dde56744df","requestId":"gty000b0da9@dx1956a13dd0bb86a532","sessionId":"cid000b0da8@dx1956a13dc5cb86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741241444}
{"recordId":"gty000b0da9@dx1956a13dd0bb86a532:1f9616259da74475835bc5dde56744df","requestId":"gty000b0da9@dx1956a13dd0bb86a532","sessionId":"cid000b0da8@dx1956a13dc5cb86a532","eof":"1","text":"此次俄乌双方交换被扣押人员","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741241444}
send data finished:1741241444.3021204
{"recordId":"gty000b0da9@dx1956a13dd0bb86a532:1f9616259da74475835bc5dde56744df","requestId":"gty000b0da9@dx1956a13dd0bb86a532","sessionId":"cid000b0da8@dx1956a13dc5cb86a532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"此次俄乌双方交换被扣押人员","intentId":"chat","intentName":"闲聊","nlg":"咱们聊聊生活上的事情怎么样？","shouldEndSession":true},"nlu":{"input":"此次俄乌双方交换被扣押人员","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741241444}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid3de0a0e0@dx873c1b2488643eef00"}
连接正常关闭
1741241444
param:b'{\n            "auth_id": "61e309e9fe384c6aa1f25bcd5d83186e",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid7050cb84@dx17b51b2488643eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "24a5e74340ca4e13b5243d2de7f1258c","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b0fef@dx1956a13e9e8b8a9532"}
started:
ws start
####################
测试进行: 1.pcm_handle_28.pcm
{"recordId":"ase000f6968@hu1956a13ef6b05c4882:24a5e74340ca4e13b5243d2de7f1258c","requestId":"ase000f6968@hu1956a13ef6b05c4882","sessionId":"cid000b0fef@dx1956a13e9e8b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741241446}
{"recordId":"gty000b0ff2@dx1956a13eaa8b8a9532:24a5e74340ca4e13b5243d2de7f1258c","requestId":"gty000b0ff2@dx1956a13eaa8b8a9532","sessionId":"cid000b0fef@dx1956a13e9e8b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741241448}
{"recordId":"gty000b0ff2@dx1956a13eaa8b8a9532:24a5e74340ca4e13b5243d2de7f1258c","requestId":"gty000b0ff2@dx1956a13eaa8b8a9532","sessionId":"cid000b0fef@dx1956a13e9e8b8a9532","eof":"1","text":"","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741241448}
{"recordId":"gty000b0ff2@dx1956a13eaa8b8a9532:24a5e74340ca4e13b5243d2de7f1258c","requestId":"gty000b0ff2@dx1956a13eaa8b8a9532","sessionId":"cid000b0fef@dx1956a13e9e8b8a9532","topic":"dm.output","skill":"","skillId":"","speakUrl":"","error":{"errId":"010305","errMsg":"asr result is null"},"dm":{},"nlu":{"input":"","skill":"","skillId":""},"timestamp":1741241448}
发送结束标识
发送断开连接标识
send data finished:1741241448.3763065
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid46fe705d@dxfbd21b2488683eef00"}
连接正常关闭
1741241448
param:b'{\n            "auth_id": "7f770c15a7504d84ad643d6d718def20",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidfd4ea35f@dx58701b2488683eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "d61f638230ec4effa062d7863a1d153d","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b0dce@dx1956a13f8eeb8aa532"}
started:
ws start
####################
测试进行: 1.pcm_handle_29.pcm
{"recordId":"gty000b0dcf@dx1956a13f9a9b8aa532:d61f638230ec4effa062d7863a1d153d","requestId":"gty000b0dcf@dx1956a13f9a9b8aa532","sessionId":"cid000b0dce@dx1956a13f8eeb8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741241450}
{"recordId":"ase000faad5@hu1956a14007a05c2882:d61f638230ec4effa062d7863a1d153d","requestId":"ase000faad5@hu1956a14007a05c2882","sessionId":"cid000b0dce@dx1956a13f8eeb8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741241450}
{"recordId":"gty000b0dcf@dx1956a13f9a9b8aa532:d61f638230ec4effa062d7863a1d153d","requestId":"gty000b0dcf@dx1956a13f9a9b8aa532","sessionId":"cid000b0dce@dx1956a13f8eeb8aa532","eof":"1","text":"","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741241450}
{"recordId":"gty000b0dcf@dx1956a13f9a9b8aa532:d61f638230ec4effa062d7863a1d153d","requestId":"gty000b0dcf@dx1956a13f9a9b8aa532","sessionId":"cid000b0dce@dx1956a13f8eeb8aa532","topic":"dm.output","skill":"","skillId":"","speakUrl":"","error":{"errId":"010305","errMsg":"asr result is null"},"dm":{},"nlu":{"input":"","skill":"","skillId":""},"timestamp":1741241450}
发送结束标识
发送断开连接标识
send data finished:1741241450.7936459
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidba37d7f4@dx70b01b24886a3eef00"}
连接正常关闭
1741241450
param:b'{\n            "auth_id": "da86ac91710f4fc588f5a62951744ab5",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidb7bcc130@dx76a41b24886a3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "ad6a9455892c44698ea1bef9fc83000a","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b0ff8@dx1956a14024bb8a9532"}
started:
ws start
####################
测试进行: 1.pcm_handle_3.pcm
{"recordId":"ase000e296a@hu1956a14090c05bf882:ad6a9455892c44698ea1bef9fc83000a","requestId":"ase000e296a@hu1956a14090c05bf882","sessionId":"cid000b0ff8@dx1956a14024bb8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741241452}
{"recordId":"gty000b0ffa@dx1956a140305b8a9532:ad6a9455892c44698ea1bef9fc83000a","requestId":"gty000b0ffa@dx1956a140305b8a9532","sessionId":"cid000b0ff8@dx1956a14024bb8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741241459}
{"recordId":"gty000b0ffa@dx1956a140305b8a9532:ad6a9455892c44698ea1bef9fc83000a","requestId":"gty000b0ffa@dx1956a140305b8a9532","sessionId":"cid000b0ff8@dx1956a14024bb8a9532","eof":"1","text":"毫米和418.3毫米那今明两天副热带高压总体比较稳定西南暖湿气流持续的提供水汽","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741241459}
send data finished:1741241459.8105972
{"recordId":"gty000b0ffa@dx1956a140305b8a9532:ad6a9455892c44698ea1bef9fc83000a","requestId":"gty000b0ffa@dx1956a140305b8a9532","sessionId":"cid000b0ff8@dx1956a14024bb8a9532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"毫米和418.3毫米那今明两天副热带高压总体比较稳定西南暖湿气流持续的提供水汽","intentId":"chat","intentName":"闲聊","nlg":"你这么说，好像我也不知道那是什么呢。","shouldEndSession":true},"nlu":{"input":"毫米和418.3毫米那今明两天副热带高压总体比较稳定西南暖湿气流持续的提供水汽","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741241459}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid6ee376ef@dx70bc1b2488743eef00"}
连接正常关闭
1741241460
param:b'{\n            "auth_id": "7c7787e238494820b33fdc08b3da8004",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid1b83cde5@dxab191b2488743eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "c63b58d96dd641858a628d9e70c33ce0","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b0db5@dx1956a142677b86a532"}
started:
ws start
####################
测试进行: 1.pcm_handle_30.pcm
Error reading file[Errno 2] No such file or directory: './data/0224两分钟噪声180个/新闻64h/20240626075117\\1.pcm_handle_9.p1.pcm_handle_30.pcm'
1741241520
param:b'{\n            "auth_id": "70181506addb497d8cb7927530806b92",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidef533d28@dx7eb71b2488b03eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "2ee858bb60c249418f5266ac424d9463","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b0df1@dx1956a1511a0b8aa532"}
started:
ws start
####################
测试进行: 1.pcm_handle_4.pcm
{"recordId":"ase000dea1d@hu1956a151b680427882:2ee858bb60c249418f5266ac424d9463","requestId":"ase000dea1d@hu1956a151b680427882","sessionId":"cid000b0df1@dx1956a1511a0b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741241523}
{"recordId":"gty000b0df2@dx1956a151255b8aa532:2ee858bb60c249418f5266ac424d9463","requestId":"gty000b0df2@dx1956a151255b8aa532","sessionId":"cid000b0df1@dx1956a1511a0b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741241524}
{"recordId":"gty000b0df2@dx1956a151255b8aa532:2ee858bb60c249418f5266ac424d9463","requestId":"gty000b0df2@dx1956a151255b8aa532","sessionId":"cid000b0df1@dx1956a1511a0b8aa532","eof":"1","text":"风量转为二档","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741241524}
send data finished:1741241524.3545086
{"recordId":"gty000b0df2@dx1956a151255b8aa532:2ee858bb60c249418f5266ac424d9463","requestId":"gty000b0df2@dx1956a151255b8aa532","sessionId":"cid000b0df1@dx1956a1511a0b8aa532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"风量转为二档","intentId":"chat","intentName":"闲聊","nlg":"你成功难住我啦，换个简单的问我吧。","shouldEndSession":true},"nlu":{"input":"风量转为二档","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741241524}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidad808417@dx93e21b2488b43eef00"}
连接正常关闭
1741241524
param:b'{\n            "auth_id": "f726283be8f144b1afe8ae4968355e17",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidb67cf9fe@dxd6ec1b2488b43eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "db4af3ccc5f64c149ab478bf4a657e00","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b0df7@dx1956a152268b8aa532"}
started:
ws start
####################
测试进行: 1.pcm_handle_5.pcm
{"recordId":"gty000b0df8@dx1956a152315b8aa532:db4af3ccc5f64c149ab478bf4a657e00","requestId":"gty000b0df8@dx1956a152315b8aa532","sessionId":"cid000b0df7@dx1956a152268b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741241526}
{"recordId":"gty000b0df8@dx1956a152315b8aa532:db4af3ccc5f64c149ab478bf4a657e00","requestId":"gty000b0df8@dx1956a152315b8aa532","sessionId":"cid000b0df7@dx1956a152268b8aa532","eof":"1","text":"取暖器eu","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741241526}
send data finished:1741241526.9770248
{"recordId":"gty000b0df8@dx1956a152315b8aa532:db4af3ccc5f64c149ab478bf4a657e00","requestId":"gty000b0df8@dx1956a152315b8aa532","sessionId":"cid000b0df7@dx1956a152268b8aa532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"取暖器eu","intentId":"chat","intentName":"闲聊","nlg":"这个我真不懂，换个我懂的呗。","shouldEndSession":true},"nlu":{"input":"取暖器eu","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741241527}
{"recordId":"ase000dec7a@hu1956a152a880427882:db4af3ccc5f64c149ab478bf4a657e00","requestId":"ase000dec7a@hu1956a152a880427882","sessionId":"cid000b0df7@dx1956a152268b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741241527}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid26800420@dx62361b2488b73eef00"}
连接正常关闭
1741241527
param:b'{\n            "auth_id": "cc149a3dd121419d9a45f7f1b22593ab",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid05ac2613@dxdc401b2488b73eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "8f68abc0cc554b0c9b2056de93e9d2f2","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b1651@dx1956a152caa7844532"}
started:
ws start
####################
测试进行: 1.pcm_handle_6.pcm
{"recordId":"ase000e2bf8@hu1956a15336305c3882:8f68abc0cc554b0c9b2056de93e9d2f2","requestId":"ase000e2bf8@hu1956a15336305c3882","sessionId":"cid000b1651@dx1956a152caa7844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741241529}
{"recordId":"gty000b1652@dx1956a152d5b7844532:8f68abc0cc554b0c9b2056de93e9d2f2","requestId":"gty000b1652@dx1956a152d5b7844532","sessionId":"cid000b1651@dx1956a152caa7844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741241532}
{"recordId":"gty000b1652@dx1956a152d5b7844532:8f68abc0cc554b0c9b2056de93e9d2f2","requestId":"gty000b1652@dx1956a152d5b7844532","sessionId":"cid000b1651@dx1956a152caa7844532","eof":"1","text":"返回器安全着陆实现世界首次月球背面采样返回","pinyin":"","topic":"asr.speech.result","age":"elder","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741241532}
send data finished:1741241533.061566
{"recordId":"gty000b1652@dx1956a152d5b7844532:8f68abc0cc554b0c9b2056de93e9d2f2","requestId":"gty000b1652@dx1956a152d5b7844532","sessionId":"cid000b1651@dx1956a152caa7844532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"返回器安全着陆实现世界首次月球背面采样返回","intentId":"chat","intentName":"闲聊","nlg":"这个我真不懂，换个我懂的呗。","shouldEndSession":true},"nlu":{"input":"返回器安全着陆实现世界首次月球背面采样返回","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741241533}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cide980b9bd@dx47531b2488bd3eef00"}
连接正常关闭
1741241533
param:b'{\n            "auth_id": "50f2a1fd71ee4d399b9153a10f7b097d",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid453fe1ed@dx0ea41b2488bd3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "696b08e7c0114df19ae6d9ecab6e9c00","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b1035@dx1956a15447fb8a9532"}
started:
ws start
####################
测试进行: 1.pcm_handle_7.pcm
{"recordId":"ase000df16a@hu1956a154a700427882:696b08e7c0114df19ae6d9ecab6e9c00","requestId":"ase000df16a@hu1956a154a700427882","sessionId":"cid000b1035@dx1956a15447fb8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741241535}
{"recordId":"gty000b1036@dx1956a154534b8a9532:696b08e7c0114df19ae6d9ecab6e9c00","requestId":"gty000b1036@dx1956a154534b8a9532","sessionId":"cid000b1035@dx1956a15447fb8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741241535}
{"recordId":"gty000b1036@dx1956a154534b8a9532:696b08e7c0114df19ae6d9ecab6e9c00","requestId":"gty000b1036@dx1956a154534b8a9532","sessionId":"cid000b1035@dx1956a15447fb8a9532","eof":"1","text":"广西北部的天气","pinyin":"","topic":"asr.speech.result","age":"elder","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741241535}
send data finished:1741241535.3169293
{"recordId":"gty000b1036@dx1956a154534b8a9532:696b08e7c0114df19ae6d9ecab6e9c00","requestId":"gty000b1036@dx1956a154534b8a9532","sessionId":"cid000b1035@dx1956a15447fb8a9532","topic":"dm.output","skill":"天气","skillId":"2019042500000544","speakUrl":"","error":{},"dm":{"input":"广西北部的天气","intentId":"QUERY","intentName":"查询天气信息","nlg":"请问你想查询广西哪个城市的天气？","widget":{"cityName":""},"dm_intent":"custom","cityName":"","shouldEndSession":false},"nlu":{"input":"广西北部的天气","skill":"天气","skillId":"2019042500000544","skillVersion":"56","semantics":{"request":{"intent":"QUERY","slots":[{"name":"datetime","normValue":"{\"datetime\":\"2025-03-06\",\"suggestDatetime\":\"2025-03-06\"}","value":"2025-03-06"},{"name":"location.province","normValue":"广西壮族自治区","value":"广西壮族自治区"},{"name":"location.provinceAddr","normValue":"广西","value":"广西"},{"name":"location.type","normValue":"LOC_BASIC","value":"LOC_BASIC"},{"name":"queryType","value":"内容"},{"name":"subfocus","value":"天气状况"}]}}},"timestamp":1741241535}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidb397946d@dxdfcb1b2488bf3eef00"}
连接正常关闭
1741241535
param:b'{\n            "auth_id": "333c5b5997b443ca910478ae245943f4",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid77422ec9@dx6ddc1b2488bf3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "a4b4c0dc16864622a8bc339506cf6369","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b0dea@dx1956a154ca1b86a532"}
started:
ws start
####################
测试进行: 1.pcm_handle_8.pcm
{"recordId":"ase000d81b3@hu1956a15542404d3882:a4b4c0dc16864622a8bc339506cf6369","requestId":"ase000d81b3@hu1956a15542404d3882","sessionId":"cid000b0dea@dx1956a154ca1b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741241537}
{"recordId":"gty000b0deb@dx1956a154d5cb86a532:a4b4c0dc16864622a8bc339506cf6369","requestId":"gty000b0deb@dx1956a154d5cb86a532","sessionId":"cid000b0dea@dx1956a154ca1b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741241543}
{"recordId":"gty000b0deb@dx1956a154d5cb86a532:a4b4c0dc16864622a8bc339506cf6369","requestId":"gty000b0deb@dx1956a154d5cb86a532","sessionId":"cid000b0dea@dx1956a154ca1b86a532","eof":"1","text":"受到连续的降雨天气以及上游来水的共同影响在昨天也就是25号的上午10:00","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741241543}
send data finished:1741241543.152565
{"recordId":"gty000b0deb@dx1956a154d5cb86a532:a4b4c0dc16864622a8bc339506cf6369","requestId":"gty000b0deb@dx1956a154d5cb86a532","sessionId":"cid000b0dea@dx1956a154ca1b86a532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"受到连续的降雨天气以及上游来水的共同影响在昨天也就是25号的上午10:00","intentId":"chat","intentName":"闲聊","nlg":"这个我也不会，还是简单的问题适合我！","shouldEndSession":true},"nlu":{"input":"受到连续的降雨天气以及上游来水的共同影响在昨天也就是25号的上午10:00","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741241543}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidc658869a@dxd1c41b2488c73eef00"}
连接正常关闭
1741241543
param:b'{\n            "auth_id": "e7bd384acded4382869fc77ddb8fd289",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidb7163d6c@dx36241b2488c73eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "291faec4d93043bd8a28338508e20350","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b103f@dx1956a156cffb8a9532"}
started:
ws start
####################
测试进行: 1.pcm_handle_9.pcm
{"recordId":"ase000fd41a@hu1956a1573f205c0882:291faec4d93043bd8a28338508e20350","requestId":"ase000fd41a@hu1956a1573f205c0882","sessionId":"cid000b103f@dx1956a156cffb8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741241545}
{"recordId":"gty000b1040@dx1956a156db6b8a9532:291faec4d93043bd8a28338508e20350","requestId":"gty000b1040@dx1956a156db6b8a9532","sessionId":"cid000b103f@dx1956a156cffb8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741241548}
{"recordId":"gty000b1040@dx1956a156db6b8a9532:291faec4d93043bd8a28338508e20350","requestId":"gty000b1040@dx1956a156db6b8a9532","sessionId":"cid000b103f@dx1956a156cffb8a9532","eof":"1","text":"水位猛涨要尽快向两侧高处跑","pinyin":"","topic":"asr.speech.result","age":"elder","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741241548}
send data finished:1741241548.3379629
{"recordId":"gty000b1040@dx1956a156db6b8a9532:291faec4d93043bd8a28338508e20350","requestId":"gty000b1040@dx1956a156db6b8a9532","sessionId":"cid000b103f@dx1956a156cffb8a9532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"水位猛涨要尽快向两侧高处跑","intentId":"chat","intentName":"闲聊","nlg":"这要不我给你展示我的其他本领吧！","shouldEndSession":true},"nlu":{"input":"水位猛涨要尽快向两侧高处跑","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741241548}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"ciddcf70238@dxfe8a1b2488cc3eef00"}
连接正常关闭
#####运行完成,点击查看结果按钮查看结果!##########
