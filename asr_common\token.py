import hashlib
import json
import time

import requests

url = "http://api.iflyos.cn/external/ls_dispatch_auth/tokens"


def get_token(product_id, secret, device_id):
    # secret = secret
    ts = str(int(time.time()))
    sign = hashlib.md5((secret + ts).encode()).hexdigest()
    payload = json.dumps({
        "productId": product_id,
        "deviceId": device_id,
        "curtime": ts,
        "checksum": sign
    })
    headers = {
        'Content-Type': 'application/json'
    }

    response = requests.request("POST", url, headers=headers, data=payload, timeout=10)
    token = json.loads(response.text).get('token')
    return token

if __name__ == '__main__':
    g_token = get_token('c959d056-7590-4baf-9010-25e2225fb003', '43e921c1-4526-431b-9bc1-33ff9da4cca2', 'aiui_test_device1')
    print(g_token)