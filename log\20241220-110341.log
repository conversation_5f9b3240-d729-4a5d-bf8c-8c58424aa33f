1734663821
param:b'{\n            "auth_id": "0b5f01ced8fc4c99914677d2c8362f6d",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid237e191a@dx2ee61ac02a8c3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "61d9723a3315428cab705fd75146fc47","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bde2e@dx193e2055773b8aa532"}
started:
ws start
####################
测试进行: ctm0001030e@hu17b4cb4fb36020c902#46275217.pcm
{"recordId":"ase000dea0b@hu193e20568c304d3882:61d9723a3315428cab705fd75146fc47","requestId":"ase000dea0b@hu193e20568c304d3882","sessionId":"cid000bde2e@dx193e2055773b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734663825}
{"recordId":"gty000bde2f@dx193e2055a7fb8aa532:61d9723a3315428cab705fd75146fc47","requestId":"gty000bde2f@dx193e2055a7fb8aa532","sessionId":"cid000bde2e@dx193e2055773b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734663827}
{"recordId":"gty000bde2f@dx193e2055a7fb8aa532:61d9723a3315428cab705fd75146fc47","requestId":"gty000bde2f@dx193e2055a7fb8aa532","sessionId":"cid000bde2e@dx193e2055773b8aa532","eof":"1","text":"电视怎么调大屏","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734663827}
send data finished:1734663828616.8186
{"recordId":"gty000bde2f@dx193e2055a7fb8aa532:61d9723a3315428cab705fd75146fc47","requestId":"gty000bde2f@dx193e2055a7fb8aa532","sessionId":"cid000bde2e@dx193e2055773b8aa532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"电视怎么调大屏","intentId":"chat","intentName":"闲聊","nlg":"这样高难度的问题还是给我撞上啦，换一个简单的呗。","shouldEndSession":true},"nlu":{"input":"电视怎么调大屏","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1734663827}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidd987d70a@dx1ddc1ac02a933eef00"}
连接正常关闭
1734663828
param:b'{\n            "auth_id": "4f2d7f3a501142478ec04ee467bc13b8",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid02933af6@dx92101ac02a943eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "c7c78ef5d22542fbafe1d64575969a9c","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be222@dx193e205739eb8a9532"}
started:
ws start
####################
测试进行: ctm00010320@hu17b4cb503aa020c902#46275237.pcm
Error reading file[Errno 2] No such file or directory: 'data/silence.wav'
{"action":"error","code":"20010108","data":"","desc":"{\"code\":20010108,\"sub\":\"\"}","sid":"gty000be223@dx193e205758bb8a9532"}
{'action': 'error', 'code': '20010108', 'data': '', 'desc': '{"code":20010108,"sub":""}', 'sid': 'gty000be223@dx193e205758bb8a9532'}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid7ccd9724@dxdcca1ac02aad3eef00"}
连接正常关闭
1734663854
param:b'{\n            "auth_id": "27b9eac0ac224bd2b0a6aa2efb1b6eef",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid068ee7c6@dxb0ae1ac02aad3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "d5c3bd7dc7314d048e54b7f113f19e5e","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bdb91@dx193e205d76db86a532"}
started:
ws start
####################
测试进行: ctm00010329@hu17b4cb507a7020c902#46275249.pcm
{"recordId":"ase000f659b@hu193e205ecbb05c2882:d5c3bd7dc7314d048e54b7f113f19e5e","requestId":"ase000f659b@hu193e205ecbb05c2882","sessionId":"cid000bdb91@dx193e205d76db86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734663859}
{"recordId":"gty000bdb92@dx193e205d91cb86a532:d5c3bd7dc7314d048e54b7f113f19e5e","requestId":"gty000bdb92@dx193e205d91cb86a532","sessionId":"cid000bdb91@dx193e205d76db86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734663860}
{"recordId":"gty000bdb92@dx193e205d91cb86a532:d5c3bd7dc7314d048e54b7f113f19e5e","requestId":"gty000bdb92@dx193e205d91cb86a532","sessionId":"cid000bdb91@dx193e205d76db86a532","eof":"1","text":"你可不可以学一下经典","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734663860}
send data finished:1734663861459.5442
{"recordId":"gty000bdb92@dx193e205d91cb86a532:d5c3bd7dc7314d048e54b7f113f19e5e","requestId":"gty000bdb92@dx193e205d91cb86a532","sessionId":"cid000bdb91@dx193e205d76db86a532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"你可不可以学一下经典","intentId":"chat","intentName":"闲聊","nlg":"这要不我给你展示我的其他本领吧！","shouldEndSession":true},"nlu":{"input":"你可不可以学一下经典","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1734663860}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid20a4bf94@dxdeb81ac02ab43eef00"}
连接正常关闭
1734663861
param:b'{\n            "auth_id": "33481cd6330740d5b24776fc5b717c0b",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid1f6e0b4a@dx5ae71ac02ab53eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "35b91b502f974a3a8bc4a64d1f8b2d70","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be0f8@dx193e205f4287844532"}
started:
ws start
####################
测试进行: ctm00010424@hu17b59a7c006020c902#46466943.pcm
{"recordId":"ase000ed094@hu193e20603f705c4882:35b91b502f974a3a8bc4a64d1f8b2d70","requestId":"ase000ed094@hu193e20603f705c4882","sessionId":"cid000be0f8@dx193e205f4287844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734663865}
{"recordId":"ase000d10fb@hu193e20604d804d3882:35b91b502f974a3a8bc4a64d1f8b2d70","requestId":"ase000d10fb@hu193e20604d804d3882","sessionId":"cid000be0f8@dx193e205f4287844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734663865}
{"recordId":"gty000be0f9@dx193e205f5d27844532:35b91b502f974a3a8bc4a64d1f8b2d70","requestId":"gty000be0f9@dx193e205f5d27844532","sessionId":"cid000be0f8@dx193e205f4287844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734663866}
{"recordId":"gty000be0f9@dx193e205f5d27844532:35b91b502f974a3a8bc4a64d1f8b2d70","requestId":"gty000be0f9@dx193e205f5d27844532","sessionId":"cid000be0f8@dx193e205f4287844532","eof":"1","text":"我要听易烊千玺","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1734663866}
send data finished:1734663867583.643
{"recordId":"gty000be0f9@dx193e205f5d27844532:35b91b502f974a3a8bc4a64d1f8b2d70","requestId":"gty000be0f9@dx193e205f5d27844532","sessionId":"cid000be0f8@dx193e205f4287844532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"我要听易烊千玺","intentId":"PLAY","intentName":"点歌","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"我要听易烊千玺","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"歌手名","value":"易烊千玺"}]}}},"timestamp":1734663866}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid7786dbea@dxb9101ac02aba3eef00"}
连接正常关闭
1734663867
param:b'{\n            "auth_id": "31ea8a948e134b8c8caf1a72fd0185c5",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid81f3cd87@dxedab1ac02abb3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "7cebbc67e26e479084bdbf5ceec23d22","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bde58@dx193e2060b3db8aa532"}
started:
ws start
####################
测试进行: ctm00010435@hu17b59a7c588020c902#46466960.pcm
Error reading file[Errno 2] No such file or directory: 'data/silence.wav'
{"action":"error","code":"20010108","data":"","desc":"{\"code\":20010108,\"sub\":\"\"}","sid":"gty000bde59@dx193e2060d1db8aa532"}
{'action': 'error', 'code': '20010108', 'data': '', 'desc': '{"code":20010108,"sub":""}', 'sid': 'gty000bde59@dx193e2060d1db8aa532'}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid85541e74@dx2f691ac02ad43eef00"}
连接正常关闭
1734663893
param:b'{\n            "auth_id": "751abed84fca49e0a58c52fdb60e4206",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid7fedc4ae@dx40c81ac02ad43eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "1a1516be07f8419aaf2ee79a9a1a0247","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bdbad@dx193e2066f8eb86a532"}
started:
ws start
####################
测试进行: ctm00010443@hu17b59a7c8d1020c902#46466977.pcm
{"recordId":"ase000f7df0@hu193e2067d4505c0882:1a1516be07f8419aaf2ee79a9a1a0247","requestId":"ase000f7df0@hu193e2067d4505c0882","sessionId":"cid000bdbad@dx193e2066f8eb86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734663896}
{"recordId":"gty000bdbae@dx193e2067514b86a532:1a1516be07f8419aaf2ee79a9a1a0247","requestId":"gty000bdbae@dx193e2067514b86a532","sessionId":"cid000bdbad@dx193e2066f8eb86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734663901}
{"recordId":"gty000bdbae@dx193e2067514b86a532:1a1516be07f8419aaf2ee79a9a1a0247","requestId":"gty000bdbae@dx193e2067514b86a532","sessionId":"cid000bdbad@dx193e2066f8eb86a532","eof":"1","text":"二十风","pinyin":"","topic":"asr.speech.result","age":"elder","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734663901}
send data finished:1734663902246.3835
{"recordId":"gty000bdbae@dx193e2067514b86a532:1a1516be07f8419aaf2ee79a9a1a0247","requestId":"gty000bdbae@dx193e2067514b86a532","sessionId":"cid000bdbad@dx193e2066f8eb86a532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"二十风","intentId":"chat","intentName":"闲聊","nlg":"这样高难度的问题还是给我撞上啦，换一个简单的呗。","shouldEndSession":true},"nlu":{"input":"二十风","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1734663901}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid93af1cf1@dx4d821ac02add3eef00"}
连接正常关闭
1734663902
param:b'{\n            "auth_id": "e3e9f2105dca4f76ae082ad9628d1e14",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidab11571c@dxbbe61ac02add3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "299868203c13488d89f8ea4598aa6801","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bde7a@dx193e206933fb8aa532"}
started:
ws start
####################
测试进行: ctm00010558@hu17b62d10b3d020c902#46579979.pcm
{"recordId":"ase000e1543@hu193e206b3f805bf882:299868203c13488d89f8ea4598aa6801","requestId":"ase000e1543@hu193e206b3f805bf882","sessionId":"cid000bde7a@dx193e206933fb8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734663910}
{"recordId":"gty000bde7d@dx193e206ade2b8aa532:299868203c13488d89f8ea4598aa6801","requestId":"gty000bde7d@dx193e206ade2b8aa532","sessionId":"cid000bde7a@dx193e206933fb8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734663916}
{"recordId":"gty000bde7d@dx193e206ade2b8aa532:299868203c13488d89f8ea4598aa6801","requestId":"gty000bde7d@dx193e206ade2b8aa532","sessionId":"cid000bde7a@dx193e206933fb8aa532","eof":"1","text":"我想看电视剧","pinyin":"","topic":"asr.speech.result","age":"child","ageScore":"","gender":"","genderScore":"","language_class":"mandarin","timestamp":1734663916}
send data finished:1734663917119.1804
{"recordId":"gty000bde7d@dx193e206ade2b8aa532:299868203c13488d89f8ea4598aa6801","requestId":"gty000bde7d@dx193e206ade2b8aa532","sessionId":"cid000bde7a@dx193e206933fb8aa532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"我想看电视剧","intentId":"chat","intentName":"闲聊","nlg":"那我们正好休息下眼睛聊聊天。","shouldEndSession":true},"nlu":{"input":"我想看电视剧","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1734663916}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"ciddab8f477@dxac531ac02aec3eef00"}
连接正常关闭
1734663917
param:b'{\n            "auth_id": "97126c98512e4ef78700c75387ebb182",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid5adb6b26@dx2f661ac02aec3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "28fe5bd697294236b0e78e373444550f","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bdbc1@dx193e206cd40b86a532"}
started:
ws start
####################
测试进行: ctm00010562@hu17b62d11176020c902#46579992.pcm
{"recordId":"ase000fa08d@hu193e206dd8105c2882:28fe5bd697294236b0e78e373444550f","requestId":"ase000fa08d@hu193e206dd8105c2882","sessionId":"cid000bdbc1@dx193e206cd40b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734663921}
{"recordId":"gty000bdbc3@dx193e206d1acb86a532:28fe5bd697294236b0e78e373444550f","requestId":"gty000bdbc3@dx193e206d1acb86a532","sessionId":"cid000bdbc1@dx193e206cd40b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734663926}
{"recordId":"gty000bdbc3@dx193e206d1acb86a532:28fe5bd697294236b0e78e373444550f","requestId":"gty000bdbc3@dx193e206d1acb86a532","sessionId":"cid000bdbc1@dx193e206cd40b86a532","eof":"1","text":"","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734663926}
{"recordId":"gty000bdbc3@dx193e206d1acb86a532:28fe5bd697294236b0e78e373444550f","requestId":"gty000bdbc3@dx193e206d1acb86a532","sessionId":"cid000bdbc1@dx193e206cd40b86a532","topic":"dm.output","skill":"","skillId":"","speakUrl":"","error":{"errId":"010305","errMsg":"asr result is null"},"dm":{},"nlu":{"input":"","skill":"","skillId":""},"timestamp":1734663926}
发送结束标识
发送断开连接标识
send data finished:1734663927177.8464
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid1d0e949d@dx577c1ac02af63eef00"}
连接正常关闭
1734663927
param:b'{\n            "auth_id": "36ceacbdab8e4c0ca84ffcb7b95dd9b9",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid94bc82bb@dx95671ac02af63eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "93a07c0d6c024bd2a0c15bc02c88ba13","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be12a@dx193e206f5167844532"}
started:
ws start
####################
测试进行: ctm00010573@hu17b62d11b05020c902#46580006.pcm
{"recordId":"ase000e2a46@hu193e20709a005bf882:93a07c0d6c024bd2a0c15bc02c88ba13","requestId":"ase000e2a46@hu193e20709a005bf882","sessionId":"cid000be12a@dx193e206f5167844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734663932}
{"recordId":"gty000be12b@dx193e206f6f57844532:93a07c0d6c024bd2a0c15bc02c88ba13","requestId":"gty000be12b@dx193e206f6f57844532","sessionId":"cid000be12a@dx193e206f5167844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734663933}
{"recordId":"gty000be12b@dx193e206f6f57844532:93a07c0d6c024bd2a0c15bc02c88ba13","requestId":"gty000be12b@dx193e206f6f57844532","sessionId":"cid000be12a@dx193e206f5167844532","eof":"1","text":"设置今天晚上6点的闹钟","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1734663933}
send data finished:1734663934837.6375
{"recordId":"gty000be12b@dx193e206f6f57844532:93a07c0d6c024bd2a0c15bc02c88ba13","requestId":"gty000be12b@dx193e206f6f57844532","sessionId":"cid000be12a@dx193e206f5167844532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"设置今天晚上6点的闹钟","intentId":"chat","intentName":"闲聊","nlg":"这个我真不懂，换个我懂的呗。","shouldEndSession":true},"nlu":{"input":"设置今天晚上6点的闹钟","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1734663934}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid9bed01a4@dx6ced1ac02afe3eef00"}
连接正常关闭
1734663935
param:b'{\n            "auth_id": "d2537d9ad6ed49b6b0aa4465e7520bb6",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidcd51846d@dx160e1ac02afe3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "4a1e7e66a18349b5b512caee97ee1b2b","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be25e@dx193e2071261b8a9532"}
started:
ws start
####################
测试进行: ctm00010586@hu17b62d120df020c902#46580018.pcm
{"recordId":"ase000fa83e@hu193e20727fb05c0882:4a1e7e66a18349b5b512caee97ee1b2b","requestId":"ase000fa83e@hu193e20727fb05c0882","sessionId":"cid000be25e@dx193e2071261b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734663940}
{"recordId":"gty000be25f@dx193e207145db8a9532:4a1e7e66a18349b5b512caee97ee1b2b","requestId":"gty000be25f@dx193e207145db8a9532","sessionId":"cid000be25e@dx193e2071261b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734663941}
{"recordId":"gty000be25f@dx193e207145db8a9532:4a1e7e66a18349b5b512caee97ee1b2b","requestId":"gty000be25f@dx193e207145db8a9532","sessionId":"cid000be25e@dx193e2071261b8a9532","eof":"1","text":"今天太阳什么时候出来","pinyin":"","topic":"asr.speech.result","age":"child","ageScore":"","gender":"","genderScore":"","language_class":"mandarin","timestamp":1734663941}
send data finished:1734663942485.1455
{"recordId":"gty000be25f@dx193e207145db8a9532:4a1e7e66a18349b5b512caee97ee1b2b","requestId":"gty000be25f@dx193e207145db8a9532","sessionId":"cid000be25e@dx193e2071261b8a9532","topic":"dm.output","skill":"天气","skillId":"2019042500000544","speakUrl":"","error":{},"dm":{"input":"今天太阳什么时候出来","intentId":"QUERY","intentName":"查询天气信息","nlg":"北京市今天全天多云，-6℃ ~ 3℃，东北风3-4级，天气寒冷，注意保暖。","widget":{"webhookResp":{"result":[{"airData":56,"airQuality":"良","city":"北京市","date":"2024-12-20","dateLong":1734624000,"date_for_voice":"今天","exp":{"ct":{"expName":"穿衣指数","level":"寒冷","prompt":"外面天寒地冻，防寒保暖最重要，帽子、围巾、手套全副武装，不宜室外逛街。"},"dy":{"expName":"钓鱼指数","level":"较适宜","prompt":"温差稍大，鱼儿不太活跃，可能会对钓鱼产生影响。"},"gm":{"expName":"感冒指数","level":"较易发","prompt":"感冒较易发生，干净整洁的环境和清新流通的空气都有利于降低感冒的几率，体质较弱的童鞋们要特别加强自我保护。"},"jt":{"expName":"交通指数","level":"良好","prompt":"天气较好，路面干燥，交通气象条件良好，车辆可以正常行驶。"},"tr":{"expName":"旅游指数","level":"一般","prompt":"天空状况还是比较好的，但温度比较低，且风稍大，会让人感觉有点冷。外出请备上防风保暖衣物。"},"uv":{"expName":"紫外线指数","level":"最弱","prompt":"辐射弱，请涂擦SPF8-12防晒护肤品。"},"xc":{"expName":"洗车指数","level":"较适宜","prompt":"较适宜洗车，未来一天无雨，风力较小，擦洗一新的汽车至少能保持一天。"},"yd":{"expName":"运动指数","level":"不适宜","prompt":"气温过低，特别容易着凉感冒，较不适宜户外运动，建议室内运动。"}},"extra":"","humidity":"24%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2024-12-20 11:00:08","pm25":"50","precipitation":"0","sunRise":"2024-12-20 07:32:00","sunSet":"2024-12-20 16:52:00","temp":3,"tempHigh":"3℃","tempLow":"-6℃","tempRange":"-6℃ ~ 3℃","tempReal":"-2℃","visibility":"","warning":"大风蓝色预警","weather":"多云","weatherDescription":"天气寒冷，注意保暖。","weatherDescription3":"-6℃到4℃，风不大，天气寒冷，注意保暖。北京市气象台发布大风蓝色预警信号。","weatherDescription7":"-5℃到4℃，风不大，天气寒冷，注意保暖。北京市气象台发布大风蓝色预警信号。","weatherType":1,"week":"周五","wind":"东北风3-4级","windLevel":0},{"airData":44,"airQuality":"优","city":"北京市","date":"2024-12-19","dateLong":1734537600,"date_for_voice":"昨天","humidity":"31%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2024-12-20 11:00:08","sunRise":"2024-12-19 07:31:00","sunSet":"2024-12-19 16:52:00","tempHigh":"3℃","tempLow":"-6℃","tempRange":"-6℃ ~ 3℃","weather":"多云","weatherDescription":"天气寒冷，注意保暖。","weatherType":1,"week":"周四","wind":"南风微风","windLevel":0},{"airData":30,"airQuality":"优","city":"北京市","date":"2024-12-21","dateLong":1734710400,"date_for_voice":"明天","humidity":"26%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2024-12-20 11:00:08","sunRise":"2024-12-21 07:32:00","sunSet":"2024-12-21 16:53:00","tempHigh":"4℃","tempLow":"-6℃","tempRange":"-6℃ ~ 4℃","weather":"晴","weatherDescription":"天气寒冷，注意保暖。","weatherType":0,"week":"周六","wind":"西北风3-4级","windLevel":1},{"airData":40,"airQuality":"优","city":"北京市","date":"2024-12-22","dateLong":1734796800,"date_for_voice":"后天","humidity":"24%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2024-12-20 11:00:08","sunRise":"2024-12-22 07:33:00","sunSet":"2024-12-22 16:53:00","tempHigh":"4℃","tempLow":"-7℃","tempRange":"-7℃ ~ 4℃","weather":"晴","weatherDescription":"天气寒冷，注意保暖。","weatherType":0,"week":"周日","wind":"西北风微风","windLevel":0},{"airData":100,"airQuality":"良","city":"北京市","date":"2024-12-23","dateLong":1734883200,"date_for_voice":"23号","humidity":"23%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2024-12-20 11:00:08","sunRise":"2024-12-23 07:33:00","sunSet":"2024-12-23 16:54:00","tempHigh":"4℃","tempLow":"-7℃","tempRange":"-7℃ ~ 4℃","weather":"多云","weatherDescription":"天气寒冷，注意保暖。","weatherType":1,"week":"下周一","wind":"西北风转东北风微风","windLevel":0},{"airData":110,"airQuality":"轻微污染","city":"北京市","date":"2024-12-24","dateLong":1734969600,"date_for_voice":"24号","humidity":"26%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2024-12-20 11:00:08","sunRise":"2024-12-24 07:34:00","sunSet":"2024-12-24 16:54:00","tempHigh":"3℃","tempLow":"-5℃","tempRange":"-5℃ ~ 3℃","weather":"多云","weatherDescription":"天气寒冷，注意保暖。","weatherType":1,"week":"下周二","wind":"东南风转北风微风","windLevel":0},{"airData":60,"airQuality":"良","city":"北京市","date":"2024-12-25","dateLong":1735056000,"date_for_voice":"25号","humidity":"25%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2024-12-20 11:00:08","sunRise":"2024-12-25 07:34:00","sunSet":"2024-12-25 16:55:00","tempHigh":"4℃","tempLow":"-6℃","tempRange":"-6℃ ~ 4℃","weather":"晴","weatherDescription":"天气寒冷，注意保暖。","weatherType":0,"week":"下周三","wind":"西北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2024-12-26","dateLong":1735142400,"date_for_voice":"26号","humidity":"23%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2024-12-20 11:00:08","sunRise":"2024-12-26 07:35:00","sunSet":"2024-12-26 16:55:00","tempHigh":"1℃","tempLow":"-6℃","tempRange":"-6℃ ~ 1℃","weather":"多云","weatherDescription":"天气寒冷，注意保暖。","weatherType":1,"week":"下周四","wind":"西北风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2024-12-27","dateLong":1735228800,"date_for_voice":"27号","humidity":"21%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2024-12-20 11:00:08","sunRise":"2024-12-27 07:35:00","sunSet":"2024-12-27 16:56:00","tempHigh":"3℃","tempLow":"-4℃","tempRange":"-4℃ ~ 3℃","weather":"晴","weatherDescription":"天气寒冷，注意保暖。","weatherType":0,"week":"下周五","wind":"西北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2024-12-28","dateLong":1735315200,"date_for_voice":"28号","humidity":"22%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2024-12-20 11:00:08","sunRise":"2024-12-28 07:35:00","sunSet":"2024-12-28 16:57:00","tempHigh":"3℃","tempLow":"-4℃","tempRange":"-4℃ ~ 3℃","weather":"晴转多云","weatherDescription":"天气寒冷，注意保暖。","weatherType":0,"week":"下周六","wind":"西北风转西南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2024-12-29","dateLong":1735401600,"date_for_voice":"29号","humidity":"23%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2024-12-20 11:00:08","sunRise":"2024-12-29 07:35:00","sunSet":"2024-12-29 16:58:00","tempHigh":"4℃","tempLow":"-4℃","tempRange":"-4℃ ~ 4℃","weather":"阴转晴","weatherDescription":"天气寒冷，注意保暖。","weatherType":2,"week":"下周日","wind":"东南风转西北风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2024-12-30","dateLong":1735488000,"date_for_voice":"30号","humidity":"22%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2024-12-20 11:00:08","sunRise":"2024-12-30 07:36:00","sunSet":"2024-12-30 16:58:00","tempHigh":"2℃","tempLow":"-2℃","tempRange":"-2℃ ~ 2℃","weather":"阴转多云","weatherDescription":"天气寒冷，注意保暖。","weatherType":2,"week":"下下周一","wind":"东南风转西北风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2024-12-31","dateLong":1735574400,"date_for_voice":"31号","humidity":"22%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2024-12-20 11:00:08","sunRise":"2024-12-31 07:36:00","sunSet":"2024-12-31 16:59:00","tempHigh":"0℃","tempLow":"-4℃","tempRange":"-4℃ ~ 0℃","weather":"晴","weatherDescription":"天气寒冷，注意保暖。","weatherType":0,"week":"下下周二","wind":"西北风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-01-01","dateLong":1735660800,"date_for_voice":"1号","humidity":"24%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2024-12-20 11:00:08","sunRise":"2025-01-01 07:36:00","sunSet":"2025-01-01 17:00:00","tempHigh":"1℃","tempLow":"-5℃","tempRange":"-5℃ ~ 1℃","weather":"晴","weatherDescription":"天气寒冷，注意保暖。","weatherType":0,"week":"下下周三","wind":"西北风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-01-02","dateLong":1735747200,"date_for_voice":"2号","humidity":"27%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2024-12-20 11:00:08","sunRise":"2025-01-02 07:36:00","sunSet":"2025-01-02 17:01:00","tempHigh":"1℃","tempLow":"-5℃","tempRange":"-5℃ ~ 1℃","weather":"晴","weatherDescription":"天气寒冷，注意保暖。","weatherType":0,"week":"下下周四","wind":"西北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-01-03","dateLong":1735833600,"date_for_voice":"3号","humidity":"28%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2024-12-20 11:00:08","sunRise":"2025-01-03 07:36:00","sunSet":"2025-01-03 17:02:00","tempHigh":"-1℃","tempLow":"-5℃","tempRange":"-5℃ ~ -1℃","weather":"晴","weatherDescription":"天气寒冷，注意保暖。","weatherType":0,"week":"下下周五","wind":"西北风微风","windLevel":0}]},"cityName":"北京市"},"dm_intent":"custom","cityName":"北京市","shouldEndSession":true},"nlu":{"input":"今天太阳什么时候出来","skill":"天气","skillId":"2019042500000544","skillVersion":"56","semantics":{"request":{"intent":"QUERY","slots":[{"name":"queryType","value":"内容"},{"name":"subfocus","value":"日出时间"},{"name":"datetime","normValue":"{\"datetime\":\"2024-12-20\",\"suggestDatetime\":\"2024-12-20\"}","value":"今天"},{"name":"location.city","normValue":"CURRENT_CITY","value":"CURRENT_CITY"},{"name":"location.poi","normValue":"CURRENT_POI","value":"CURRENT_POI"},{"name":"location.type","normValue":"LOC_POI","value":"LOC_POI"}]}}},"timestamp":1734663941}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidf30b9ac9@dxe86c1ac02b053eef00"}
连接正常关闭
1734663942
param:b'{\n            "auth_id": "c271bfd0806a4200a728068ccd73e82d",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid71ea3d5f@dx0d0a1ac02b053eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "3aaa086f3399439fafd66a77dae8b4d9","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bdbdd@dx193e2073007b86a532"}
started:
ws start
####################
测试进行: ctm00010e4a@hu17b5411338f0212902#46356772.pcm
{"recordId":"ase000e3698@hu193e2073d9805bf882:3aaa086f3399439fafd66a77dae8b4d9","requestId":"ase000e3698@hu193e2073d9805bf882","sessionId":"cid000bdbdd@dx193e2073007b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734663945}
{"recordId":"gty000bdbde@dx193e20731feb86a532:3aaa086f3399439fafd66a77dae8b4d9","requestId":"gty000bdbde@dx193e20731feb86a532","sessionId":"cid000bdbdd@dx193e2073007b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734663948}
{"recordId":"gty000bdbde@dx193e20731feb86a532:3aaa086f3399439fafd66a77dae8b4d9","requestId":"gty000bdbde@dx193e20731feb86a532","sessionId":"cid000bdbdd@dx193e2073007b86a532","eof":"1","text":"4/5÷12","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734663948}
send data finished:1734663949075.37
{"recordId":"gty000bdbde@dx193e20731feb86a532:3aaa086f3399439fafd66a77dae8b4d9","requestId":"gty000bdbde@dx193e20731feb86a532","sessionId":"cid000bdbdd@dx193e2073007b86a532","topic":"dm.output","skill":"计算器","skillId":"2019031500001072","speakUrl":"","error":{},"dm":{"input":"4/5÷12","intentId":"CALC_ANSWER","intentName":"直接返回计算结果","nlg":"等于0.0667","shouldEndSession":true},"nlu":{"input":"4/5÷12","skill":"计算器","skillId":"2019031500001072","skillVersion":"11","semantics":{"request":{"entrypoint":"ent","hazard":false,"score":1,"slots":[{"begin":0,"end":6,"name":"Calculator","normValue":"0.0667","value":"4/5÷12"}],"template":"{Calculator}"}}},"timestamp":1734663948}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid2c98f5ca@dxcacd1ac02b0c3eef00"}
连接正常关闭
1734663949
param:b'{\n            "auth_id": "72548741e5504d399dcf71b426f4d830",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidab46b6a7@dxa3f51ac02b0c3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "15c0c5e56d344081a6386bcad560b0e6","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bdbe3@dx193e207499fb86a532"}
started:
ws start
####################
测试进行: ctm00010e5c@hu17b54113cf90212902#46356799.pcm
{"recordId":"ase000dd5ce@hu193e2075aca0427882:15c0c5e56d344081a6386bcad560b0e6","requestId":"ase000dd5ce@hu193e2075aca0427882","sessionId":"cid000bdbe3@dx193e207499fb86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734663953}
{"recordId":"gty000bdbe4@dx193e2074ba6b86a532:15c0c5e56d344081a6386bcad560b0e6","requestId":"gty000bdbe4@dx193e2074ba6b86a532","sessionId":"cid000bdbe3@dx193e207499fb86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734663953}
{"recordId":"gty000bdbe4@dx193e2074ba6b86a532:15c0c5e56d344081a6386bcad560b0e6","requestId":"gty000bdbe4@dx193e2074ba6b86a532","sessionId":"cid000bdbe3@dx193e207499fb86a532","eof":"1","text":"京东购物关闭","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1734663953}
send data finished:1734663954694.95
{"recordId":"gty000bdbe4@dx193e2074ba6b86a532:15c0c5e56d344081a6386bcad560b0e6","requestId":"gty000bdbe4@dx193e2074ba6b86a532","sessionId":"cid000bdbe3@dx193e207499fb86a532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"京东购物关闭","intentId":"chat","intentName":"闲聊","nlg":"我就是个机器人，这个问题也太难啦。你简单点说呗！","shouldEndSession":true},"nlu":{"input":"京东购物关闭","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1734663954}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid4d1c59b0@dxec4a1ac02b123eef00"}
连接正常关闭
1734663954
param:b'{\n            "auth_id": "40e96cf6325c434dbbcdd2036ad8b126",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid39e7540f@dx4cda1ac02b123eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "6d4ec05507f3405b8de2cc831c97cbb5","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bdea6@dx193e207603ab8aa532"}
started:
ws start
####################
测试进行: ctm00011399@hu17b59b5998e020c902#46470195.pcm
{"recordId":"ase000e1851@hu193e207719005c3882:6d4ec05507f3405b8de2cc831c97cbb5","requestId":"ase000e1851@hu193e207719005c3882","sessionId":"cid000bdea6@dx193e207603ab8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734663959}
{"recordId":"ase000e42e5@hu193e207726d05bf882:6d4ec05507f3405b8de2cc831c97cbb5","requestId":"ase000e42e5@hu193e207726d05bf882","sessionId":"cid000bdea6@dx193e207603ab8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734663959}
{"recordId":"gty000bdea7@dx193e207623eb8aa532:6d4ec05507f3405b8de2cc831c97cbb5","requestId":"gty000bdea7@dx193e207623eb8aa532","sessionId":"cid000bdea6@dx193e207603ab8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734663960}
{"recordId":"gty000bdea7@dx193e207623eb8aa532:6d4ec05507f3405b8de2cc831c97cbb5","requestId":"gty000bdea7@dx193e207623eb8aa532","sessionId":"cid000bdea6@dx193e207603ab8aa532","eof":"1","text":"播放窗外的小豆豆","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1734663960}
send data finished:1734663961586.354
{"recordId":"gty000bdea7@dx193e207623eb8aa532:6d4ec05507f3405b8de2cc831c97cbb5","requestId":"gty000bdea7@dx193e207623eb8aa532","sessionId":"cid000bdea6@dx193e207603ab8aa532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"播放窗外的小豆豆","intentId":"chat","intentName":"闲聊","nlg":"这样高难度的问题还是给我撞上啦，换一个简单的呗。","shouldEndSession":true},"nlu":{"input":"播放窗外的小豆豆","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1734663960}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid574498bd@dx76ed1ac02b183eef00"}
连接正常关闭
1734663961
param:b'{\n            "auth_id": "f911cde23eb64f26a8395f3ad1dd6fd7",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cida6785107@dx5a131ac02b193eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "598ea9aecf8943a59b138fbc48e5388a","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be26c@dx193e2077ae4b8a9532"}
started:
ws start
####################
测试进行: ctm000113b3@hu17b59b5a724020c902#46470220.pcm
{"recordId":"ase000fca0c@hu193e207892705c2882:598ea9aecf8943a59b138fbc48e5388a","requestId":"ase000fca0c@hu193e207892705c2882","sessionId":"cid000be26c@dx193e2077ae4b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734663965}
{"recordId":"gty000be26d@dx193e2077ce1b8a9532:598ea9aecf8943a59b138fbc48e5388a","requestId":"gty000be26d@dx193e2077ce1b8a9532","sessionId":"cid000be26c@dx193e2077ae4b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734663965}
{"recordId":"gty000be26d@dx193e2077ce1b8a9532:598ea9aecf8943a59b138fbc48e5388a","requestId":"gty000be26d@dx193e2077ce1b8a9532","sessionId":"cid000be26c@dx193e2077ae4b8a9532","eof":"1","text":"春节小品","pinyin":"","topic":"asr.speech.result","age":"child","ageScore":"","gender":"","genderScore":"","language_class":"mandarin","timestamp":1734663965}
send data finished:1734663966733.1973
{"recordId":"gty000be26d@dx193e2077ce1b8a9532:598ea9aecf8943a59b138fbc48e5388a","requestId":"gty000be26d@dx193e2077ce1b8a9532","sessionId":"cid000be26c@dx193e2077ae4b8a9532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"春节小品","intentId":"chat","intentName":"闲聊","nlg":"这要不我给你展示我的其他本领吧！","shouldEndSession":true},"nlu":{"input":"春节小品","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1734663966}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid77e4e04d@dxf74a1ac02b1e3eef00"}
连接正常关闭
1734663966
param:b'{\n            "auth_id": "206024616a014ae894aa9ce29c963a85",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid96abce98@dxe95a1ac02b1e3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "9faae8cc1a134f4a9f71a9c0f77e11a2","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bdeb7@dx193e2078f20b8aa532"}
started:
ws start
####################
测试进行: ctm000113d0@hu17b59b5b6c3020c902#46470269.pcm
{"recordId":"ase000e5dbb@hu193e20798ad1323882:9faae8cc1a134f4a9f71a9c0f77e11a2","requestId":"ase000e5dbb@hu193e20798ad1323882","sessionId":"cid000bdeb7@dx193e2078f20b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734663969}
{"recordId":"gty000bdeb8@dx193e207911eb8aa532:9faae8cc1a134f4a9f71a9c0f77e11a2","requestId":"gty000bdeb8@dx193e207911eb8aa532","sessionId":"cid000bdeb7@dx193e2078f20b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734663971}
{"recordId":"gty000bdeb8@dx193e207911eb8aa532:9faae8cc1a134f4a9f71a9c0f77e11a2","requestId":"gty000bdeb8@dx193e207911eb8aa532","sessionId":"cid000bdeb7@dx193e2078f20b8aa532","eof":"1","text":"于谦的相声","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734663971}
send data finished:1734663972354.3772
{"recordId":"gty000bdeb8@dx193e207911eb8aa532:9faae8cc1a134f4a9f71a9c0f77e11a2","requestId":"gty000bdeb8@dx193e207911eb8aa532","sessionId":"cid000bdeb7@dx193e2078f20b8aa532","topic":"dm.output","skill":"相声小品","skillId":"crossTalk","speakUrl":"","error":{},"dm":{"input":"于谦的相声","intentId":"QUERY","intentName":"查询","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"于谦的相声","skill":"相声小品","skillId":"crossTalk","skillVersion":"19.0","semantics":{"request":{"slots":[{"name":"actor","value":"于谦"},{"name":"category","value":"相声"}]}}},"timestamp":1734663971}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid45ebf8d4@dx0e4b1ac02b233eef00"}
连接正常关闭
1734663972
param:b'{\n            "auth_id": "fec77a26f02c4d6ba89675de46c924e8",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid009fa023@dx5ea91ac02b243eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "4cbd574102d24d30960eb9d80c7d2e0f","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be276@dx193e207a5f5b8a9532"}
started:
ws start
####################
测试进行: ctm0001148b@hu17b6230bc850212902#46571615.pcm
{"recordId":"ase000e280f@hu193e207b17405c3882:4cbd574102d24d30960eb9d80c7d2e0f","requestId":"ase000e280f@hu193e207b17405c3882","sessionId":"cid000be276@dx193e207a5f5b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734663975}
{"recordId":"gty000be277@dx193e207a7fdb8a9532:4cbd574102d24d30960eb9d80c7d2e0f","requestId":"gty000be277@dx193e207a7fdb8a9532","sessionId":"cid000be276@dx193e207a5f5b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734663978}
{"recordId":"gty000be277@dx193e207a7fdb8a9532:4cbd574102d24d30960eb9d80c7d2e0f","requestId":"gty000be277@dx193e207a7fdb8a9532","sessionId":"cid000be276@dx193e207a5f5b8a9532","eof":"1","text":"我爸爸的姐妹妈妈是谁","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734663978}
send data finished:1734663979046.241
{"recordId":"gty000be277@dx193e207a7fdb8a9532:4cbd574102d24d30960eb9d80c7d2e0f","requestId":"gty000be277@dx193e207a7fdb8a9532","sessionId":"cid000be276@dx193e207a5f5b8a9532","topic":"dm.output","skill":"LEIQIAO.relationShip","skillId":"LEIQIAO.relationShip","speakUrl":"","error":{},"dm":{"input":"我爸爸的姐妹妈妈是谁","intentId":"CALL_ELSE","intentName":"查询","nlg":"爸爸的姐妹的妈妈是你的奶奶","shouldEndSession":true},"nlu":{"input":"我爸爸的姐妹妈妈是谁","skill":"LEIQIAO.relationShip","skillId":"LEIQIAO.relationShip","skillVersion":"74.0","semantics":{"request":{"entrypoint":"ent","hazard":false,"score":1,"slots":[{"begin":1,"end":3,"name":"call","normValue":"爸爸","value":"爸爸"},{"begin":4,"end":6,"name":"call","normValue":"姐妹","value":"姐妹"},{"begin":6,"end":8,"name":"call","normValue":"妈妈","value":"妈妈"}],"template":"我{call}的{call}{call}是谁"}}},"timestamp":1734663978}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidd61f2110@dxe8e71ac02b2a3eef00"}
连接正常关闭
1734663979
param:b'{\n            "auth_id": "6d0dcff13b434cbaaaafbe68b5de2e7c",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid0cb7d5a8@dx44011ac02b2a3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "8c5d4a0258094c4ab4bfb740b598dce9","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bdc05@dx193e207be97b86a532"}
started:
ws start
####################
测试进行: ctm00011492@hu17b6230c21d0212902#46571629.pcm
{"recordId":"ase000e2f99@hu193e207d13705c3882:8c5d4a0258094c4ab4bfb740b598dce9","requestId":"ase000e2f99@hu193e207d13705c3882","sessionId":"cid000bdc05@dx193e207be97b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734663983}
{"recordId":"gty000bdc06@dx193e207c06ab86a532:8c5d4a0258094c4ab4bfb740b598dce9","requestId":"gty000bdc06@dx193e207c06ab86a532","sessionId":"cid000bdc05@dx193e207be97b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734663983}
{"recordId":"gty000bdc06@dx193e207c06ab86a532:8c5d4a0258094c4ab4bfb740b598dce9","requestId":"gty000bdc06@dx193e207c06ab86a532","sessionId":"cid000bdc05@dx193e207be97b86a532","eof":"1","text":"闪付手机版","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734663983}
send data finished:1734663984705.3367
{"recordId":"gty000bdc06@dx193e207c06ab86a532:8c5d4a0258094c4ab4bfb740b598dce9","requestId":"gty000bdc06@dx193e207c06ab86a532","sessionId":"cid000bdc05@dx193e207be97b86a532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"闪付手机版","intentId":"chat","intentName":"闲聊","nlg":"你成功难住我啦，换个简单的问我吧。","shouldEndSession":true},"nlu":{"input":"闪付手机版","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1734663984}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid65fa2286@dxd1cf1ac02b303eef00"}
连接正常关闭
1734663984
param:b'{\n            "auth_id": "1d20772d07f046338ac802f872785ff7",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid3fbd14a1@dx66e01ac02b303eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "1f5ce58833ed4adabe58420b82e8faab","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be27e@dx193e207d4fab8a9532"}
started:
ws start
####################
测试进行: ctm000114a0@hu17b6230cc5c0212902#46571646.pcm
{"recordId":"ase000e4583@hu193e207e6f405c4882:1f5ce58833ed4adabe58420b82e8faab","requestId":"ase000e4583@hu193e207e6f405c4882","sessionId":"cid000be27e@dx193e207d4fab8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734663989}
{"recordId":"gty000be27f@dx193e207d6dbb8a9532:1f5ce58833ed4adabe58420b82e8faab","requestId":"gty000be27f@dx193e207d6dbb8a9532","sessionId":"cid000be27e@dx193e207d4fab8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734663990}
{"recordId":"gty000be27f@dx193e207d6dbb8a9532:1f5ce58833ed4adabe58420b82e8faab","requestId":"gty000be27f@dx193e207d6dbb8a9532","sessionId":"cid000be27e@dx193e207d4fab8a9532","eof":"1","text":"一公顷大约等于多少平方米","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734663990}
send data finished:1734663991603.1753
{"recordId":"gty000be27f@dx193e207d6dbb8a9532:1f5ce58833ed4adabe58420b82e8faab","requestId":"gty000be27f@dx193e207d6dbb8a9532","sessionId":"cid000be27e@dx193e207d4fab8a9532","topic":"dm.output","skill":"单位换算","skillId":"2018112200000082","speakUrl":"","error":{},"dm":{"input":"一公顷大约等于多少平方米","intentId":"CONVERSION_SOLUTION","intentName":"具体换算","nlg":"1公顷等于10000平方米。","shouldEndSession":true},"nlu":{"input":"一公顷大约等于多少平方米","skill":"单位换算","skillId":"2018112200000082","skillVersion":"76.0","semantics":{"request":{"entrypoint":"ent","hazard":false,"score":1,"slots":[{"begin":0,"end":1,"name":"srcNumber","normValue":"1","value":"一"},{"begin":1,"end":3,"name":"srcUnit","normValue":"公顷","value":"公顷"},{"begin":9,"end":12,"name":"destUnit","normValue":"平方米","value":"平方米"}],"template":"{srcNumber}{srcUnit}大约等于多少{destUnit}"}}},"timestamp":1734663990}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid501e5221@dxa3bf1ac02b363eef00"}
连接正常关闭
1734663991
param:b'{\n            "auth_id": "73a2e55879be4f14a557bccbe74844da",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid4f67b477@dx36481ac02b373eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "f76cb01d3d594c4e90574ed6810d52eb","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be148@dx193e207effa7844532"}
started:
ws start
####################
测试进行: ctm000114af@hu17b6230d0130212902#46571653.pcm
{"recordId":"ase000e3b0f@hu193e208014905c3882:f76cb01d3d594c4e90574ed6810d52eb","requestId":"ase000e3b0f@hu193e208014905c3882","sessionId":"cid000be148@dx193e207effa7844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734663995}
{"recordId":"gty000be149@dx193e207f1d17844532:f76cb01d3d594c4e90574ed6810d52eb","requestId":"gty000be149@dx193e207f1d17844532","sessionId":"cid000be148@dx193e207effa7844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734663996}
{"recordId":"gty000be149@dx193e207f1d17844532:f76cb01d3d594c4e90574ed6810d52eb","requestId":"gty000be149@dx193e207f1d17844532","sessionId":"cid000be148@dx193e207effa7844532","eof":"1","text":"我要你取消闹钟","pinyin":"","topic":"asr.speech.result","age":"child","ageScore":"","gender":"","genderScore":"","language_class":"mandarin","timestamp":1734663996}
send data finished:1734663997465.4895
{"recordId":"gty000be149@dx193e207f1d17844532:f76cb01d3d594c4e90574ed6810d52eb","requestId":"gty000be149@dx193e207f1d17844532","sessionId":"cid000be148@dx193e207effa7844532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"我要你取消闹钟","intentId":"chat","intentName":"闲聊","nlg":"这个我也不会，还是简单的问题适合我！","shouldEndSession":true},"nlu":{"input":"我要你取消闹钟","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1734663996}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid2e0f10e1@dx33f11ac02b3c3eef00"}
连接正常关闭
1734663997
param:b'{\n            "auth_id": "77c1f5df8be04b8798feb720fb888f91",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid96658c6a@dxe4181ac02b3d3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "655b766fbe064582930ab816ba28e6f7","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be14f@dx193e208070f7844532"}
started:
ws start
####################
测试进行: ctm00011891@hu17b59bae0c6020c902#46471001.pcm
{"recordId":"ase000e7c64@hu193e208176c1323882:655b766fbe064582930ab816ba28e6f7","requestId":"ase000e7c64@hu193e208176c1323882","sessionId":"cid000be14f@dx193e208070f7844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664001}
{"recordId":"gty000be151@dx193e20808fc7844532:655b766fbe064582930ab816ba28e6f7","requestId":"gty000be151@dx193e20808fc7844532","sessionId":"cid000be14f@dx193e208070f7844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664003}
{"recordId":"gty000be151@dx193e20808fc7844532:655b766fbe064582930ab816ba28e6f7","requestId":"gty000be151@dx193e20808fc7844532","sessionId":"cid000be14f@dx193e208070f7844532","eof":"1","text":"马上给我播放儿歌小牧童","pinyin":"","topic":"asr.speech.result","age":"elder","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664003}
send data finished:1734664004119.721
{"recordId":"gty000be151@dx193e20808fc7844532:655b766fbe064582930ab816ba28e6f7","requestId":"gty000be151@dx193e20808fc7844532","sessionId":"cid000be14f@dx193e208070f7844532","topic":"dm.output","skill":"国学","skillId":"AIUI.chLiterature","speakUrl":"","error":{},"dm":{"input":"马上给我播放儿歌小牧童","intentId":"QUERY_GROUP","intentName":"综合查询","nlg":"即将为你播放《牧童》－吕洞宾和小牧童","widget":{"content":[{"album":"五年级必背古诗文－国学小课堂","language":"CN","tags":"内容分类 亲子 国学启蒙 国学小课堂","title":"《牧童》－吕洞宾和小牧童","subTitle":"","imageUrl":"http://p0.ifengimg.com/a/2019_02/b87fafbcb7a5666_size246_w640_h640.jpg","linkUrl":"https://p6.renbenzhihui.com/video19.ifeng.com/video09/2019/01/09/4966760-543-066-1422.mp3?pf=OH9GI&vid=4502820&tm=1734664003553&pid=212031&t=1734664003&k=A7A874CE2ED00679C875A5B38235CEE7","id":"4502820","extra":{"source":"ifeng"}},{"album":"一年级必背古诗文－国学小课堂","language":"CN","tags":"内容分类 亲子 国学启蒙 国学小课堂","title":"《所见》－牛背上的小牧童","subTitle":"","imageUrl":"http://p0.ifengimg.com/a/2019_02/b091359975ea57c_size188_w640_h640.jpg","linkUrl":"https://p6.renbenzhihui.com/video19.ifeng.com/video09/2019/01/09/4966700-543-066-1343.mp3?pf=OH9GI&vid=4502784&tm=1734664003560&pid=212029&t=1734664003&k=6F85880DF4606A6D197F08C3A8B37EFF","id":"4502784","extra":{"source":"ifeng"}}]},"shouldEndSession":true},"nlu":{"input":"马上给我播放儿歌小牧童","skill":"国学","skillId":"AIUI.chLiterature","skillVersion":"2.0","semantics":{"request":{"entrypoint":"ent","score":0.8378620147705078,"slots":[{"begin":9,"end":11,"name":"name","normValue":"牧童","value":"牧童"}],"template":"{play}儿童国学{name}"}}},"timestamp":1734664003}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cide9f7a066@dxd8d71ac02b433eef00"}
连接正常关闭
1734664004
param:b'{\n            "auth_id": "11d06763b53e43d88bbfea1fd6837414",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid12613aec@dxbb121ac02b433eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "feb1b9d5707c4605b8c56b0a0874fede","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bded3@dx193e208219eb8aa532"}
started:
ws start
####################
测试进行: ctm0001189a@hu17b59bae594020c902#46471011.pcm
{"recordId":"ase000fe795@hu193e20831f605c0882:feb1b9d5707c4605b8c56b0a0874fede","requestId":"ase000fe795@hu193e20831f605c0882","sessionId":"cid000bded3@dx193e208219eb8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664008}
{"recordId":"gty000bded4@dx193e2082399b8aa532:feb1b9d5707c4605b8c56b0a0874fede","requestId":"gty000bded4@dx193e2082399b8aa532","sessionId":"cid000bded3@dx193e208219eb8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664009}
{"recordId":"gty000bded4@dx193e2082399b8aa532:feb1b9d5707c4605b8c56b0a0874fede","requestId":"gty000bded4@dx193e2082399b8aa532","sessionId":"cid000bded3@dx193e208219eb8aa532","eof":"1","text":"有没有小蓓蕾组合唱的儿歌","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664010}
send data finished:1734664010890.7158
{"recordId":"gty000bded4@dx193e2082399b8aa532:feb1b9d5707c4605b8c56b0a0874fede","requestId":"gty000bded4@dx193e2082399b8aa532","sessionId":"cid000bded3@dx193e208219eb8aa532","topic":"dm.output","skill":"儿歌","skillId":"2019031500001056","speakUrl":"","error":{},"dm":{"input":"有没有小蓓蕾组合唱的儿歌","intentId":"QUERY_BY_ARTIST","intentName":"根据歌手名点播儿歌","nlg":"没有找到合适内容","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"有没有小蓓蕾组合唱的儿歌","skill":"儿歌","skillId":"2019031500001056","skillVersion":"8","semantics":{"request":{"entrypoint":"ent","hazard":false,"score":1,"slots":[{"name":"歌手名","value":"小蓓蕾组合"}],"template":"有没有{artist}唱的儿歌"}}},"timestamp":1734664010}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cide2766c58@dxab951ac02b4a3eef00"}
连接正常关闭
1734664010
param:b'{\n            "auth_id": "1beb0246c8254be8a0fa8425ac7df0a4",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid0a3949d7@dxe9901ac02b4a3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "c9f19c62fede407c98862b32895abf82","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be29c@dx193e2083b00b8a9532"}
started:
ws start
####################
测试进行: ctm000118a9@hu17b59baf074020c902#46471029.pcm
{"recordId":"ase000e7799@hu193e2084b6a05bf882:c9f19c62fede407c98862b32895abf82","requestId":"ase000e7799@hu193e2084b6a05bf882","sessionId":"cid000be29c@dx193e2083b00b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664014}
{"recordId":"ase000e7799@hu193e2084b6a05bf882:c9f19c62fede407c98862b32895abf82","requestId":"ase000e7799@hu193e2084b6a05bf882","sessionId":"cid000be29c@dx193e2083b00b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664014}
{"recordId":"gty000be29d@dx193e2083d0cb8a9532:c9f19c62fede407c98862b32895abf82","requestId":"gty000be29d@dx193e2083d0cb8a9532","sessionId":"cid000be29c@dx193e2083b00b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664015}
{"recordId":"gty000be29d@dx193e2083d0cb8a9532:c9f19c62fede407c98862b32895abf82","requestId":"gty000be29d@dx193e2083d0cb8a9532","sessionId":"cid000be29c@dx193e2083b00b8a9532","eof":"1","text":"可以帮我推荐个股票吗","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664015}
send data finished:1734664016834.7249
{"recordId":"gty000be29d@dx193e2083d0cb8a9532:c9f19c62fede407c98862b32895abf82","requestId":"gty000be29d@dx193e2083d0cb8a9532","sessionId":"cid000be29c@dx193e2083b00b8a9532","topic":"dm.output","skill":"股票","skillId":"IFLYTEK.stock","speakUrl":"","error":{},"dm":{"input":"可以帮我推荐个股票吗","intentId":"STOCK_RECOMMEND","intentName":"推荐股票","nlg":"我还不能预知未来股市，无法为您推荐股票。","shouldEndSession":true},"nlu":{"input":"可以帮我推荐个股票吗","skill":"股票","skillId":"IFLYTEK.stock","skillVersion":"371.0","semantics":{"request":{"slots":[]}}},"timestamp":1734664016}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidb73512ed@dx6beb1ac02b503eef00"}
连接正常关闭
1734664016
param:b'{\n            "auth_id": "1fb0a180910e41dcb2a73401e35ef06d",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid22bd4ce3@dx545b1ac02b503eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "de6df94db7ec432498e57b4cef230052","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bdc1a@dx193e2085216b86a532"}
started:
ws start
####################
测试进行: ctm000118b6@hu17b4fbfceea020c902#46308921.pcm
{"recordId":"ase000ff514@hu193e208698305c0882:de6df94db7ec432498e57b4cef230052","requestId":"ase000ff514@hu193e208698305c0882","sessionId":"cid000bdc1a@dx193e2085216b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664022}
{"recordId":"gty000bdc1f@dx193e2085425b86a532:de6df94db7ec432498e57b4cef230052","requestId":"gty000bdc1f@dx193e2085425b86a532","sessionId":"cid000bdc1a@dx193e2085216b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664025}
{"recordId":"gty000bdc1f@dx193e2085425b86a532:de6df94db7ec432498e57b4cef230052","requestId":"gty000bdc1f@dx193e2085425b86a532","sessionId":"cid000bdc1a@dx193e2085216b86a532","eof":"1","text":"我想听长发公主的故事","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1734664025}
send data finished:1734664025863.4724
{"recordId":"gty000bdc1f@dx193e2085425b86a532:de6df94db7ec432498e57b4cef230052","requestId":"gty000bdc1f@dx193e2085425b86a532","sessionId":"cid000bdc1a@dx193e2085216b86a532","topic":"dm.output","skill":"故事","skillId":"2019031500001010","speakUrl":"","error":{},"dm":{"input":"我想听长发公主的故事","intentId":"QUERY","intentName":"故事点播","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"我想听长发公主的故事","skill":"故事","skillId":"2019031500001010","skillVersion":"55","semantics":{"request":{"slots":[{"name":"name","value":"长发公主"}]}}},"timestamp":1734664025}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid110ec3c2@dxb3801ac02b593eef00"}
连接正常关闭
1734664025
param:b'{\n            "auth_id": "b8411b88d2ab43acb7e88c139fe7ad0e",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidae64f5fa@dx5f6c1ac02b593eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "1f8a47cbb4f64542a0ce9ce3696dd3f4","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be15a@dx193e20875ab7844532"}
started:
ws start
####################
测试进行: ctm000118c4@hu17b4fbfd54b020c902#46308945.pcm
{"recordId":"ase000d2010@hu193e2088dbd0427882:1f8a47cbb4f64542a0ce9ce3696dd3f4","requestId":"ase000d2010@hu193e2088dbd0427882","sessionId":"cid000be15a@dx193e20875ab7844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664031}
{"recordId":"gty000be15b@dx193e208784f7844532:1f8a47cbb4f64542a0ce9ce3696dd3f4","requestId":"gty000be15b@dx193e208784f7844532","sessionId":"cid000be15a@dx193e20875ab7844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664034}
{"recordId":"gty000be15b@dx193e208784f7844532:1f8a47cbb4f64542a0ce9ce3696dd3f4","requestId":"gty000be15b@dx193e208784f7844532","sessionId":"cid000be15a@dx193e20875ab7844532","eof":"1","text":"有没有调频90.8生活广播","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664034}
send data finished:1734664035020.693
{"recordId":"gty000be15b@dx193e208784f7844532:1f8a47cbb4f64542a0ce9ce3696dd3f4","requestId":"gty000be15b@dx193e208784f7844532","sessionId":"cid000be15a@dx193e20875ab7844532","topic":"dm.output","skill":"网络电台","skillId":"2019031500001032","speakUrl":"","error":{},"dm":{"input":"有没有调频90.8生活广播","intentId":"LAUNCH","intentName":"打开","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"有没有调频90.8生活广播","skill":"网络电台","skillId":"2019031500001032","skillVersion":"44","semantics":{"request":{"slots":[{"name":"code","value":"90.8"},{"name":"nameOrig","value":"生活电台"},{"name":"waveband","value":"fm"},{"name":"category","value":"生活"}]}}},"timestamp":1734664034}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidd1a2f65c@dxbe631ac02b623eef00"}
连接正常关闭
1734664035
param:b'{\n            "auth_id": "793a3072b26147d187e2577417a84a49",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid1fcbc4e6@dxf3351ac02b623eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "bbb9bc7d7ce4499691e1b3e94631b5c9","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bdc29@dx193e208995bb86a532"}
started:
ws start
####################
测试进行: ctm000118cf@hu17b4fbfd9e3020c902#46308961.pcm
{"recordId":"ase000e657d@hu193e208b0c305c3882:bbb9bc7d7ce4499691e1b3e94631b5c9","requestId":"ase000e657d@hu193e208b0c305c3882","sessionId":"cid000bdc29@dx193e208995bb86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664040}
{"recordId":"gty000bdc2a@dx193e2089b97b86a532:bbb9bc7d7ce4499691e1b3e94631b5c9","requestId":"gty000bdc2a@dx193e2089b97b86a532","sessionId":"cid000bdc29@dx193e208995bb86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664041}
{"recordId":"gty000bdc2a@dx193e2089b97b86a532:bbb9bc7d7ce4499691e1b3e94631b5c9","requestId":"gty000bdc2a@dx193e2089b97b86a532","sessionId":"cid000bdc29@dx193e208995bb86a532","eof":"1","text":"找一下三字经","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664041}
send data finished:1734664042358.1895
{"recordId":"gty000bdc2a@dx193e2089b97b86a532:bbb9bc7d7ce4499691e1b3e94631b5c9","requestId":"gty000bdc2a@dx193e2089b97b86a532","sessionId":"cid000bdc29@dx193e208995bb86a532","topic":"dm.output","skill":"国学","skillId":"AIUI.chLiterature","speakUrl":"","error":{},"dm":{"input":"找一下三字经","intentId":"QUERY_GROUP","intentName":"综合查询","nlg":"即将为你播放三字经 读史者","widget":{"content":[{"album":"嘟拉新三字经","language":"CN","tags":"嘟拉学堂,嘟拉新三字经","title":"三字经 读史者","subTitle":"","imageUrl":"","linkUrl":"https://cdn9003.iflyos.cn/audio/national/newsanzijing/854a54e025a6be728d9026ce60183d4e.mp3","id":"854a54e025a6be728d9026ce60183d4e","extra":{"source":"61ertong"}},{"album":"嘟拉三字经","language":"CN","tags":"嘟拉学堂,嘟拉三字经","title":"三字经 此十义","subTitle":"","imageUrl":"","linkUrl":"https://cdn9003.iflyos.cn/audio/national/sanzijing/129e6cdf9e772281c8cb22df6d4d3c77.mp3","id":"129e6cdf9e772281c8cb22df6d4d3c77","extra":{"source":"61ertong"}},{"album":"嘟拉三字经","language":"CN","tags":"嘟拉学堂,嘟拉三字经","title":"三字经 寒燠均","subTitle":"","imageUrl":"","linkUrl":"https://cdn9003.iflyos.cn/audio/national/sanzijing/9ea935750ffa477e88284a1b56f0ba2a.mp3","id":"9ea935750ffa477e88284a1b56f0ba2a","extra":{"source":"61ertong"}},{"album":"嘟拉新三字经","language":"CN","tags":"嘟拉学堂,嘟拉新三字经","title":"三字经 有连山","subTitle":"","imageUrl":"","linkUrl":"https://cdn9003.iflyos.cn/audio/national/newsanzijing/7e3c66189cce3a71e7fb229f3a34e0ff.mp3","id":"7e3c66189cce3a71e7fb229f3a34e0ff","extra":{"source":"61ertong"}},{"album":"嘟拉新三字经","language":"CN","tags":"嘟拉学堂,嘟拉新三字经","title":"三字经 玉不琢","subTitle":"","imageUrl":"","linkUrl":"https://cdn9003.iflyos.cn/audio/national/newsanzijing/6884bfbf3da1e017eece09ba351782d6.mp3","id":"6884bfbf3da1e017eece09ba351782d6","extra":{"source":"61ertong"}},{"album":"嘟拉三字经","language":"CN","tags":"嘟拉学堂,嘟拉三字经","title":"三字经 匏土革","subTitle":"","imageUrl":"","linkUrl":"https://cdn9003.iflyos.cn/audio/national/sanzijing/01574b3bed3b4c94179893271f10420f.mp3","id":"01574b3bed3b4c94179893271f10420f","extra":{"source":"61ertong"}},{"album":"嘟拉三字经","language":"CN","tags":"嘟拉学堂,嘟拉三字经","title":"三字经 稻粱菽","subTitle":"","imageUrl":"","linkUrl":"https://cdn9003.iflyos.cn/audio/national/sanzijing/8e9e54081d9ef89b4e66f05afaf2d877.mp3","id":"8e9e54081d9ef89b4e66f05afaf2d877","extra":{"source":"61ertong"}},{"album":"嘟拉三字经","language":"CN","tags":"嘟拉学堂,嘟拉三字经","title":"三字经 迨至隋","subTitle":"","imageUrl":"","linkUrl":"https://cdn9003.iflyos.cn/audio/national/sanzijing/186b18352b066bc65504d72ce607194c.mp3","id":"186b18352b066bc65504d72ce607194c","extra":{"source":"61ertong"}},{"album":"嘟拉新三字经","language":"CN","tags":"嘟拉学堂,嘟拉新三字经","title":"三字经 汤伐夏","subTitle":"","imageUrl":"","linkUrl":"https://cdn9003.iflyos.cn/audio/national/newsanzijing/5af820a2eb9e2a920232beb361fc5b6c.mp3","id":"5af820a2eb9e2a920232beb361fc5b6c","extra":{"source":"61ertong"}},{"album":"嘟拉新三字经","language":"CN","tags":"嘟拉学堂,嘟拉新三字经","title":"三字经 匏土革","subTitle":"","imageUrl":"","linkUrl":"https://cdn9003.iflyos.cn/audio/national/newsanzijing/01574b3bed3b4c94179893271f10420f.mp3","id":"01574b3bed3b4c94179893271f10420f","extra":{"source":"61ertong"}}]},"shouldEndSession":true},"nlu":{"input":"找一下三字经","skill":"国学","skillId":"AIUI.chLiterature","skillVersion":"2.0","semantics":{"request":{"entrypoint":"ent","hazard":false,"score":1,"slots":[{"begin":3,"end":6,"name":"name","normValue":"三字经","value":"三字经"}],"template":"{search}{name}"}}},"timestamp":1734664041}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidf7fe4e49@dx03ee1ac02b693eef00"}
连接正常关闭
1734664042
param:b'{\n            "auth_id": "01e50a73b5034c17aa76cd9fd7e92d64",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid80123d13@dx14661ac02b693eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "bf0975516cff472aaa7d7935501d4b01","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bdee9@dx193e208b61cb8aa532"}
started:
ws start
####################
测试进行: ctm000118de@hu17b4fbfdea9020c902#46308980.pcm
{"recordId":"ase000d2f55@hu193e208ccda0427882:bf0975516cff472aaa7d7935501d4b01","requestId":"ase000d2f55@hu193e208ccda0427882","sessionId":"cid000bdee9@dx193e208b61cb8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664048}
{"recordId":"gty000bdeea@dx193e208b826b8aa532:bf0975516cff472aaa7d7935501d4b01","requestId":"gty000bdeea@dx193e208b826b8aa532","sessionId":"cid000bdee9@dx193e208b61cb8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664049}
{"recordId":"gty000bdeea@dx193e208b826b8aa532:bf0975516cff472aaa7d7935501d4b01","requestId":"gty000bdeea@dx193e208b826b8aa532","sessionId":"cid000bdee9@dx193e208b61cb8aa532","eof":"1","text":"我要把人民币兑换成美元","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664049}
send data finished:1734664050417.134
{"recordId":"gty000bdeea@dx193e208b826b8aa532:bf0975516cff472aaa7d7935501d4b01","requestId":"gty000bdeea@dx193e208b826b8aa532","sessionId":"cid000bdee9@dx193e208b61cb8aa532","topic":"dm.output","skill":"汇率","skillId":"2019031800001146","speakUrl":"","error":{},"dm":{"input":"我要把人民币兑换成美元","intentId":"RATE_QUERY","intentName":"汇率查询","nlg":"1人民币是0.137012美元，数据在2024-12-20 10:55:00更新，仅供参考。","shouldEndSession":true},"nlu":{"input":"我要把人民币兑换成美元","skill":"汇率","skillId":"2019031800001146","skillVersion":"65.0","semantics":{"request":{"entrypoint":"ent","hazard":false,"score":1,"slots":[{"begin":3,"end":6,"name":"src","normValue":"CNY","value":"人民币"},{"begin":9,"end":11,"name":"dest","normValue":"USD","value":"美元"}],"template":"{prefix}{src}{conversion}{dest}"}}},"timestamp":1734664049}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cida7e0a43f@dx663e1ac02b713eef00"}
连接正常关闭
1734664050
param:b'{\n            "auth_id": "6ee6a84bb246481bbde952091c8e27a8",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid258173ec@dx72691ac02b713eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "7d5291b7bc6f4912b527a1588e32cf1e","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be172@dx193e208d5727844532"}
started:
ws start
####################
测试进行: ctm0001190a@hu17ba70b828b0212902#47592803.pcm
{"recordId":"gty000be173@dx193e208d74d7844532:7d5291b7bc6f4912b527a1588e32cf1e","requestId":"gty000be173@dx193e208d74d7844532","sessionId":"cid000be172@dx193e208d5727844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664054}
{"recordId":"gty000be173@dx193e208d74d7844532:7d5291b7bc6f4912b527a1588e32cf1e","requestId":"gty000be173@dx193e208d74d7844532","sessionId":"cid000be172@dx193e208d5727844532","eof":"1","text":"音乐关了","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664054}
send data finished:1734664055384.1523
{"recordId":"gty000be173@dx193e208d74d7844532:7d5291b7bc6f4912b527a1588e32cf1e","requestId":"gty000be173@dx193e208d74d7844532","sessionId":"cid000be172@dx193e208d5727844532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"音乐关了","intentId":"INSTRUCTION","intentName":"音乐控制","nlg":"","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"音乐关了","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"操作","value":"close"}]}}},"timestamp":1734664054}
{"recordId":"ase000f1318@hu193e208e6ed05c0882:7d5291b7bc6f4912b527a1588e32cf1e","requestId":"ase000f1318@hu193e208e6ed05c0882","sessionId":"cid000be172@dx193e208d5727844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664054}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid36f86021@dxd6a31ac02b763eef00"}
连接正常关闭
1734664055
param:b'{\n            "auth_id": "cd3cf12350aa477e80e070908e6a45a0",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid7877d727@dx34251ac02b763eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "0a4893f2f86c468cbf1109f961231121","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be176@dx193e208e9267844532"}
started:
ws start
####################
测试进行: ctm0001190e@hu17ba70b83df0212902#47592809.pcm
{"recordId":"ase000e891c@hu193e208fe5705c4882:0a4893f2f86c468cbf1109f961231121","requestId":"ase000e891c@hu193e208fe5705c4882","sessionId":"cid000be176@dx193e208e9267844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664060}
{"recordId":"ase000e8962@hu193e208ff4c05c4882:0a4893f2f86c468cbf1109f961231121","requestId":"ase000e8962@hu193e208ff4c05c4882","sessionId":"cid000be176@dx193e208e9267844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664060}
{"recordId":"gty000be177@dx193e208eade7844532:0a4893f2f86c468cbf1109f961231121","requestId":"gty000be177@dx193e208eade7844532","sessionId":"cid000be176@dx193e208e9267844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664061}
{"recordId":"gty000be177@dx193e208eade7844532:0a4893f2f86c468cbf1109f961231121","requestId":"gty000be177@dx193e208eade7844532","sessionId":"cid000be176@dx193e208e9267844532","eof":"1","text":"收藏现在放的这首歌","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664061}
send data finished:1734664062770.5774
{"recordId":"gty000be177@dx193e208eade7844532:0a4893f2f86c468cbf1109f961231121","requestId":"gty000be177@dx193e208eade7844532","sessionId":"cid000be176@dx193e208e9267844532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"收藏现在放的这首歌","intentId":"chat","intentName":"闲聊","nlg":"想破了我的小脑袋都没想出来，你换个问题吧！","shouldEndSession":true},"nlu":{"input":"收藏现在放的这首歌","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1734664062}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid1f661019@dx0ff31ac02b7e3eef00"}
连接正常关闭
1734664062
param:b'{\n            "auth_id": "ea968fe9e11e41ad8e95bc01442c280f",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid21426f31@dxfd871ac02b7e3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "0d48ab8c539341d6a56956e181dd2333","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bdc3b@dx193e20907e5b86a532"}
started:
ws start
####################
测试进行: ctm00011911@hu17ba70b88b50212902#47592812.pcm
{"recordId":"ase000ea9e1@hu193e2091dc005bf882:0d48ab8c539341d6a56956e181dd2333","requestId":"ase000ea9e1@hu193e2091dc005bf882","sessionId":"cid000bdc3b@dx193e20907e5b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664068}
{"recordId":"gty000bdc3c@dx193e20909cfb86a532:0d48ab8c539341d6a56956e181dd2333","requestId":"gty000bdc3c@dx193e20909cfb86a532","sessionId":"cid000bdc3b@dx193e20907e5b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664068}
{"recordId":"gty000bdc3c@dx193e20909cfb86a532:0d48ab8c539341d6a56956e181dd2333","requestId":"gty000bdc3c@dx193e20909cfb86a532","sessionId":"cid000bdc3b@dx193e20907e5b86a532","eof":"1","text":"播放冯巩的相声","pinyin":"","topic":"asr.speech.result","age":"elder","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664068}
send data finished:1734664069701.6482
{"recordId":"gty000bdc3c@dx193e20909cfb86a532:0d48ab8c539341d6a56956e181dd2333","requestId":"gty000bdc3c@dx193e20909cfb86a532","sessionId":"cid000bdc3b@dx193e20907e5b86a532","topic":"dm.output","skill":"相声小品","skillId":"crossTalk","speakUrl":"","error":{},"dm":{"input":"播放冯巩的相声","intentId":"QUERY","intentName":"查询","nlg":"请欣赏相声4.乡音总关情之大话美食","widget":{"content":[{"album":"冯巩经典相声","title":"4.乡音总关情之大话美食","subTitle":"大喜聆音","imageUrl":"http://img2.sycdn.kuwo.cn/star/albumcover/240/s3s59/65/855404826.jpg","linkUrl":"https://lv-sycdn.kuwo.cn/c076b1161229d17b280e5f3fa21cfc14/6764df85/resource/30106/trackmedia/long/C200003X8mO13GKucN.m4a","id":"352080378","extra":{"source":"kuwotingshu"}}]},"shouldEndSession":true},"nlu":{"input":"播放冯巩的相声","skill":"相声小品","skillId":"crossTalk","skillVersion":"19.0","semantics":{"request":{"slots":[{"name":"actor","value":"冯巩"},{"name":"category","value":"相声"}]}}},"timestamp":1734664069}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cide3d1b33d@dxb8961ac02b853eef00"}
连接正常关闭
1734664069
param:b'{\n            "auth_id": "51641c7d2c074058903ed7cca54f78ee",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid4a387830@dx2c1a1ac02b853eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "4abd0d94daf94d01ad451eaa13ea5914","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bdf07@dx193e209217db8aa532"}
started:
ws start
####################
测试进行: ctm00011914@hu17ba70b8e090212902#47592826.pcm
{"recordId":"ase000eafad@hu193e209360e05bf882:4abd0d94daf94d01ad451eaa13ea5914","requestId":"ase000eafad@hu193e209360e05bf882","sessionId":"cid000bdf07@dx193e209217db8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664074}
{"recordId":"gty000bdf08@dx193e2092384b8aa532:4abd0d94daf94d01ad451eaa13ea5914","requestId":"gty000bdf08@dx193e2092384b8aa532","sessionId":"cid000bdf07@dx193e209217db8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664074}
{"recordId":"gty000bdf08@dx193e2092384b8aa532:4abd0d94daf94d01ad451eaa13ea5914","requestId":"gty000bdf08@dx193e2092384b8aa532","sessionId":"cid000bdf07@dx193e209217db8aa532","eof":"1","text":"故事讲个故事吧","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664074}
send data finished:1734664075850.8972
{"recordId":"gty000bdf08@dx193e2092384b8aa532:4abd0d94daf94d01ad451eaa13ea5914","requestId":"gty000bdf08@dx193e2092384b8aa532","sessionId":"cid000bdf07@dx193e209217db8aa532","topic":"dm.output","skill":"故事","skillId":"2019031500001010","speakUrl":"","error":{},"dm":{"input":"故事讲个故事吧","intentId":"RANDOM_QUERY","intentName":"随机播放","nlg":"请欣赏故事【橘子-植物】。","widget":{"content":[{"album":"嘟拉小故事","title":"橘子-植物","subTitle":"","linkUrl":"https://cdn9003.iflyos.cn/audio/story/small/282536.mp3","id":"282536","extra":{"source":"61ertong"}},{"album":"嘟拉动物故事","title":"乖乖猴的笑话_照相","subTitle":"","linkUrl":"https://cdn9003.iflyos.cn/audio/story/animal/282408.mp3","id":"282408","extra":{"source":"61ertong"}},{"album":"嘟拉益智故事","title":"讲卫生的嘟拉","subTitle":"","linkUrl":"https://cdn9003.iflyos.cn/audio/story/puzzle/279325.mp3","id":"279325","extra":{"source":"61ertong"}},{"album":"嘟拉童话故事","title":"水果音乐厅","subTitle":"","linkUrl":"https://cdn9003.iflyos.cn/audio/story/tonghua/559651012ef945aa617871209aa3f8a9.mp3","id":"279880","extra":{"source":"61ertong"}},{"album":"嘟拉动物故事","title":"袋鼠-动物","subTitle":"","linkUrl":"https://cdn9003.iflyos.cn/audio/story/animal/282562.mp3","id":"282562","extra":{"source":"61ertong"}},{"album":"嘟拉童话故事","title":"小公主","subTitle":"","linkUrl":"https://cdn9003.iflyos.cn/audio/story/tonghua/281076.mp3","id":"281076","extra":{"source":"61ertong"}},{"album":"嘟拉动物故事","title":"山中无老虎，猴子称大王","subTitle":"","linkUrl":"https://cdn9003.iflyos.cn/audio/story/animal/c14a0a534128ce4ed8e41c5e72736615.mp3","id":"279112","extra":{"source":"61ertong"}},{"album":"嘟拉小故事","title":"皮克的故事_第十讲","subTitle":"","linkUrl":"https://cdn9003.iflyos.cn/audio/story/small/282136.mp3","id":"282136","extra":{"source":"61ertong"}},{"album":"嘟拉成语故事","title":"翻然改图","subTitle":"","linkUrl":"https://cdn9003.iflyos.cn/audio/story/poetry/282792.mp3","id":"282792","extra":{"source":"61ertong"}},{"album":"嘟拉童话故事","title":"一个非常有用的人","subTitle":"","linkUrl":"https://cdn9003.iflyos.cn/audio/story/tonghua/282228.mp3","id":"282228","extra":{"source":"61ertong"}}]},"shouldEndSession":true},"nlu":{"input":"故事讲个故事吧","skill":"故事","skillId":"2019031500001010","skillVersion":"55","semantics":{"request":{"slots":[]}}},"timestamp":1734664075}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid741b6d13@dx38ac1ac02b8b3eef00"}
连接正常关闭
1734664075
param:b'{\n            "auth_id": "18173f004827484996c7fa2058edbf27",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidf6815fc3@dx6ed71ac02b8b3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "a13a21a45c0243818dcda880173c697b","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bdf0d@dx193e20938efb8aa532"}
started:
ws start
####################
测试进行: ctm00011919@hu17ba70b8fa30212902#47592831.pcm
{"recordId":"ase000e8a30@hu193e2094a5505c3882:a13a21a45c0243818dcda880173c697b","requestId":"ase000e8a30@hu193e2094a5505c3882","sessionId":"cid000bdf0d@dx193e20938efb8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664080}
{"recordId":"gty000bdf0e@dx193e2093b7cb8aa532:a13a21a45c0243818dcda880173c697b","requestId":"gty000bdf0e@dx193e2093b7cb8aa532","sessionId":"cid000bdf0d@dx193e20938efb8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664081}
{"recordId":"gty000bdf0e@dx193e2093b7cb8aa532:a13a21a45c0243818dcda880173c697b","requestId":"gty000bdf0e@dx193e2093b7cb8aa532","sessionId":"cid000bdf0d@dx193e20938efb8aa532","eof":"1","text":"请你给我讲个故事好吗","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664081}
send data finished:1734664082421.3613
{"recordId":"gty000bdf0e@dx193e2093b7cb8aa532:a13a21a45c0243818dcda880173c697b","requestId":"gty000bdf0e@dx193e2093b7cb8aa532","sessionId":"cid000bdf0d@dx193e20938efb8aa532","topic":"dm.output","skill":"故事","skillId":"2019031500001010","speakUrl":"","error":{},"dm":{"input":"请你给我讲个故事好吗","intentId":"RANDOM_QUERY","intentName":"随机播放","nlg":"请欣赏故事【半途而废】。","widget":{"content":[{"album":"嘟拉成语故事","title":"半途而废","subTitle":"","linkUrl":"https://cdn9003.iflyos.cn/audio/story/poetry/282824.mp3","id":"282824","extra":{"source":"61ertong"}},{"album":"嘟拉成语故事","title":"鸦雀无声","subTitle":"","linkUrl":"https://cdn9003.iflyos.cn/audio/story/poetry/279956.mp3","id":"279956","extra":{"source":"61ertong"}},{"album":"嘟拉睡前故事","title":"三头小猪","subTitle":"","linkUrl":"https://cdn9003.iflyos.cn/audio/story/sleep/282012.mp3","id":"282012","extra":{"source":"61ertong"}},{"album":"嘟拉小故事","title":"快乐模仿","subTitle":"","linkUrl":"https://cdn9003.iflyos.cn/audio/story/small/5f414fbbff6fc672dddc8d3f2602e3e0.mp3","id":"279520","extra":{"source":"61ertong"}},{"album":"嘟拉童话故事","title":"贪吃星星的妖怪","subTitle":"","linkUrl":"https://cdn9003.iflyos.cn/audio/story/tonghua/8d26ccd7e8a4e564f4df9290eb869d15.mp3","id":"280330","extra":{"source":"61ertong"}},{"album":"嘟拉动物故事","title":"坐收渔翁之利的乌龟","subTitle":"","linkUrl":"https://cdn9003.iflyos.cn/audio/story/animal/520cd6c91ae5fd077199f3657c453d62.mp3","id":"279121","extra":{"source":"61ertong"}},{"album":"嘟拉动物故事","title":"放牛娃","subTitle":"","linkUrl":"https://cdn9003.iflyos.cn/audio/story/animal/283152.mp3","id":"283152","extra":{"source":"61ertong"}},{"album":"嘟拉益智故事","title":"《红楼梦》曹雪芹创作","subTitle":"","linkUrl":"https://cdn9003.iflyos.cn/audio/story/puzzle/280649.mp3","id":"280649","extra":{"source":"61ertong"}},{"album":"嘟拉童话故事","title":"呆子伊凡","subTitle":"","linkUrl":"https://cdn9003.iflyos.cn/audio/story/tonghua/281060.mp3","id":"281060","extra":{"source":"61ertong"}},{"album":"嘟拉动物故事","title":"壮蚊子和瘦蚊子","subTitle":"","linkUrl":"https://cdn9003.iflyos.cn/audio/story/animal/2e946f3a8191f4449fb3501987381395.mp3","id":"279132","extra":{"source":"61ertong"}}]},"shouldEndSession":true},"nlu":{"input":"请你给我讲个故事好吗","skill":"故事","skillId":"2019031500001010","skillVersion":"55","semantics":{"request":{"slots":[]}}},"timestamp":1734664081}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid465a654d@dx53901ac02b913eef00"}
连接正常关闭
1734664082
param:b'{\n            "auth_id": "69d95299ba1a4761ad4000b94adeb822",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidc7c65ac8@dx45091ac02b913eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "2d0858c9225f42079e2bcc2fa88c7cf3","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be17c@dx193e20952637844532"}
started:
ws start
####################
测试进行: ctm0001191d@hu17ba70b91a70212902#47592834.pcm
{"recordId":"ase000ea286@hu193e209660805c4882:2d0858c9225f42079e2bcc2fa88c7cf3","requestId":"ase000ea286@hu193e209660805c4882","sessionId":"cid000be17c@dx193e20952637844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664087}
{"recordId":"ase000ebbb4@hu193e209671205bf882:2d0858c9225f42079e2bcc2fa88c7cf3","requestId":"ase000ebbb4@hu193e209671205bf882","sessionId":"cid000be17c@dx193e20952637844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664087}
{"recordId":"gty000be17d@dx193e20956cd7844532:2d0858c9225f42079e2bcc2fa88c7cf3","requestId":"gty000be17d@dx193e20956cd7844532","sessionId":"cid000be17c@dx193e20952637844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664087}
{"recordId":"gty000be17d@dx193e20956cd7844532:2d0858c9225f42079e2bcc2fa88c7cf3","requestId":"gty000be17d@dx193e20956cd7844532","sessionId":"cid000be17c@dx193e20952637844532","eof":"1","text":"我想听相声","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664087}
send data finished:1734664088391.4414
{"recordId":"gty000be17d@dx193e20956cd7844532:2d0858c9225f42079e2bcc2fa88c7cf3","requestId":"gty000be17d@dx193e20956cd7844532","sessionId":"cid000be17c@dx193e20952637844532","topic":"dm.output","skill":"相声小品","skillId":"crossTalk","speakUrl":"","error":{},"dm":{"input":"我想听相声","intentId":"QUERY","intentName":"查询","nlg":"为您找到相声 教子胡同","widget":{"content":[{"album":"郭德纲单口相声全集","title":"教子胡同","subTitle":"郭德纲","imageUrl":"http://img2.sycdn.kuwo.cn/star/albumcover/240/44/20/3032701279.jpg","linkUrl":"https://no-sycdn.kuwo.cn/e38159e7ad29d6e5f57e880196539ac1/6764df97/resource/a2/41/11/1908807080.aac","id":"47536359","extra":{"source":"kuwotingshu"}}]},"shouldEndSession":true},"nlu":{"input":"我想听相声","skill":"相声小品","skillId":"crossTalk","skillVersion":"19.0","semantics":{"request":{"slots":[{"name":"category","value":"相声"}]}}},"timestamp":1734664087}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid8fd7fe99@dx38111ac02b973eef00"}
连接正常关闭
1734664088
param:b'{\n            "auth_id": "8bc50c3d84c944dcbd5b5f1b466a3176",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidb4d4331e@dx35941ac02b973eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "16375eec026e4d8597a4fbb7fb090664","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bdf15@dx193e2096a1eb8aa532"}
started:
ws start
####################
测试进行: ctm0001191f@hu17ba70b93b50212902#47592838.pcm
{"recordId":"ase000ec116@hu193e2097c8c05bf882:16375eec026e4d8597a4fbb7fb090664","requestId":"ase000ec116@hu193e2097c8c05bf882","sessionId":"cid000bdf15@dx193e2096a1eb8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664092}
{"recordId":"gty000bdf16@dx193e2096ca9b8aa532:16375eec026e4d8597a4fbb7fb090664","requestId":"gty000bdf16@dx193e2096ca9b8aa532","sessionId":"cid000bdf15@dx193e2096a1eb8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664093}
{"recordId":"gty000bdf16@dx193e2096ca9b8aa532:16375eec026e4d8597a4fbb7fb090664","requestId":"gty000bdf16@dx193e2096ca9b8aa532","sessionId":"cid000bdf15@dx193e2096a1eb8aa532","eof":"1","text":"我要听相声","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664093}
send data finished:1734664094132.447
{"recordId":"gty000bdf16@dx193e2096ca9b8aa532:16375eec026e4d8597a4fbb7fb090664","requestId":"gty000bdf16@dx193e2096ca9b8aa532","sessionId":"cid000bdf15@dx193e2096a1eb8aa532","topic":"dm.output","skill":"相声小品","skillId":"crossTalk","speakUrl":"","error":{},"dm":{"input":"我要听相声","intentId":"QUERY","intentName":"查询","nlg":"我这有一个 常连安-大师兄闹衙门02","widget":{"content":[{"album":"经典单口相声大全|名家经典","title":"常连安-大师兄闹衙门02","subTitle":"华策影视官方","imageUrl":"http://img2.sycdn.kuwo.cn/star/albumcover/240/s4s64/74/1257242999.png","linkUrl":"https://lv-sycdn.kuwo.cn/1688a81368c369f913178f9ab5ad86db/6764df9d/resource/30106/trackmedia/long/C200002qsR3q0uEaKI.m4a","id":"354176785","extra":{"source":"kuwotingshu"}}]},"shouldEndSession":true},"nlu":{"input":"我要听相声","skill":"相声小品","skillId":"crossTalk","skillVersion":"19.0","semantics":{"request":{"slots":[{"name":"category","value":"相声"}]}}},"timestamp":1734664093}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid6fdd6352@dx67da1ac02b9d3eef00"}
连接正常关闭
1734664094
param:b'{\n            "auth_id": "a0db8e6fc6a741fcb58885ec9c6d7788",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid39af510e@dxfbfe1ac02b9d3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "78a7ab2ffc25414f80cc62da60a7e18c","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be188@dx193e20980d17844532"}
started:
ws start
####################
测试进行: ctm00011923@hu17ba70b96310212902#47592841.pcm
{"recordId":"ase000e9b00@hu193e2098ec805c3882:78a7ab2ffc25414f80cc62da60a7e18c","requestId":"ase000e9b00@hu193e2098ec805c3882","sessionId":"cid000be188@dx193e20980d17844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664097}
{"recordId":"gty000be189@dx193e20982a87844532:78a7ab2ffc25414f80cc62da60a7e18c","requestId":"gty000be189@dx193e20982a87844532","sessionId":"cid000be188@dx193e20980d17844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664099}
{"recordId":"gty000be189@dx193e20982a87844532:78a7ab2ffc25414f80cc62da60a7e18c","requestId":"gty000be189@dx193e20982a87844532","sessionId":"cid000be188@dx193e20980d17844532","eof":"1","text":"音量太大看小说共进午餐","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664099}
send data finished:1734664100339.802
{"recordId":"gty000be189@dx193e20982a87844532:78a7ab2ffc25414f80cc62da60a7e18c","requestId":"gty000be189@dx193e20982a87844532","sessionId":"cid000be188@dx193e20980d17844532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"音量太大看小说共进午餐","intentId":"chat","intentName":"闲聊","nlg":"我就是个机器人，这个问题也太难啦。你简单点说呗！","shouldEndSession":true},"nlu":{"input":"音量太大看小说共进午餐","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1734664099}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid49a20ba3@dx3c1e1ac02ba33eef00"}
连接正常关闭
1734664100
param:b'{\n            "auth_id": "33edf51c7a2947248544373aec2541f6",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidcfffa8ac@dxe1d21ac02ba33eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "a181fd4e5226453fa4ee9b7d6204db1e","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bdc5c@dx193e20998e4b86a532"}
started:
ws start
####################
测试进行: ctm00011927@hu17ba70b96f40212902#47592845.pcm
{"recordId":"ase000f4503@hu193e209b41205c0882:a181fd4e5226453fa4ee9b7d6204db1e","requestId":"ase000f4503@hu193e209b41205c0882","sessionId":"cid000bdc5c@dx193e20998e4b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664107}
{"recordId":"gty000bdc5d@dx193e2099afab86a532:a181fd4e5226453fa4ee9b7d6204db1e","requestId":"gty000bdc5d@dx193e2099afab86a532","sessionId":"cid000bdc5c@dx193e20998e4b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664108}
{"recordId":"gty000bdc5d@dx193e2099afab86a532:a181fd4e5226453fa4ee9b7d6204db1e","requestId":"gty000bdc5d@dx193e2099afab86a532","sessionId":"cid000bdc5c@dx193e20998e4b86a532","eof":"1","text":"打开正衣取热","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664108}
send data finished:1734664109252.106
{"recordId":"gty000bdc5d@dx193e2099afab86a532:a181fd4e5226453fa4ee9b7d6204db1e","requestId":"gty000bdc5d@dx193e2099afab86a532","sessionId":"cid000bdc5c@dx193e20998e4b86a532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"打开正衣取热","intentId":"chat","intentName":"闲聊","nlg":"这个我真不懂，换个我懂的呗。","shouldEndSession":true},"nlu":{"input":"打开正衣取热","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1734664108}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidc8fc2e05@dx4edf1ac02bac3eef00"}
连接正常关闭
1734664109
param:b'{\n            "auth_id": "130610217ef4413e89b4ee8a151daf91",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidf40ff8b0@dx632d1ac02bac3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "53c13e53261841d18f7cd21e26b1e2fd","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bdf1d@dx193e209bbccb8aa532"}
started:
ws start
####################
测试进行: ctm00011929@hu17ba70b99510212902#47592848.pcm
{"recordId":"ase000ee6b8@hu193e209cd3f1323882:53c13e53261841d18f7cd21e26b1e2fd","requestId":"ase000ee6b8@hu193e209cd3f1323882","sessionId":"cid000bdf1d@dx193e209bbccb8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664113}
{"recordId":"gty000bdf1e@dx193e209bda5b8aa532:53c13e53261841d18f7cd21e26b1e2fd","requestId":"gty000bdf1e@dx193e209bda5b8aa532","sessionId":"cid000bdf1d@dx193e209bbccb8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664116}
{"recordId":"gty000bdf1e@dx193e209bda5b8aa532:53c13e53261841d18f7cd21e26b1e2fd","requestId":"gty000bdf1e@dx193e209bda5b8aa532","sessionId":"cid000bdf1d@dx193e209bbccb8aa532","eof":"1","text":"直接查下共进午餐智能小说","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664116}
send data finished:1734664117051.5288
{"recordId":"gty000bdf1e@dx193e209bda5b8aa532:53c13e53261841d18f7cd21e26b1e2fd","requestId":"gty000bdf1e@dx193e209bda5b8aa532","sessionId":"cid000bdf1d@dx193e209bbccb8aa532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"直接查下共进午餐智能小说","intentId":"chat","intentName":"闲聊","nlg":"想破了我的小脑袋都没想出来，你换个问题吧！","shouldEndSession":true},"nlu":{"input":"直接查下共进午餐智能小说","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1734664116}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid48b76bc0@dx0c8d1ac02bb43eef00"}
连接正常关闭
1734664117
param:b'{\n            "auth_id": "148d7540541348fe957b714dc81006b7",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidcbc5241d@dx48251ac02bb43eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "c7b8dec305db44d5b7c39d6a5e93e79f","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be196@dx193e209da837844532"}
started:
ws start
####################
测试进行: ctm0001192b@hu17ba70b99bb0212902#47592850.pcm
{"recordId":"ase000f5339@hu193e209ee5705c0882:c7b8dec305db44d5b7c39d6a5e93e79f","requestId":"ase000f5339@hu193e209ee5705c0882","sessionId":"cid000be196@dx193e209da837844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664122}
{"recordId":"gty000be197@dx193e209dc5f7844532:c7b8dec305db44d5b7c39d6a5e93e79f","requestId":"gty000be197@dx193e209dc5f7844532","sessionId":"cid000be196@dx193e209da837844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664125}
{"recordId":"gty000be197@dx193e209dc5f7844532:c7b8dec305db44d5b7c39d6a5e93e79f","requestId":"gty000be197@dx193e209dc5f7844532","sessionId":"cid000be196@dx193e209da837844532","eof":"1","text":"尽管测试怎么造这个多少钱呢","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664125}
send data finished:1734664126784.2595
{"recordId":"gty000be197@dx193e209dc5f7844532:c7b8dec305db44d5b7c39d6a5e93e79f","requestId":"gty000be197@dx193e209dc5f7844532","sessionId":"cid000be196@dx193e209da837844532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"尽管测试怎么造这个多少钱呢","intentId":"chat","intentName":"闲聊","nlg":"你成功难住我啦，换个简单的问我吧。","shouldEndSession":true},"nlu":{"input":"尽管测试怎么造这个多少钱呢","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1734664126}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidf5af788f@dx642e1ac02bbe3eef00"}
连接正常关闭
1734664127
param:b'{\n            "auth_id": "5af234098a6c457dba3b540b2411b921",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid29e1f9ab@dx7c771ac02bbe3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "1bd2db15fec74b68870fd7bbbcbdc37d","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be2f8@dx193e20a0062b8a9532"}
started:
ws start
####################
测试进行: ctm00011cc9@hu17b4cb4fc0f0212902#46275220.pcm
{"recordId":"ase000ee5c2@hu193e20a10a005bf882:1bd2db15fec74b68870fd7bbbcbdc37d","requestId":"ase000ee5c2@hu193e20a10a005bf882","sessionId":"cid000be2f8@dx193e20a0062b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664130}
Error reading file[Errno 2] No such file or directory: 'data/silence.wav'
{"action":"error","code":"20010108","data":"","desc":"{\"code\":20010108,\"sub\":\"\"}","sid":"gty000be2f9@dx193e20a0254b8a9532"}
{'action': 'error', 'code': '20010108', 'data': '', 'desc': '{"code":20010108,"sub":""}', 'sid': 'gty000be2f9@dx193e20a0254b8a9532'}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid2381aaf8@dxbf941ac02bd83eef00"}
连接正常关闭
1734664153
param:b'{\n            "auth_id": "2547ada6e34e432ab81b6de11ebf25a0",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid5e2f35d8@dx43391ac02bd83eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "749803aa5d8842b19ce78422efc91cff","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bdc9f@dx193e20a67ffb86a532"}
started:
ws start
####################
测试进行: ctm00011cd1@hu17b4cb5006c0212902#46275231.pcm
{"recordId":"ase000efe9d@hu193e20a76d305bf882:749803aa5d8842b19ce78422efc91cff","requestId":"ase000efe9d@hu193e20a76d305bf882","sessionId":"cid000bdc9f@dx193e20a67ffb86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664157}
{"recordId":"ase000ee5e9@hu193e20a77c005c4882:749803aa5d8842b19ce78422efc91cff","requestId":"ase000ee5e9@hu193e20a77c005c4882","sessionId":"cid000bdc9f@dx193e20a67ffb86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664157}
{"recordId":"gty000bdca0@dx193e20a69feb86a532:749803aa5d8842b19ce78422efc91cff","requestId":"gty000bdca0@dx193e20a69feb86a532","sessionId":"cid000bdc9f@dx193e20a67ffb86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664158}
{"recordId":"gty000bdca0@dx193e20a69feb86a532:749803aa5d8842b19ce78422efc91cff","requestId":"gty000bdca0@dx193e20a69feb86a532","sessionId":"cid000bdc9f@dx193e20a67ffb86a532","eof":"1","text":"39度算高烧吗","pinyin":"","topic":"asr.speech.result","age":"elder","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664158}
send data finished:1734664159391.9043
{"recordId":"gty000bdca0@dx193e20a69feb86a532:749803aa5d8842b19ce78422efc91cff","requestId":"gty000bdca0@dx193e20a69feb86a532","sessionId":"cid000bdc9f@dx193e20a67ffb86a532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"39度算高烧吗","intentId":"chat","intentName":"闲聊","nlg":"这个我真不懂，换个我懂的呗。","shouldEndSession":true},"nlu":{"input":"39度算高烧吗","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1734664158}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid03756478@dx149b1ac02bde3eef00"}
连接正常关闭
1734664159
param:b'{\n            "auth_id": "bceb870361f441dea4f00d8897b5f29b",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid0650b94c@dxe6f11ac02bdf3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "5e6a863172344883821819a059becb24","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bdca5@dx193e20a7ff2b86a532"}
started:
ws start
####################
测试进行: ctm00011ce1@hu17b4cb506bb0212902#46275245.pcm
{"recordId":"ase000f8672@hu193e20a90a105c2882:5e6a863172344883821819a059becb24","requestId":"ase000f8672@hu193e20a90a105c2882","sessionId":"cid000bdca5@dx193e20a7ff2b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664163}
{"recordId":"ase000eda76@hu193e20a910005c3882:5e6a863172344883821819a059becb24","requestId":"ase000eda76@hu193e20a910005c3882","sessionId":"cid000bdca5@dx193e20a7ff2b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664163}
{"recordId":"gty000bdca6@dx193e20a81f5b86a532:5e6a863172344883821819a059becb24","requestId":"gty000bdca6@dx193e20a81f5b86a532","sessionId":"cid000bdca5@dx193e20a7ff2b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664164}
{"recordId":"gty000bdca6@dx193e20a81f5b86a532:5e6a863172344883821819a059becb24","requestId":"gty000bdca6@dx193e20a81f5b86a532","sessionId":"cid000bdca5@dx193e20a7ff2b86a532","eof":"1","text":"蜜蜂是怎么叫的","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664164}
send data finished:1734664165399.5955
{"recordId":"gty000bdca6@dx193e20a81f5b86a532:5e6a863172344883821819a059becb24","requestId":"gty000bdca6@dx193e20a81f5b86a532","sessionId":"cid000bdca5@dx193e20a7ff2b86a532","topic":"dm.output","skill":"动物叫声","skillId":"IFLYTEK.animalCries","speakUrl":"","error":{},"dm":{"input":"蜜蜂是怎么叫的","intentId":"PLAY","intentName":"播放","nlg":"它是这样叫的","widget":{"content":[{"type":"蜜蜂,蜂","title":"蜜蜂","tags":"","linkUrl":"http://aiui.storage.iflyresearch.com/ctimusic/animalCries/a1babcd5478d9678e07119b140ad77bdmifeng.mp3","extra":{"source":"iflytek"}}]},"shouldEndSession":true},"nlu":{"input":"蜜蜂是怎么叫的","skill":"动物叫声","skillId":"IFLYTEK.animalCries","skillVersion":"7.0","semantics":{"request":{"slots":[{"name":"name","value":"蜜蜂"}]}}},"timestamp":1734664164}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid979e1d94@dxce611ac02be43eef00"}
连接正常关闭
1734664165
param:b'{\n            "auth_id": "8378dd94639643e9b64d006c7ff897fb",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid76a0c3d2@dx1d1f1ac02be43eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "0563c8f5d9d94359a381b0a8d03ed5bb","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be30c@dx193e20a96bfb8a9532"}
started:
ws start
####################
测试进行: ctm00011eac@hu17ba9ed6f10020c902#47643167.pcm
{"recordId":"gty000be30d@dx193e20a989cb8a9532:0563c8f5d9d94359a381b0a8d03ed5bb","requestId":"gty000be30d@dx193e20a989cb8a9532","sessionId":"cid000be30c@dx193e20a96bfb8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664169}
{"recordId":"gty000be30d@dx193e20a989cb8a9532:0563c8f5d9d94359a381b0a8d03ed5bb","requestId":"gty000be30d@dx193e20a989cb8a9532","sessionId":"cid000be30c@dx193e20a96bfb8a9532","eof":"1","text":"播新闻","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664169}
send data finished:1734664170641.084
{"recordId":"ase000f818c@hu193e20aa96905c0882:0563c8f5d9d94359a381b0a8d03ed5bb","requestId":"ase000f818c@hu193e20aa96905c0882","sessionId":"cid000be30c@dx193e20a96bfb8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664169}
{"recordId":"gty000be30d@dx193e20a989cb8a9532:0563c8f5d9d94359a381b0a8d03ed5bb","requestId":"gty000be30d@dx193e20a989cb8a9532","sessionId":"cid000be30c@dx193e20a96bfb8a9532","topic":"dm.output","skill":"新闻","skillId":"2019031900001180","speakUrl":"","error":{},"dm":{"input":"播新闻","intentId":"PLAY","intentName":"听新闻","nlg":"可以了解下《小小的我》预售破4000万","widget":{"content":[{"album":"新闻","title":"《小小的我》预售破4000万","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2024122010427CDFF04011E67C470EDFC5D7A48B7958_64.mp3?pf=OH9GI&vid=11654253&tm=1734664170015&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"蜜雪冰城也坐不住了?涨价引发热议","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2024122010226C55C932DDBE8856E9B78D5816530C8B_64.mp3?pf=OH9GI&vid=11654190&tm=1734664170016&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"韩国年轻人“特种兵旅游”挤满上海","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2024122010059487F99E77BECF3C287E27C1C10DEA29_64.mp3?pf=OH9GI&vid=11654187&tm=1734664170016&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"填补空白，我国研制首批高端仪器装备计量测评装置﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/20241220095756B035800BA0A0241F9A250B7580D35A_64.mp3?pf=OH9GI&vid=11654186&tm=1734664170016&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"中新网评余华英被判死刑：打拐是全社会共同的责任﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202412200955D8B369C5A2D62C4868C3779EFCFCEA71_64.mp3?pf=OH9GI&vid=11654183&tm=1734664170016&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"普京称俄愿在乌克兰问题上妥协﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202412200955EF2BDA48A1FD1B105129754E4EFD7C63_64.mp3?pf=OH9GI&vid=11654178&tm=1734664170016&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"立法禁止未成年人使用社媒可行吗，多国考虑立法禁止未成年人使用社媒","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202412200945F4C7A02FD7915C0BBC60F3C3A83E9EF9_64.mp3?pf=OH9GI&vid=11654169&tm=1734664170016&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"美国防部：美国在叙利亚的驻军人数已增加一倍以上","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202412200837E34D4791DF50F60F019C2456FC3E878D_64.mp3?pf=OH9GI&vid=11654154&tm=1734664170016&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"12月20日人本生活资讯早报：死刑！余华英重审二审宣判；反向出游，年轻人热衷出国过年；多地官宣将“取消公摊”","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2024121920199619187C8F69C0F3863DE017C9353AA8_64.mp3?pf=OH9GI&vid=11653528&tm=1734664170016&pid=1898253","extra":{"source":""}},{"album":"新闻","title":"智汇评|多地官宣将取消公摊，购房成本会降吗？","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/20241219155123CB88DF00F8D4398EEC7B7B37BB414A_64.mp3?pf=OH9GI&vid=11653235&tm=1734664170016&pid=576511","extra":{"source":"fm"}}]},"shouldEndSession":true},"nlu":{"input":"播新闻","skill":"新闻","skillId":"2019031900001180","skillVersion":"87","semantics":{"request":{"slots":[]}}},"timestamp":1734664170}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid63127f05@dxe0cc1ac02bea3eef00"}
连接正常关闭
1734664170
param:b'{\n            "auth_id": "17f0baa0a6d944a4b59f5b8b984ef69e",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidf42d951e@dx25031ac02bea3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "de5e7dffdc0e4a069ccc7c3de13cec45","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be310@dx193e20aabeab8a9532"}
started:
ws start
####################
测试进行: ctm00011eae@hu17ba9ed6f2f020c902#47643170.pcm
{"recordId":"ase000ee587@hu193e20abcd805c3882:de5e7dffdc0e4a069ccc7c3de13cec45","requestId":"ase000ee587@hu193e20abcd805c3882","sessionId":"cid000be310@dx193e20aabeab8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664174}
{"recordId":"gty000be313@dx193e20aadd6b8a9532:de5e7dffdc0e4a069ccc7c3de13cec45","requestId":"gty000be313@dx193e20aadd6b8a9532","sessionId":"cid000be310@dx193e20aabeab8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664175}
{"recordId":"gty000be313@dx193e20aadd6b8a9532:de5e7dffdc0e4a069ccc7c3de13cec45","requestId":"gty000be313@dx193e20aadd6b8a9532","sessionId":"cid000be310@dx193e20aabeab8a9532","eof":"1","text":"随便来个笑话","pinyin":"","topic":"asr.speech.result","age":"elder","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664175}
send data finished:1734664176393.6758
{"recordId":"gty000be313@dx193e20aadd6b8a9532:de5e7dffdc0e4a069ccc7c3de13cec45","requestId":"gty000be313@dx193e20aadd6b8a9532","sessionId":"cid000be310@dx193e20aabeab8a9532","topic":"dm.output","skill":"旧版笑话","skillId":"2019032500000002","speakUrl":"","error":{},"dm":{"input":"随便来个笑话","intentId":"QUERY","intentName":"笑话点播","nlg":"那我就说个【全家都很穷】笑话。","widget":{"content":[{"source":"微博冷笑话","title":"全家都很穷","text":"","author":"笑话大王","linkUrl":"http://aiui.storage.iflyresearch.com/ctimusic/joke/-8201910665849706368.mp3","extra":{"resType":"mp3"}},{"source":"微博冷笑话","title":"我爸爸是省长","text":"","author":"笑话大王","linkUrl":"http://aiui.storage.iflyresearch.com/ctimusic/joke/7401311861037778511.mp3","extra":{"resType":"mp3"}},{"source":"微博冷笑话","title":"狗和猫","text":"","author":"笑话大王","linkUrl":"http://aiui.storage.iflyresearch.com/ctimusic/joke/8095141519541643922.mp3","extra":{"resType":"mp3"}},{"source":"微博冷笑话","title":"汤没法喝了","text":"","author":"笑话大王","linkUrl":"http://aiui.storage.iflyresearch.com/ctimusic/joke/4904051971816548440.mp3","extra":{"resType":"mp3"}},{"source":"微博冷笑话","title":"番茄逛街","text":"","author":"笑话大王","linkUrl":"http://aiui.storage.iflyresearch.com/ctimusic/joke/1525536782371369451.mp3","extra":{"resType":"mp3"}},{"source":"微博冷笑话","title":"历史很差","text":"","author":"笑话大王","linkUrl":"http://aiui.storage.iflyresearch.com/ctimusic/joke/-6464898664573155295.mp3","extra":{"resType":"mp3"}},{"source":"幽默笑话大全","title":"远离谎言","text":"","author":"纪小城","linkUrl":"http://aiui.storage.iflyresearch.com/ctimusic/joke/-6817159164516123913.mp3","extra":{"resType":"mp3"}},{"source":"微博冷笑话","title":"自讨没趣","text":"","author":"笑话大王","linkUrl":"http://aiui.storage.iflyresearch.com/ctimusic/joke/72137444674126657.mp3","extra":{"resType":"mp3"}},{"source":"微博冷笑话","title":"七个小矮人","text":"","author":"笑话大王","linkUrl":"http://aiui.storage.iflyresearch.com/ctimusic/joke/-2088648609797797255.mp3","extra":{"resType":"mp3"}},{"source":"微博冷笑话","title":"都是艺术家","text":"","author":"笑话大王","linkUrl":"http://aiui.storage.iflyresearch.com/ctimusic/joke/5202502499014251275.mp3","extra":{"resType":"mp3"}}]},"shouldEndSession":true},"nlu":{"input":"随便来个笑话","skill":"旧版笑话","skillId":"2019032500000002","skillVersion":"29","semantics":{"request":{"slots":[]}}},"timestamp":1734664175}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid228a0a89@dx547c1ac02bef3eef00"}
连接正常关闭
1734664176
param:b'{\n            "auth_id": "8fa1d904823c47ec8e635393f38c8ef4",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidf410f333@dx5a851ac02bef3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "a5a3f56e41614b9b90bf213902ed7639","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be31a@dx193e20ac17eb8a9532"}
started:
ws start
####################
测试进行: ctm00011eb0@hu17ba9ed6f4d020c902#47643172.pcm
{"recordId":"ase000e1568@hu193e20ad35305bf882:a5a3f56e41614b9b90bf213902ed7639","requestId":"ase000e1568@hu193e20ad35305bf882","sessionId":"cid000be31a@dx193e20ac17eb8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664180}
{"recordId":"gty000be31b@dx193e20ac384b8a9532:a5a3f56e41614b9b90bf213902ed7639","requestId":"gty000be31b@dx193e20ac384b8a9532","sessionId":"cid000be31a@dx193e20ac17eb8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664181}
{"recordId":"gty000be31b@dx193e20ac384b8a9532:a5a3f56e41614b9b90bf213902ed7639","requestId":"gty000be31b@dx193e20ac384b8a9532","sessionId":"cid000be31a@dx193e20ac17eb8a9532","eof":"1","text":"北京最近有什么动态","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664181}
send data finished:1734664182466.643
{"recordId":"gty000be31b@dx193e20ac384b8a9532:a5a3f56e41614b9b90bf213902ed7639","requestId":"gty000be31b@dx193e20ac384b8a9532","sessionId":"cid000be31a@dx193e20ac17eb8a9532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"北京最近有什么动态","intentId":"chat","intentName":"闲聊","nlg":"嗯……有什么呢?","shouldEndSession":true},"nlu":{"input":"北京最近有什么动态","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1734664181}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid38ca2279@dx87001ac02bf53eef00"}
连接正常关闭
1734664182
param:b'{\n            "auth_id": "8c5b9e086e71476d8f101d20c9422591",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid9fc5df16@dxe43d1ac02bf63eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "8942b02450a24d50bff0c8323b1d25a7","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bdf4c@dx193e20ad9a1b8aa532"}
started:
ws start
####################
测试进行: ctm00011eba@hu17ba9ed777f020c902#47643178.pcm
{"recordId":"ase000f9b93@hu193e20ae6ef05c2882:8942b02450a24d50bff0c8323b1d25a7","requestId":"ase000f9b93@hu193e20ae6ef05c2882","sessionId":"cid000bdf4c@dx193e20ad9a1b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664185}
{"recordId":"gty000bdf4d@dx193e20adb7eb8aa532:8942b02450a24d50bff0c8323b1d25a7","requestId":"gty000bdf4d@dx193e20adb7eb8aa532","sessionId":"cid000bdf4c@dx193e20ad9a1b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664187}
{"recordId":"gty000bdf4d@dx193e20adb7eb8aa532:8942b02450a24d50bff0c8323b1d25a7","requestId":"gty000bdf4d@dx193e20adb7eb8aa532","sessionId":"cid000bdf4c@dx193e20ad9a1b8aa532","eof":"1","text":"今天有什么新闻","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1734664187}
send data finished:1734664188484.3882
{"recordId":"gty000bdf4d@dx193e20adb7eb8aa532:8942b02450a24d50bff0c8323b1d25a7","requestId":"gty000bdf4d@dx193e20adb7eb8aa532","sessionId":"cid000bdf4c@dx193e20ad9a1b8aa532","topic":"dm.output","skill":"新闻","skillId":"2019031900001180","speakUrl":"","error":{},"dm":{"input":"今天有什么新闻","intentId":"PLAY","intentName":"听新闻","nlg":"为您报道关于有什么的新闻：近视了不戴眼镜会有什么后果","widget":{"content":[{"album":"新闻","title":"近视了不戴眼镜会有什么后果","subTitle":"","imageUrl":"","linkUrl":"https://p3.renbenzhihui.com/cmpp/202412181620E8CAC8250AFB004E8152A4E145597B6B_64.mp3?pf=fm&vid=11651797&tm=1734664187978&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"A股公司突发！实控人被判有期徒刑三年二个月，并被罚700万元！发生了什么？","subTitle":"","imageUrl":"","linkUrl":"https://p3.renbenzhihui.com/cmpp/20241215195492CE35860F8ADDF6356C826940FE4123_64.mp3?pf=fm&vid=11648752&tm=1734664187980&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"为什么有人把脚伸出被子睡觉﻿","subTitle":"","imageUrl":"","linkUrl":"https://p3.renbenzhihui.com/cmpp/2024121212386C0DCA6818625046B026318CBC6B9A0E_64.mp3?pf=fm&vid=11644463&tm=1734664187982&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"自体输血有什么好处？","subTitle":"","imageUrl":"","linkUrl":"https://p3.renbenzhihui.com/cmpp/202412021224AFDAD9467E66AE518A6E05607B11E353_64.mp3?pf=fm&vid=11631773&tm=1734664187984&pid=636495","extra":{"source":"fm"}},{"album":"新闻","title":"肾炎与泡沫尿有什么关联？","subTitle":"","imageUrl":"","linkUrl":"https://p3.renbenzhihui.com/cmpp/202412021224E3BA69248A92854271CDE9183BB7A6E2_64.mp3?pf=fm&vid=11631772&tm=1734664187986&pid=636495","extra":{"source":"fm"}},{"album":"新闻","title":"自体输血有什么好处？","subTitle":"","imageUrl":"","linkUrl":"https://p3.renbenzhihui.com/cmpp/202412021224AFDAD9467E66AE518A6E05607B11E353_64.mp3?pf=fm&vid=11631730&tm=1734664187989&pid=1716511","extra":{"source":"fm"}},{"album":"新闻","title":"肾炎与泡沫尿有什么关联？","subTitle":"","imageUrl":"","linkUrl":"https://p3.renbenzhihui.com/cmpp/202412021224E3BA69248A92854271CDE9183BB7A6E2_64.mp3?pf=fm&vid=11631719&tm=1734664187993&pid=1716511","extra":{"source":"fm"}},{"album":"新闻","title":"好的药膳有什么特点？","subTitle":"","imageUrl":"","linkUrl":"https://p3.renbenzhihui.com/cmpp/2024120116182EC3FEF364FD9235CBB39F759C29A9B0_64.mp3?pf=fm&vid=11631117&tm=1734664187995&pid=636495","extra":{"source":"fm"}},{"album":"新闻","title":"好的药膳有什么特点？","subTitle":"","imageUrl":"","linkUrl":"https://p3.renbenzhihui.com/cmpp/2024120116182EC3FEF364FD9235CBB39F759C29A9B0_64.mp3?pf=fm&vid=11631092&tm=1734664187998&pid=1716511","extra":{"source":"fm"}},{"album":"新闻","title":"为什么一放假就有休耻感","subTitle":"","imageUrl":"","linkUrl":"https://p3.renbenzhihui.com/cmpp/2024110514152F4F248DBA60661B0934A1DA0D5F1D6E_64.mp3?pf=fm&vid=11572780&tm=1734664188002&pid=1899233","extra":{"source":"凤凰FM"}}]},"shouldEndSession":true},"nlu":{"input":"今天有什么新闻","skill":"新闻","skillId":"2019031900001180","skillVersion":"87","semantics":{"request":{"slots":[{"name":"datetime","normValue":"{\"datetime\":\"2024-12-20\",\"suggestDatetime\":\"2024-12-20\"}","value":"今天"},{"name":"keyword","value":"有什么"}]}}},"timestamp":1734664188}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid95c073ed@dx02101ac02bfc3eef00"}
连接正常关闭
1734664188
param:b'{\n            "auth_id": "db86f669a55f4bf384aba41a49d4535c",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid1f6768da@dx94881ac02bfc3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "cef745ea6cd34c1cacfa0756a02c4fbc","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be1ce@dx193e20af20d7844532"}
started:
ws start
####################
测试进行: ctm00011ebc@hu17ba9ed77a4020c902#47643177.pcm
{"recordId":"ase000e32ff@hu193e20b03a11323882:cef745ea6cd34c1cacfa0756a02c4fbc","requestId":"ase000e32ff@hu193e20b03a11323882","sessionId":"cid000be1ce@dx193e20af20d7844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664193}
{"recordId":"gty000be1cf@dx193e20af3f07844532:cef745ea6cd34c1cacfa0756a02c4fbc","requestId":"gty000be1cf@dx193e20af3f07844532","sessionId":"cid000be1ce@dx193e20af20d7844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664193}
{"recordId":"gty000be1cf@dx193e20af3f07844532:cef745ea6cd34c1cacfa0756a02c4fbc","requestId":"gty000be1cf@dx193e20af3f07844532","sessionId":"cid000be1ce@dx193e20af20d7844532","eof":"1","text":"关于杨幂的新闻","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664193}
send data finished:1734664194743.4165
{"recordId":"gty000be1cf@dx193e20af3f07844532:cef745ea6cd34c1cacfa0756a02c4fbc","requestId":"gty000be1cf@dx193e20af3f07844532","sessionId":"cid000be1ce@dx193e20af20d7844532","topic":"dm.output","skill":"新闻","skillId":"2019031900001180","speakUrl":"","error":{},"dm":{"input":"关于杨幂的新闻","intentId":"PLAY","intentName":"听新闻","nlg":"为您找到关于杨幂的新闻，可以了解下杨幂不愧是先天抽象圣体","widget":{"content":[{"album":"新闻","title":"杨幂不愧是先天抽象圣体","subTitle":"","imageUrl":"","linkUrl":"https://p3.renbenzhihui.com/cmpp/202412181154F2CC9365FCB428C50D08A465D583897B_64.mp3?pf=fm&vid=11651593&tm=1734664194232&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"网传杨幂疑似红毯耍大牌？对接方发文力破各类谣言","subTitle":"","imageUrl":"","linkUrl":"https://p3.renbenzhihui.com/cmpp/202412171509DEF871D595A474B888D5B01868B7E267_64.mp3?pf=fm&vid=11650483&tm=1734664194234&pid=486506","extra":{"source":""}},{"album":"新闻","title":"网传杨幂疑似红毯耍大牌？对接方发文力破各类谣言","subTitle":"","imageUrl":"","linkUrl":"https://p3.renbenzhihui.com/cmpp/202412171508AE04697882AA31A40B15FE37A8C08961_64.mp3?pf=fm&vid=11650478&tm=1734664194235&pid=340281","extra":{"source":""}},{"album":"新闻","title":"杨幂工作室分享时代姐妹花合照","subTitle":"","imageUrl":"","linkUrl":"https://p3.renbenzhihui.com/cmpp/20241217150925525516FBACAF134965270C76C470E0_64.mp3?pf=fm&vid=11650485&tm=1734664194237&pid=486506","extra":{"source":""}},{"album":"新闻","title":"嘉人盛典9位影后同框","subTitle":"","imageUrl":"","linkUrl":"https://p3.renbenzhihui.com/cmpp/2024121618069CE373225DD6B22AE098CAC50C86ACC3_64.mp3?pf=fm&vid=11649526&tm=1734664194238&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"杨幂郭采洁郭碧婷谢依霖同框，梦回《小时代》","subTitle":"","imageUrl":"","linkUrl":"https://p3.renbenzhihui.com/cmpp/20241216160137B6F26BAF9E0659BD30B824A89AA294_64.mp3?pf=fm&vid=11649430&tm=1734664194240&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"杨幂、郭采洁、郭碧婷、谢依霖同框","subTitle":"","imageUrl":"","linkUrl":"https://p3.renbenzhihui.com/cmpp/202412161128C8D0C12AE751DB3CE5D7BA591C5A5BC5_64.mp3?pf=fm&vid=11649281&tm=1734664194241&pid=340281","extra":{"source":""}},{"album":"新闻","title":"杨幂、郭采洁、郭碧婷、谢依霖同框","subTitle":"","imageUrl":"","linkUrl":"https://p3.renbenzhihui.com/cmpp/202412161129FD89CD3B0963FEABED202627468CD1F6_64.mp3?pf=fm&vid=11649288&tm=1734664194242&pid=486506","extra":{"source":""}},{"album":"新闻","title":"杨幂助阵、产品渠道双剑破局 “中国版Moncler”高梵如何霸榜双十一？","subTitle":"","imageUrl":"","linkUrl":"https://p3.renbenzhihui.com/cmpp/2024111600140EDBEB6DA1BFC242AE370BE241DAF33E_64.mp3?pf=fm&vid=11605923&tm=1734664194244&pid=1899565","extra":{"source":"fm"}},{"album":"新闻","title":"梦幻联动！伊能静儿子晒与杨幂巴黎时装周看秀合影","subTitle":"","imageUrl":"","linkUrl":"https://p3.renbenzhihui.com/cmpp/20240929202350432A9B90F1483633FE4296FA4EC680_64.mp3?pf=fm&vid=11463407&tm=1734664194245&pid=1716511","extra":{"source":"fm"}}]},"shouldEndSession":true},"nlu":{"input":"关于杨幂的新闻","skill":"新闻","skillId":"2019031900001180","skillVersion":"87","semantics":{"request":{"slots":[{"name":"keyword","value":"关于杨幂"}]}}},"timestamp":1734664194}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid7e3394ce@dx13c71ac02c023eef00"}
连接正常关闭
1734664195
param:b'{\n            "auth_id": "11f5b47c7807412194dbd10154edc5ac",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid81c29c45@dx27181ac02c023eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "0b2cf2464b754993b5b1acc45b00a8da","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be324@dx193e20b0a56b8a9532"}
started:
ws start
####################
测试进行: ctm00011ebe@hu17ba9ed7823020c902#47643181.pcm
{"recordId":"gty000be325@dx193e20b0c7cb8a9532:0b2cf2464b754993b5b1acc45b00a8da","requestId":"gty000be325@dx193e20b0c7cb8a9532","sessionId":"cid000be324@dx193e20b0a56b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664199}
{"recordId":"gty000be325@dx193e20b0c7cb8a9532:0b2cf2464b754993b5b1acc45b00a8da","requestId":"gty000be325@dx193e20b0c7cb8a9532","sessionId":"cid000be324@dx193e20b0a56b8a9532","eof":"1","text":"说新闻","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664199}
send data finished:1734664200351.0525
{"recordId":"ase000dc0a4@hu193e20b1df10427882:0b2cf2464b754993b5b1acc45b00a8da","requestId":"ase000dc0a4@hu193e20b1df10427882","sessionId":"cid000be324@dx193e20b0a56b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664199}
{"recordId":"gty000be325@dx193e20b0c7cb8a9532:0b2cf2464b754993b5b1acc45b00a8da","requestId":"gty000be325@dx193e20b0c7cb8a9532","sessionId":"cid000be324@dx193e20b0a56b8a9532","topic":"dm.output","skill":"新闻","skillId":"2019031900001180","speakUrl":"","error":{},"dm":{"input":"说新闻","intentId":"PLAY","intentName":"听新闻","nlg":"《小小的我》预售破4000万","widget":{"content":[{"album":"新闻","title":"《小小的我》预售破4000万","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2024122010427CDFF04011E67C470EDFC5D7A48B7958_64.mp3?pf=OH9GI&vid=11654253&tm=1734664199774&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"蜜雪冰城也坐不住了?涨价引发热议","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2024122010226C55C932DDBE8856E9B78D5816530C8B_64.mp3?pf=OH9GI&vid=11654190&tm=1734664199774&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"韩国年轻人“特种兵旅游”挤满上海","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2024122010059487F99E77BECF3C287E27C1C10DEA29_64.mp3?pf=OH9GI&vid=11654187&tm=1734664199774&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"填补空白，我国研制首批高端仪器装备计量测评装置﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/20241220095756B035800BA0A0241F9A250B7580D35A_64.mp3?pf=OH9GI&vid=11654186&tm=1734664199774&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"中新网评余华英被判死刑：打拐是全社会共同的责任﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202412200955D8B369C5A2D62C4868C3779EFCFCEA71_64.mp3?pf=OH9GI&vid=11654183&tm=1734664199774&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"普京称俄愿在乌克兰问题上妥协﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202412200955EF2BDA48A1FD1B105129754E4EFD7C63_64.mp3?pf=OH9GI&vid=11654178&tm=1734664199774&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"立法禁止未成年人使用社媒可行吗，多国考虑立法禁止未成年人使用社媒","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202412200945F4C7A02FD7915C0BBC60F3C3A83E9EF9_64.mp3?pf=OH9GI&vid=11654169&tm=1734664199775&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"美国防部：美国在叙利亚的驻军人数已增加一倍以上","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202412200837E34D4791DF50F60F019C2456FC3E878D_64.mp3?pf=OH9GI&vid=11654154&tm=1734664199775&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"12月20日人本生活资讯早报：死刑！余华英重审二审宣判；反向出游，年轻人热衷出国过年；多地官宣将“取消公摊”","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2024121920199619187C8F69C0F3863DE017C9353AA8_64.mp3?pf=OH9GI&vid=11653528&tm=1734664199775&pid=1898253","extra":{"source":""}},{"album":"新闻","title":"智汇评|多地官宣将取消公摊，购房成本会降吗？","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/20241219155123CB88DF00F8D4398EEC7B7B37BB414A_64.mp3?pf=OH9GI&vid=11653235&tm=1734664199775&pid=576511","extra":{"source":"fm"}}]},"shouldEndSession":true},"nlu":{"input":"说新闻","skill":"新闻","skillId":"2019031900001180","skillVersion":"87","semantics":{"request":{"slots":[]}}},"timestamp":1734664199}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid97dc5b57@dx64a01ac02c073eef00"}
连接正常关闭
1734664200
param:b'{\n            "auth_id": "1d520ce0525d4a6e8c0f653db25d37bf",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidc785faa1@dx78441ac02c083eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "4727473734224b9d8792100d89b23892","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bdcb9@dx193e20b2034b86a532"}
started:
ws start
####################
测试进行: ctm00011ec6@hu17ba9ed7e40020c902#47643186.pcm
Error reading file[Errno 2] No such file or directory: 'data/silence.wav'
{"action":"error","code":"20010108","data":"","desc":"{\"code\":20010108,\"sub\":\"\"}","sid":"gty000bdcba@dx193e20b2245b86a532"}
{'action': 'error', 'code': '20010108', 'data': '', 'desc': '{"code":20010108,"sub":""}', 'sid': 'gty000bdcba@dx193e20b2245b86a532'}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid52f5316a@dxaadd1ac02c223eef00"}
连接正常关闭
1734664226
param:b'{\n            "auth_id": "e6595d1d5e5c43c59add2773250e6ac3",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid77de1965@dx66191ac02c223eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "0bee6b44ab654047aa72a0b6a976d1d6","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bdf76@dx193e20b869db8aa532"}
started:
ws start
####################
测试进行: ctm00011ec8@hu17ba9ed7ea1020c902#47643193.pcm
{"recordId":"ase000fc69d@hu193e20b95bf05c2882:0bee6b44ab654047aa72a0b6a976d1d6","requestId":"ase000fc69d@hu193e20b95bf05c2882","sessionId":"cid000bdf76@dx193e20b869db8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664230}
{"recordId":"gty000bdf77@dx193e20b8872b8aa532:0bee6b44ab654047aa72a0b6a976d1d6","requestId":"gty000bdf77@dx193e20b8872b8aa532","sessionId":"cid000bdf76@dx193e20b869db8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664232}
{"recordId":"gty000bdf77@dx193e20b8872b8aa532:0bee6b44ab654047aa72a0b6a976d1d6","requestId":"gty000bdf77@dx193e20b8872b8aa532","sessionId":"cid000bdf76@dx193e20b869db8aa532","eof":"1","text":"最近有什么热门的新闻吗","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1734664232}
send data finished:1734664233622.5308
{"recordId":"gty000bdf77@dx193e20b8872b8aa532:0bee6b44ab654047aa72a0b6a976d1d6","requestId":"gty000bdf77@dx193e20b8872b8aa532","sessionId":"cid000bdf76@dx193e20b869db8aa532","topic":"dm.output","skill":"新闻","skillId":"2019031900001180","speakUrl":"","error":{},"dm":{"input":"最近有什么热门的新闻吗","intentId":"PLAY","intentName":"听新闻","nlg":"《小小的我》预售破4000万","widget":{"content":[{"album":"新闻","title":"《小小的我》预售破4000万","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2024122010427CDFF04011E67C470EDFC5D7A48B7958_64.mp3?pf=OH9GI&vid=11654253&tm=1734664233001&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"美国抵押贷款利率升至6.72% 四周来首次走高","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2024122010361A8D0838D332916B8C19173D630B7BBE_64.mp3?pf=OH9GI&vid=11654252&tm=1734664233001&pid=1716511","extra":{"source":"fm"}},{"album":"新闻","title":"中方：联合国理应成为人工智能全球治理的主渠道","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202412201033127F6A54D1A7EC26C220C1A26FF13813_64.mp3?pf=OH9GI&vid=11654244&tm=1734664233001&pid=1716511","extra":{"source":"fm"}},{"album":"新闻","title":"日本两大“谷子经济”巨头签订联盟协议 拓展全球IP合作","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/20241220103386019B0BF8E267216D6A577CD888D1B7_64.mp3?pf=OH9GI&vid=11654241&tm=1734664233001&pid=1716511","extra":{"source":"fm"}},{"album":"新闻","title":"古特雷斯呼吁推动叙利亚实现和平包容可信的政治过渡","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2024122010339DF3C440FE2135947CA10074B1A51C5F_64.mp3?pf=OH9GI&vid=11654240&tm=1734664233001&pid=1716511","extra":{"source":"fm"}},{"album":"新闻","title":"英国据悉将任命贸易老将Mandelson为驻美大使 力争缓解关税压力","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202412201033BA5DB2CBD5819A4D28C400D63E13AD82_64.mp3?pf=OH9GI&vid=11654239&tm=1734664233001&pid=1716511","extra":{"source":"fm"}},{"album":"新闻","title":"OpenAI宣布桌面版ChatGPT推出应用协作功能","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202412201033A00318BE4087AC7A739EC95DD307991F_64.mp3?pf=OH9GI&vid=11654238&tm=1734664233001&pid=1716511","extra":{"source":"fm"}},{"album":"新闻","title":"谷歌推出一款类似OpenAI o1的推理模型","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2024122010337CE50066ED000DD8F66FF0466A9A8D25_64.mp3?pf=OH9GI&vid=11654233&tm=1734664233001&pid=1716511","extra":{"source":"fm"}},{"album":"新闻","title":"控制国际学生数量，澳大利亚颁布新政被批“非法”","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202412201033535A413419B9C87DA74D905F1D9B3DA6_64.mp3?pf=OH9GI&vid=11654232&tm=1734664233001&pid=1716511","extra":{"source":"fm"}},{"album":"新闻","title":"美国政府“停摆”期限临近，美众议院否决特朗普支持的临时拨款法案","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2024122010334D2836D68E9B72FEB1F2A965F7B05A2C_64.mp3?pf=OH9GI&vid=11654231&tm=1734664233001&pid=1716511","extra":{"source":"fm"}}]},"shouldEndSession":true},"nlu":{"input":"最近有什么热门的新闻吗","skill":"新闻","skillId":"2019031900001180","skillVersion":"87","semantics":{"request":{"slots":[{"name":"category","value":"热点"}]}}},"timestamp":1734664233}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid02304ac2@dx50ab1ac02c293eef00"}
连接正常关闭
1734664233
param:b'{\n            "auth_id": "bfb70848f23b41f19885c4c1cd64b308",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid3002c999@dxc3511ac02c293eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "25a891fe805c41a19208285f9d1ead56","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bdcca@dx193e20ba1d0b86a532"}
started:
ws start
####################
测试进行: ctm00011ed7@hu17ba9ed874f020c902#47643204.pcm
{"recordId":"ase000fc41f@hu193e20bb46005c0882:25a891fe805c41a19208285f9d1ead56","requestId":"ase000fc41f@hu193e20bb46005c0882","sessionId":"cid000bdcca@dx193e20ba1d0b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664238}
{"recordId":"gty000bdccb@dx193e20ba3cdb86a532:25a891fe805c41a19208285f9d1ead56","requestId":"gty000bdccb@dx193e20ba3cdb86a532","sessionId":"cid000bdcca@dx193e20ba1d0b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664242}
{"recordId":"gty000bdccb@dx193e20ba3cdb86a532:25a891fe805c41a19208285f9d1ead56","requestId":"gty000bdccb@dx193e20ba3cdb86a532","sessionId":"cid000bdcca@dx193e20ba1d0b86a532","eof":"1","text":"档案放一首王力宏和谭维维的缘分是","pinyin":"","topic":"asr.speech.result","age":"elder","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664242}
send data finished:1734664242858.129
{"recordId":"gty000bdccb@dx193e20ba3cdb86a532:25a891fe805c41a19208285f9d1ead56","requestId":"gty000bdccb@dx193e20ba3cdb86a532","sessionId":"cid000bdcca@dx193e20ba1d0b86a532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"档案放一首王力宏和谭维维的缘分是","intentId":"PLAY","intentName":"点歌","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"档案放一首王力宏和谭维维的缘分是","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"歌曲名","value":"缘分"},{"name":"歌手名","value":"王力宏"},{"name":"合唱歌手","value":"谭维维"}]}}},"timestamp":1734664242}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid9a2f4518@dx16111ac02c323eef00"}
连接正常关闭
1734664242
param:b'{\n            "auth_id": "212ddc5171d94b7cbce1ef753cfd04b1",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid66553a8c@dxa37e1ac02c323eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "6fbb49ab10f0433eabb677d0c7c3800e","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be1f0@dx193e20bc5457844532"}
started:
ws start
####################
测试进行: ctm00011ee2@hu17ba9ed8da1020c902#47643207.pcm
{"recordId":"ase000fd6c3@hu193e20bd6eb05c2882:6fbb49ab10f0433eabb677d0c7c3800e","requestId":"ase000fd6c3@hu193e20bd6eb05c2882","sessionId":"cid000be1f0@dx193e20bc5457844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664247}
{"recordId":"ase000e3cf3@hu193e20bd78805c4882:6fbb49ab10f0433eabb677d0c7c3800e","requestId":"ase000e3cf3@hu193e20bd78805c4882","sessionId":"cid000be1f0@dx193e20bc5457844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664247}
{"recordId":"gty000be1f1@dx193e20bc73c7844532:6fbb49ab10f0433eabb677d0c7c3800e","requestId":"gty000be1f1@dx193e20bc73c7844532","sessionId":"cid000be1f0@dx193e20bc5457844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664247}
{"recordId":"gty000be1f1@dx193e20bc73c7844532:6fbb49ab10f0433eabb677d0c7c3800e","requestId":"gty000be1f1@dx193e20bc73c7844532","sessionId":"cid000be1f0@dx193e20bc5457844532","eof":"1","text":"来一首我怀念的","pinyin":"","topic":"asr.speech.result","age":"elder","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664247}
send data finished:1734664248717.3813
{"recordId":"gty000be1f1@dx193e20bc73c7844532:6fbb49ab10f0433eabb677d0c7c3800e","requestId":"gty000be1f1@dx193e20bc73c7844532","sessionId":"cid000be1f0@dx193e20bc5457844532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"来一首我怀念的","intentId":"PLAY","intentName":"点歌","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"来一首我怀念的","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"歌曲名","value":"我怀念的"}]}}},"timestamp":1734664247}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid9846082c@dxac461ac02c373eef00"}
连接正常关闭
1734664248
param:b'{\n            "auth_id": "e18a19b940d74f0baa9e3c3e0dac17c7",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidf9fef5a0@dxb5ed1ac02c383eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "f6cb81ba9d0c43d3a953660d0cdfca9c","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be1fa@dx193e20bdcce7844532"}
started:
ws start
####################
测试进行: ctm00011ee5@hu17ba9ed8e29020c902#47643213.pcm
{"recordId":"ase000df4cc@hu193e20bf29b0427882:f6cb81ba9d0c43d3a953660d0cdfca9c","requestId":"ase000df4cc@hu193e20bf29b0427882","sessionId":"cid000be1fa@dx193e20bdcce7844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664254}
{"recordId":"gty000be1fb@dx193e20bde897844532:f6cb81ba9d0c43d3a953660d0cdfca9c","requestId":"gty000be1fb@dx193e20bde897844532","sessionId":"cid000be1fa@dx193e20bdcce7844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664256}
{"recordId":"gty000be1fb@dx193e20bde897844532:f6cb81ba9d0c43d3a953660d0cdfca9c","requestId":"gty000be1fb@dx193e20bde897844532","sessionId":"cid000be1fa@dx193e20bdcce7844532","eof":"1","text":"暂停切换别带","pinyin":"","topic":"asr.speech.result","age":"elder","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664256}
send data finished:1734664257381.8774
{"recordId":"gty000be1fb@dx193e20bde897844532:f6cb81ba9d0c43d3a953660d0cdfca9c","requestId":"gty000be1fb@dx193e20bde897844532","sessionId":"cid000be1fa@dx193e20bdcce7844532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"暂停切换别带","intentId":"chat","intentName":"闲聊","nlg":"这个我真不懂，换个我懂的呗。","shouldEndSession":true},"nlu":{"input":"暂停切换别带","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1734664256}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid131f74af@dx6a921ac02c403eef00"}
连接正常关闭
1734664257
param:b'{\n            "auth_id": "7e10b131745a4d86888109ee3d44a839",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid0467f239@dx5b071ac02c413eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "3dcbecaa1cf34cdda21b800302bb913e","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be202@dx193e20c00027844532"}
started:
ws start
####################
测试进行: ctm000121ed@hu17b59a7bb700212902#46466916.pcm
{"recordId":"ase000e393e@hu193e20c101105c3882:3dcbecaa1cf34cdda21b800302bb913e","requestId":"ase000e393e@hu193e20c101105c3882","sessionId":"cid000be202@dx193e20c00027844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664261}
{"recordId":"ase000fe50f@hu193e20c10ed05c2882:3dcbecaa1cf34cdda21b800302bb913e","requestId":"ase000fe50f@hu193e20c10ed05c2882","sessionId":"cid000be202@dx193e20c00027844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664262}
{"recordId":"gty000be203@dx193e20c01d57844532:3dcbecaa1cf34cdda21b800302bb913e","requestId":"gty000be203@dx193e20c01d57844532","sessionId":"cid000be202@dx193e20c00027844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664262}
{"recordId":"gty000be203@dx193e20c01d57844532:3dcbecaa1cf34cdda21b800302bb913e","requestId":"gty000be203@dx193e20c01d57844532","sessionId":"cid000be202@dx193e20c00027844532","eof":"1","text":"我想听钢琴曲","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1734664262}
send data finished:1734664263352.6248
{"recordId":"gty000be203@dx193e20c01d57844532:3dcbecaa1cf34cdda21b800302bb913e","requestId":"gty000be203@dx193e20c01d57844532","sessionId":"cid000be202@dx193e20c00027844532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"我想听钢琴曲","intentId":"PLAY","intentName":"点歌","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"我想听钢琴曲","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"乐器","value":"钢琴"},{"name":"标签","value":"钢琴曲"}]}}},"timestamp":1734664262}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidd637032d@dx21f11ac02c463eef00"}
连接正常关闭
1734664263
param:b'{\n            "auth_id": "ca2dce0976334947b4b7378a83b7a7ca",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid8823d3de@dx5a9b1ac02c463eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "48e5af6d63ee401390deb9e51f4fbc38","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be35a@dx193e20c1510b8a9532"}
started:
ws start
####################
测试进行: ctm000121ff@hu17b59a7c16b0212902#46466942.pcm
{"recordId":"gty000be35b@dx193e20c1703b8a9532:48e5af6d63ee401390deb9e51f4fbc38","requestId":"gty000be35b@dx193e20c1703b8a9532","sessionId":"cid000be35a@dx193e20c1510b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664268}
{"recordId":"gty000be35b@dx193e20c1703b8a9532:48e5af6d63ee401390deb9e51f4fbc38","requestId":"gty000be35b@dx193e20c1703b8a9532","sessionId":"cid000be35a@dx193e20c1510b8a9532","eof":"1","text":"小清风","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1734664268}
send data finished:1734664268859.289
{"recordId":"gty000be35b@dx193e20c1703b8a9532:48e5af6d63ee401390deb9e51f4fbc38","requestId":"gty000be35b@dx193e20c1703b8a9532","sessionId":"cid000be35a@dx193e20c1510b8a9532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"小清风","intentId":"chat","intentName":"闲聊","nlg":"我就是个机器人，这个问题也太难啦。你简单点说呗！","shouldEndSession":true},"nlu":{"input":"小清风","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1734664268}
{"recordId":"ase000fe12b@hu193e20c298705c0882:48e5af6d63ee401390deb9e51f4fbc38","requestId":"ase000fe12b@hu193e20c298705c0882","sessionId":"cid000be35a@dx193e20c1510b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664268}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidf478549f@dx8eca1ac02c4c3eef00"}
连接正常关闭
1734664269
param:b'{\n            "auth_id": "59e9fefa8c9d4c5582112b1df6bccf87",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid329bb5a1@dx139f1ac02c4c3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "6e032cfac54b4bcfad3effc3ef0bf3c3","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be35c@dx193e20c2c55b8a9532"}
started:
ws start
####################
测试进行: ctm0001220c@hu17b59a7c53a0212902#46466953.pcm
{"recordId":"ase000ff603@hu193e20c517605c2882:6e032cfac54b4bcfad3effc3ef0bf3c3","requestId":"ase000ff603@hu193e20c517605c2882","sessionId":"cid000be35c@dx193e20c2c55b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664278}
Error reading file[Errno 2] No such file or directory: 'data/silence.wav'
{"action":"error","code":"20010108","data":"","desc":"{\"code\":20010108,\"sub\":\"\"}","sid":"gty000be35d@dx193e20c2e3ab8a9532"}
{'action': 'error', 'code': '20010108', 'data': '', 'desc': '{"code":20010108,"sub":""}', 'sid': 'gty000be35d@dx193e20c2e3ab8a9532'}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid3e4323c0@dx76f01ac02c663eef00"}
连接正常关闭
1734664295
param:b'{\n            "auth_id": "0d86eb54bff8486ea66214669d3f546f",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid91159140@dx5dbf1ac02c663eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "4fb3b3b1765b438f92f2a322cb6ac095","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be36e@dx193e20c9123b8a9532"}
started:
ws start
####################
测试进行: ctm00012221@hu17b59a7cc0a0212902#46466990.pcm
{"recordId":"ase000e8732@hu193e20c9db805bf882:4fb3b3b1765b438f92f2a322cb6ac095","requestId":"ase000e8732@hu193e20c9db805bf882","sessionId":"cid000be36e@dx193e20c9123b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664298}
{"recordId":"gty000be371@dx193e20c92e1b8a9532:4fb3b3b1765b438f92f2a322cb6ac095","requestId":"gty000be371@dx193e20c92e1b8a9532","sessionId":"cid000be36e@dx193e20c9123b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664300}
{"recordId":"gty000be371@dx193e20c92e1b8a9532:4fb3b3b1765b438f92f2a322cb6ac095","requestId":"gty000be371@dx193e20c92e1b8a9532","sessionId":"cid000be36e@dx193e20c9123b8a9532","eof":"1","text":"","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664300}
{"recordId":"gty000be371@dx193e20c92e1b8a9532:4fb3b3b1765b438f92f2a322cb6ac095","requestId":"gty000be371@dx193e20c92e1b8a9532","sessionId":"cid000be36e@dx193e20c9123b8a9532","topic":"dm.output","skill":"","skillId":"","speakUrl":"","error":{"errId":"010305","errMsg":"asr result is null"},"dm":{},"nlu":{"input":"","skill":"","skillId":""},"timestamp":1734664300}
发送结束标识
发送断开连接标识
send data finished:1734664301629.402
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid73d7c1da@dx09e51ac02c6c3eef00"}
连接正常关闭
1734664301
param:b'{\n            "auth_id": "e010e34f587f4c82b78734e8c8d63306",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid974c9b6b@dx1d6c1ac02c6d3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "2e61794d9d414afab1d07ff8e1fc42a9","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be226@dx193e20caa8b7844532"}
started:
ws start
####################
测试进行: ctm0001254a@hu17b6668678b020c902#46613773.pcm
{"recordId":"ase000db593@hu193e20cbf8b04d3882:2e61794d9d414afab1d07ff8e1fc42a9","requestId":"ase000db593@hu193e20cbf8b04d3882","sessionId":"cid000be226@dx193e20caa8b7844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664306}
Error reading file[Errno 2] No such file or directory: 'data/silence.wav'
{"action":"error","code":"20010108","data":"","desc":"{\"code\":20010108,\"sub\":\"\"}","sid":"gty000be227@dx193e20cac657844532"}
{'action': 'error', 'code': '20010108', 'data': '', 'desc': '{"code":20010108,"sub":""}', 'sid': 'gty000be227@dx193e20cac657844532'}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid27a4c98e@dxa9ee1ac02c883eef00"}
连接正常关闭
1734664329
param:b'{\n            "auth_id": "9ab17c76a7204e1bbb1e266b360eed27",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid54f3baa6@dxf6801ac02c883eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "7cdeadd2a9884460bdfbfd89579af454","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bdd1c@dx193e20d15ecb86a532"}
started:
ws start
####################
测试进行: ctm00012551@hu17b66686d40020c902#46613780.pcm
{"recordId":"ase000f1c61@hu193e20d1fea05c0882:7cdeadd2a9884460bdfbfd89579af454","requestId":"ase000f1c61@hu193e20d1fea05c0882","sessionId":"cid000bdd1c@dx193e20d15ecb86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664331}
Error reading file[Errno 2] No such file or directory: 'data/silence.wav'
{"action":"error","code":"20010108","data":"","desc":"{\"code\":20010108,\"sub\":\"\"}","sid":"gty000bdd1d@dx193e20d17eeb86a532"}
{'action': 'error', 'code': '20010108', 'data': '', 'desc': '{"code":20010108,"sub":""}', 'sid': 'gty000bdd1d@dx193e20d17eeb86a532'}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid90a007fb@dx67db1ac02ca53eef00"}
连接正常关闭
1734664358
param:b'{\n            "auth_id": "1eea8b1b1cf241b597054a4dd710f7ff",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid1c60d26a@dxe45e1ac02ca53eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "5b29e95db51148c2bb1986692c286c16","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be25a@dx193e20d88437844532"}
started:
ws start
####################
测试进行: ctm00012566@hu17b666873fb020c902#46613793.pcm
{"recordId":"ase000f41b7@hu193e20d8f7605c2882:5b29e95db51148c2bb1986692c286c16","requestId":"ase000f41b7@hu193e20d8f7605c2882","sessionId":"cid000be25a@dx193e20d88437844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664359}
{"recordId":"gty000be25b@dx193e20d8a2c7844532:5b29e95db51148c2bb1986692c286c16","requestId":"gty000be25b@dx193e20d8a2c7844532","sessionId":"cid000be25a@dx193e20d88437844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664366}
{"recordId":"gty000be25b@dx193e20d8a2c7844532:5b29e95db51148c2bb1986692c286c16","requestId":"gty000be25b@dx193e20d8a2c7844532","sessionId":"cid000be25a@dx193e20d88437844532","eof":"1","text":"我想听新闻","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664366}
send data finished:1734664367216.6929
{"recordId":"gty000be25b@dx193e20d8a2c7844532:5b29e95db51148c2bb1986692c286c16","requestId":"gty000be25b@dx193e20d8a2c7844532","sessionId":"cid000be25a@dx193e20d88437844532","topic":"dm.output","skill":"新闻","skillId":"2019031900001180","speakUrl":"","error":{},"dm":{"input":"我想听新闻","intentId":"PLAY","intentName":"听新闻","nlg":"fm报道：《小小的我》预售破4000万","widget":{"content":[{"album":"新闻","title":"《小小的我》预售破4000万","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2024122010427CDFF04011E67C470EDFC5D7A48B7958_64.mp3?pf=OH9GI&vid=11654253&tm=1734664366623&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"蜜雪冰城也坐不住了?涨价引发热议","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2024122010226C55C932DDBE8856E9B78D5816530C8B_64.mp3?pf=OH9GI&vid=11654190&tm=1734664366623&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"韩国年轻人“特种兵旅游”挤满上海","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2024122010059487F99E77BECF3C287E27C1C10DEA29_64.mp3?pf=OH9GI&vid=11654187&tm=1734664366623&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"填补空白，我国研制首批高端仪器装备计量测评装置﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/20241220095756B035800BA0A0241F9A250B7580D35A_64.mp3?pf=OH9GI&vid=11654186&tm=1734664366623&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"中新网评余华英被判死刑：打拐是全社会共同的责任﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202412200955D8B369C5A2D62C4868C3779EFCFCEA71_64.mp3?pf=OH9GI&vid=11654183&tm=1734664366623&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"普京称俄愿在乌克兰问题上妥协﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202412200955EF2BDA48A1FD1B105129754E4EFD7C63_64.mp3?pf=OH9GI&vid=11654178&tm=1734664366623&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"立法禁止未成年人使用社媒可行吗，多国考虑立法禁止未成年人使用社媒","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202412200945F4C7A02FD7915C0BBC60F3C3A83E9EF9_64.mp3?pf=OH9GI&vid=11654169&tm=1734664366623&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"美国防部：美国在叙利亚的驻军人数已增加一倍以上","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202412200837E34D4791DF50F60F019C2456FC3E878D_64.mp3?pf=OH9GI&vid=11654154&tm=1734664366623&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"12月20日人本生活资讯早报：死刑！余华英重审二审宣判；反向出游，年轻人热衷出国过年；多地官宣将“取消公摊”","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2024121920199619187C8F69C0F3863DE017C9353AA8_64.mp3?pf=OH9GI&vid=11653528&tm=1734664366623&pid=1898253","extra":{"source":""}},{"album":"新闻","title":"智汇评|多地官宣将取消公摊，购房成本会降吗？","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/20241219155123CB88DF00F8D4398EEC7B7B37BB414A_64.mp3?pf=OH9GI&vid=11653235&tm=1734664366623&pid=576511","extra":{"source":"fm"}}]},"shouldEndSession":true},"nlu":{"input":"我想听新闻","skill":"新闻","skillId":"2019031900001180","skillVersion":"87","semantics":{"request":{"slots":[]}}},"timestamp":1734664366}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid8eb03a78@dx22351ac02cae3eef00"}
连接正常关闭
1734664367
param:b'{\n            "auth_id": "0e0722aba6e94537ad07f8845e6e10a8",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid4e45e041@dx8f011ac02cae3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "6085cb508ff64437bfc0f7dd69b05591","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bdd34@dx193e20dabe9b86a532"}
started:
ws start
####################
测试进行: ctm00012575@hu17b666879d6020c902#46613807.pcm
{"recordId":"ase000edc52@hu193e20db8dd1323882:6085cb508ff64437bfc0f7dd69b05591","requestId":"ase000edc52@hu193e20db8dd1323882","sessionId":"cid000bdd34@dx193e20dabe9b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664370}
{"recordId":"gty000bdd37@dx193e20dadccb86a532:6085cb508ff64437bfc0f7dd69b05591","requestId":"gty000bdd37@dx193e20dadccb86a532","sessionId":"cid000bdd34@dx193e20dabe9b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664376}
{"recordId":"gty000bdd37@dx193e20dadccb86a532:6085cb508ff64437bfc0f7dd69b05591","requestId":"gty000bdd37@dx193e20dadccb86a532","sessionId":"cid000bdd34@dx193e20dabe9b86a532","eof":"1","text":"播放琵琶名曲","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664376}
send data finished:1734664377412.6968
{"recordId":"gty000bdd37@dx193e20dadccb86a532:6085cb508ff64437bfc0f7dd69b05591","requestId":"gty000bdd37@dx193e20dadccb86a532","sessionId":"cid000bdd34@dx193e20dabe9b86a532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"播放琵琶名曲","intentId":"PLAY","intentName":"点歌","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"播放琵琶名曲","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"乐器","value":"琵琶"},{"name":"标签","value":"琵琶|名曲"}]}}},"timestamp":1734664376}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cida31ed5f0@dx0f141ac02cb83eef00"}
连接正常关闭
1734664377
param:b'{\n            "auth_id": "6ed6efd23be64b3bae4a0406dec955bc",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid580628ba@dx132b1ac02cb83eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "0aef5851297347a3b110bf0912344d32","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be3a0@dx193e20dd2e0b8a9532"}
started:
ws start
####################
测试进行: ctm00012592@hu17b66688586020c902#46613825.pcm
{"recordId":"ase000ebbd0@hu193e20de37205c4882:0aef5851297347a3b110bf0912344d32","requestId":"ase000ebbd0@hu193e20de37205c4882","sessionId":"cid000be3a0@dx193e20dd2e0b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664381}
{"recordId":"gty000be3a1@dx193e20dd4b5b8a9532:0aef5851297347a3b110bf0912344d32","requestId":"gty000be3a1@dx193e20dd4b5b8a9532","sessionId":"cid000be3a0@dx193e20dd2e0b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664383}
{"recordId":"gty000be3a1@dx193e20dd4b5b8a9532:0aef5851297347a3b110bf0912344d32","requestId":"gty000be3a1@dx193e20dd4b5b8a9532","sessionId":"cid000be3a0@dx193e20dd2e0b8a9532","eof":"1","text":"请放专辑全面沦陷","pinyin":"","topic":"asr.speech.result","age":"child","ageScore":"","gender":"","genderScore":"","language_class":"mandarin","timestamp":1734664383}
send data finished:1734664384030.181
{"recordId":"gty000be3a1@dx193e20dd4b5b8a9532:0aef5851297347a3b110bf0912344d32","requestId":"gty000be3a1@dx193e20dd4b5b8a9532","sessionId":"cid000be3a0@dx193e20dd2e0b8a9532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"请放专辑全面沦陷","intentId":"PLAY","intentName":"点歌","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"请放专辑全面沦陷","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"来源类型","value":"专辑"},{"name":"专辑名","value":"全面沦陷"},{"name":"歌曲来源","value":"全面沦陷"},{"name":"专辑名","value":"全面沦陷"}]}}},"timestamp":1734664383}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid856225be@dxc4901ac02cbf3eef00"}
连接正常关闭
1734664384
param:b'{\n            "auth_id": "d1fefdcda7424bf3a46302b4eac5cd2d",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid9fd0e91b@dxfe431ac02cbf3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "8581786d0a054ed7a262e20e00d4f5cb","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bdfce@dx193e20dec7db8aa532"}
started:
ws start
####################
测试进行: ctm00012657@hu17b62d10b340212902#46579980.pcm
{"recordId":"gty000bdfcf@dx193e20dee93b8aa532:8581786d0a054ed7a262e20e00d4f5cb","requestId":"gty000bdfcf@dx193e20dee93b8aa532","sessionId":"cid000bdfce@dx193e20dec7db8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664391}
{"recordId":"gty000bdfcf@dx193e20dee93b8aa532:8581786d0a054ed7a262e20e00d4f5cb","requestId":"gty000bdfcf@dx193e20dee93b8aa532","sessionId":"cid000bdfce@dx193e20dec7db8aa532","eof":"1","text":"放热水","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1734664391}
send data finished:1734664392228.1602
{"recordId":"gty000bdfcf@dx193e20dee93b8aa532:8581786d0a054ed7a262e20e00d4f5cb","requestId":"gty000bdfcf@dx193e20dee93b8aa532","sessionId":"cid000bdfce@dx193e20dec7db8aa532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"放热水","intentId":"chat","intentName":"闲聊","nlg":"想破了我的小脑袋都没想出来，你换个问题吧！","shouldEndSession":true},"nlu":{"input":"放热水","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1734664391}
{"recordId":"ase000d7675@hu193e20e0ba10427882:8581786d0a054ed7a262e20e00d4f5cb","requestId":"ase000d7675@hu193e20e0ba10427882","sessionId":"cid000bdfce@dx193e20dec7db8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664391}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid55933890@dxb9171ac02cc73eef00"}
连接正常关闭
1734664392
param:b'{\n            "auth_id": "773270303a8845f0921daa08a8c81dd8",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidb307d9a6@dxc0591ac02cc83eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "dfd0022e331843248ccbe702771bf1c5","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bdd4e@dx193e20e0e04b86a532"}
started:
ws start
####################
测试进行: ctm00012666@hu17b62d110a00212902#46579987.pcm
{"recordId":"ase000ee330@hu193e20e207905bf882:dfd0022e331843248ccbe702771bf1c5","requestId":"ase000ee330@hu193e20e207905bf882","sessionId":"cid000bdd4e@dx193e20e0e04b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664397}
{"recordId":"gty000bdd4f@dx193e20e102db86a532:dfd0022e331843248ccbe702771bf1c5","requestId":"gty000bdd4f@dx193e20e102db86a532","sessionId":"cid000bdd4e@dx193e20e0e04b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664398}
{"recordId":"gty000bdd4f@dx193e20e102db86a532:dfd0022e331843248ccbe702771bf1c5","requestId":"gty000bdd4f@dx193e20e102db86a532","sessionId":"cid000bdd4e@dx193e20e0e04b86a532","eof":"1","text":"赵龙的电视剧","pinyin":"","topic":"asr.speech.result","age":"elder","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1734664398}
send data finished:1734664399024.5166
{"recordId":"gty000bdd4f@dx193e20e102db86a532:dfd0022e331843248ccbe702771bf1c5","requestId":"gty000bdd4f@dx193e20e102db86a532","sessionId":"cid000bdd4e@dx193e20e0e04b86a532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"赵龙的电视剧","intentId":"chat","intentName":"闲聊","nlg":"这个我也不会，还是简单的问题适合我！","shouldEndSession":true},"nlu":{"input":"赵龙的电视剧","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1734664398}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cide8e619ec@dxf7281ac02cce3eef00"}
连接正常关闭
1734664399
param:b'{\n            "auth_id": "685239bda47c4c7a8b9c6272783d6485",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid740b8662@dx35241ac02cce3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "4e56092d54314e54847c875d95c97c0c","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bdd58@dx193e20e27cdb86a532"}
started:
ws start
####################
测试进行: ctm00012677@hu17b62d1193a0212902#46580004.pcm
{"recordId":"ase000d837c@hu193e20e433b0427882:4e56092d54314e54847c875d95c97c0c","requestId":"ase000d837c@hu193e20e433b0427882","sessionId":"cid000bdd58@dx193e20e27cdb86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664405}
{"recordId":"gty000bdd59@dx193e20e29dfb86a532:4e56092d54314e54847c875d95c97c0c","requestId":"gty000bdd59@dx193e20e29dfb86a532","sessionId":"cid000bdd58@dx193e20e27cdb86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664407}
{"recordId":"gty000bdd59@dx193e20e29dfb86a532:4e56092d54314e54847c875d95c97c0c","requestId":"gty000bdd59@dx193e20e29dfb86a532","sessionId":"cid000bdd58@dx193e20e27cdb86a532","eof":"1","text":"每天提醒我晚上10:30睡觉","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1734664407}
send data finished:1734664408429.8015
{"recordId":"gty000bdd59@dx193e20e29dfb86a532:4e56092d54314e54847c875d95c97c0c","requestId":"gty000bdd59@dx193e20e29dfb86a532","sessionId":"cid000bdd58@dx193e20e27cdb86a532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"每天提醒我晚上10:30睡觉","intentId":"chat","intentName":"闲聊","nlg":"咱们聊聊生活上的事情怎么样？","shouldEndSession":true},"nlu":{"input":"每天提醒我晚上10:30睡觉","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1734664407}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidbc3147ff@dxb5281ac02cd73eef00"}
连接正常关闭
1734664408
param:b'{\n            "auth_id": "1369f7a373ce432faf9620f4e812a213",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid6e587605@dxc2311ac02cd83eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "83ed25a2739c486686d27e953aeb11de","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be290@dx193e20e4c817844532"}
started:
ws start
####################
测试进行: ctm00012680@hu17b62d11f180212902#46580015.pcm
{"recordId":"ase000edaac@hu193e20e667c05c4882:83ed25a2739c486686d27e953aeb11de","requestId":"ase000edaac@hu193e20e667c05c4882","sessionId":"cid000be290@dx193e20e4c817844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664414}
{"recordId":"gty000be295@dx193e20e4ea77844532:83ed25a2739c486686d27e953aeb11de","requestId":"gty000be295@dx193e20e4ea77844532","sessionId":"cid000be290@dx193e20e4c817844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664415}
{"recordId":"gty000be295@dx193e20e4ea77844532:83ed25a2739c486686d27e953aeb11de","requestId":"gty000be295@dx193e20e4ea77844532","sessionId":"cid000be290@dx193e20e4c817844532","eof":"1","text":"北京天气冷不冷","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1734664416}
send data finished:1734664416880.3806
{"recordId":"gty000be295@dx193e20e4ea77844532:83ed25a2739c486686d27e953aeb11de","requestId":"gty000be295@dx193e20e4ea77844532","sessionId":"cid000be290@dx193e20e4c817844532","topic":"dm.output","skill":"天气","skillId":"2019042500000544","speakUrl":"","error":{},"dm":{"input":"北京天气冷不冷","intentId":"QUERY","intentName":"查询天气信息","nlg":"北京市现在3度，今天-6℃ ~ 3℃，天气寒冷，注意保暖。","widget":{"webhookResp":{"result":[{"airData":56,"airQuality":"良","city":"北京市","date":"2024-12-20","dateLong":1734624000,"date_for_voice":"今天","exp":{"ct":{"expName":"穿衣指数","level":"寒冷","prompt":"外面天寒地冻，防寒保暖最重要，帽子、围巾、手套全副武装，不宜室外逛街。"},"dy":{"expName":"钓鱼指数","level":"较适宜","prompt":"温差稍大，鱼儿不太活跃，可能会对钓鱼产生影响。"},"gm":{"expName":"感冒指数","level":"较易发","prompt":"感冒较易发生，干净整洁的环境和清新流通的空气都有利于降低感冒的几率，体质较弱的童鞋们要特别加强自我保护。"},"jt":{"expName":"交通指数","level":"良好","prompt":"天气较好，路面干燥，交通气象条件良好，车辆可以正常行驶。"},"tr":{"expName":"旅游指数","level":"一般","prompt":"天空状况还是比较好的，但温度比较低，且风稍大，会让人感觉有点冷。外出请备上防风保暖衣物。"},"uv":{"expName":"紫外线指数","level":"最弱","prompt":"辐射弱，请涂擦SPF8-12防晒护肤品。"},"xc":{"expName":"洗车指数","level":"较适宜","prompt":"较适宜洗车，未来一天无雨，风力较小，擦洗一新的汽车至少能保持一天。"},"yd":{"expName":"运动指数","level":"不适宜","prompt":"气温过低，特别容易着凉感冒，较不适宜户外运动，建议室内运动。"}},"extra":"","humidity":"24%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2024-12-20 11:00:08","pm25":"50","precipitation":"0","sunRise":"2024-12-20 07:32:00","sunSet":"2024-12-20 16:52:00","temp":3,"tempHigh":"3℃","tempLow":"-6℃","tempRange":"-6℃ ~ 3℃","tempReal":"-2℃","visibility":"","warning":"大风蓝色预警","weather":"多云","weatherDescription":"天气寒冷，注意保暖。","weatherDescription3":"-6℃到4℃，风不大，天气寒冷，注意保暖。北京市气象台发布大风蓝色预警信号。","weatherDescription7":"-5℃到4℃，风不大，天气寒冷，注意保暖。北京市气象台发布大风蓝色预警信号。","weatherType":1,"week":"周五","wind":"东北风3-4级","windLevel":0},{"airData":44,"airQuality":"优","city":"北京市","date":"2024-12-19","dateLong":1734537600,"date_for_voice":"昨天","humidity":"31%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2024-12-20 11:00:08","sunRise":"2024-12-19 07:31:00","sunSet":"2024-12-19 16:52:00","tempHigh":"3℃","tempLow":"-6℃","tempRange":"-6℃ ~ 3℃","weather":"多云","weatherDescription":"天气寒冷，注意保暖。","weatherType":1,"week":"周四","wind":"南风微风","windLevel":0},{"airData":30,"airQuality":"优","city":"北京市","date":"2024-12-21","dateLong":1734710400,"date_for_voice":"明天","humidity":"26%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2024-12-20 11:00:08","sunRise":"2024-12-21 07:32:00","sunSet":"2024-12-21 16:53:00","tempHigh":"4℃","tempLow":"-6℃","tempRange":"-6℃ ~ 4℃","weather":"晴","weatherDescription":"天气寒冷，注意保暖。","weatherType":0,"week":"周六","wind":"西北风3-4级","windLevel":1},{"airData":40,"airQuality":"优","city":"北京市","date":"2024-12-22","dateLong":1734796800,"date_for_voice":"后天","humidity":"24%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2024-12-20 11:00:08","sunRise":"2024-12-22 07:33:00","sunSet":"2024-12-22 16:53:00","tempHigh":"4℃","tempLow":"-7℃","tempRange":"-7℃ ~ 4℃","weather":"晴","weatherDescription":"天气寒冷，注意保暖。","weatherType":0,"week":"周日","wind":"西北风微风","windLevel":0},{"airData":100,"airQuality":"良","city":"北京市","date":"2024-12-23","dateLong":1734883200,"date_for_voice":"23号","humidity":"23%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2024-12-20 11:00:08","sunRise":"2024-12-23 07:33:00","sunSet":"2024-12-23 16:54:00","tempHigh":"4℃","tempLow":"-7℃","tempRange":"-7℃ ~ 4℃","weather":"多云","weatherDescription":"天气寒冷，注意保暖。","weatherType":1,"week":"下周一","wind":"西北风转东北风微风","windLevel":0},{"airData":110,"airQuality":"轻微污染","city":"北京市","date":"2024-12-24","dateLong":1734969600,"date_for_voice":"24号","humidity":"26%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2024-12-20 11:00:08","sunRise":"2024-12-24 07:34:00","sunSet":"2024-12-24 16:54:00","tempHigh":"3℃","tempLow":"-5℃","tempRange":"-5℃ ~ 3℃","weather":"多云","weatherDescription":"天气寒冷，注意保暖。","weatherType":1,"week":"下周二","wind":"东南风转北风微风","windLevel":0},{"airData":60,"airQuality":"良","city":"北京市","date":"2024-12-25","dateLong":1735056000,"date_for_voice":"25号","humidity":"25%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2024-12-20 11:00:08","sunRise":"2024-12-25 07:34:00","sunSet":"2024-12-25 16:55:00","tempHigh":"4℃","tempLow":"-6℃","tempRange":"-6℃ ~ 4℃","weather":"晴","weatherDescription":"天气寒冷，注意保暖。","weatherType":0,"week":"下周三","wind":"西北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2024-12-26","dateLong":1735142400,"date_for_voice":"26号","humidity":"23%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2024-12-20 11:00:08","sunRise":"2024-12-26 07:35:00","sunSet":"2024-12-26 16:55:00","tempHigh":"1℃","tempLow":"-6℃","tempRange":"-6℃ ~ 1℃","weather":"多云","weatherDescription":"天气寒冷，注意保暖。","weatherType":1,"week":"下周四","wind":"西北风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2024-12-27","dateLong":1735228800,"date_for_voice":"27号","humidity":"21%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2024-12-20 11:00:08","sunRise":"2024-12-27 07:35:00","sunSet":"2024-12-27 16:56:00","tempHigh":"3℃","tempLow":"-4℃","tempRange":"-4℃ ~ 3℃","weather":"晴","weatherDescription":"天气寒冷，注意保暖。","weatherType":0,"week":"下周五","wind":"西北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2024-12-28","dateLong":1735315200,"date_for_voice":"28号","humidity":"22%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2024-12-20 11:00:08","sunRise":"2024-12-28 07:35:00","sunSet":"2024-12-28 16:57:00","tempHigh":"3℃","tempLow":"-4℃","tempRange":"-4℃ ~ 3℃","weather":"晴转多云","weatherDescription":"天气寒冷，注意保暖。","weatherType":0,"week":"下周六","wind":"西北风转西南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2024-12-29","dateLong":1735401600,"date_for_voice":"29号","humidity":"23%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2024-12-20 11:00:08","sunRise":"2024-12-29 07:35:00","sunSet":"2024-12-29 16:58:00","tempHigh":"4℃","tempLow":"-4℃","tempRange":"-4℃ ~ 4℃","weather":"阴转晴","weatherDescription":"天气寒冷，注意保暖。","weatherType":2,"week":"下周日","wind":"东南风转西北风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2024-12-30","dateLong":1735488000,"date_for_voice":"30号","humidity":"22%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2024-12-20 11:00:08","sunRise":"2024-12-30 07:36:00","sunSet":"2024-12-30 16:58:00","tempHigh":"2℃","tempLow":"-2℃","tempRange":"-2℃ ~ 2℃","weather":"阴转多云","weatherDescription":"天气寒冷，注意保暖。","weatherType":2,"week":"下下周一","wind":"东南风转西北风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2024-12-31","dateLong":1735574400,"date_for_voice":"31号","humidity":"22%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2024-12-20 11:00:08","sunRise":"2024-12-31 07:36:00","sunSet":"2024-12-31 16:59:00","tempHigh":"0℃","tempLow":"-4℃","tempRange":"-4℃ ~ 0℃","weather":"晴","weatherDescription":"天气寒冷，注意保暖。","weatherType":0,"week":"下下周二","wind":"西北风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-01-01","dateLong":1735660800,"date_for_voice":"1号","humidity":"24%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2024-12-20 11:00:08","sunRise":"2025-01-01 07:36:00","sunSet":"2025-01-01 17:00:00","tempHigh":"1℃","tempLow":"-5℃","tempRange":"-5℃ ~ 1℃","weather":"晴","weatherDescription":"天气寒冷，注意保暖。","weatherType":0,"week":"下下周三","wind":"西北风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-01-02","dateLong":1735747200,"date_for_voice":"2号","humidity":"27%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2024-12-20 11:00:08","sunRise":"2025-01-02 07:36:00","sunSet":"2025-01-02 17:01:00","tempHigh":"1℃","tempLow":"-5℃","tempRange":"-5℃ ~ 1℃","weather":"晴","weatherDescription":"天气寒冷，注意保暖。","weatherType":0,"week":"下下周四","wind":"西北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-01-03","dateLong":1735833600,"date_for_voice":"3号","humidity":"28%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2024-12-20 11:00:08","sunRise":"2025-01-03 07:36:00","sunSet":"2025-01-03 17:02:00","tempHigh":"-1℃","tempLow":"-5℃","tempRange":"-5℃ ~ -1℃","weather":"晴","weatherDescription":"天气寒冷，注意保暖。","weatherType":0,"week":"下下周五","wind":"西北风微风","windLevel":0}]},"cityName":"北京市"},"dm_intent":"custom","cityName":"北京市","shouldEndSession":true},"nlu":{"input":"北京天气冷不冷","skill":"天气","skillId":"2019042500000544","skillVersion":"56","semantics":{"request":{"intent":"QUERY","slots":[{"name":"queryType","value":"程度"},{"name":"queryValue","value":"冷"},{"name":"subfocus","value":"温度"},{"name":"datetime","normValue":"{\"datetime\":\"2024-12-20\",\"suggestDatetime\":\"2024-12-20\"}","value":"2024-12-20"},{"name":"location.city","normValue":"北京市","value":"北京市"},{"name":"location.cityAddr","normValue":"北京","value":"北京"},{"name":"location.type","normValue":"LOC_BASIC","value":"LOC_BASIC"}]}}},"timestamp":1734664416}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid450b4f75@dx19171ac02ce03eef00"}
连接正常关闭
1734664417
param:b'{\n            "auth_id": "2ef554da0e324088872832f0ce3c5e9f",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cida90b26db@dx66eb1ac02ce03eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "1dde2f53c7514a1485665916e67031b3","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bdfe4@dx193e20e6d12b8aa532"}
started:
ws start
####################
测试进行: ctm00012f61@hu17b57ba0fdc020c902#46419822.pcm
{"recordId":"ase000ee05e@hu193e20e7e6905c4882:1dde2f53c7514a1485665916e67031b3","requestId":"ase000ee05e@hu193e20e7e6905c4882","sessionId":"cid000bdfe4@dx193e20e6d12b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664421}
{"recordId":"gty000bdfe5@dx193e20e6f32b8aa532:1dde2f53c7514a1485665916e67031b3","requestId":"gty000bdfe5@dx193e20e6f32b8aa532","sessionId":"cid000bdfe4@dx193e20e6d12b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664422}
{"recordId":"gty000bdfe5@dx193e20e6f32b8aa532:1dde2f53c7514a1485665916e67031b3","requestId":"gty000bdfe5@dx193e20e6f32b8aa532","sessionId":"cid000bdfe4@dx193e20e6d12b8aa532","eof":"1","text":"那是不是现在11:30","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664422}
send data finished:1734664423383.4165
{"recordId":"gty000bdfe5@dx193e20e6f32b8aa532:1dde2f53c7514a1485665916e67031b3","requestId":"gty000bdfe5@dx193e20e6f32b8aa532","sessionId":"cid000bdfe4@dx193e20e6d12b8aa532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"那是不是现在11:30","intentId":"chat","intentName":"闲聊","nlg":"那是什么呀?我真不知道啊。","shouldEndSession":true},"nlu":{"input":"那是不是现在11:30","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1734664422}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid6f97fa24@dxc9e81ac02ce63eef00"}
连接正常关闭
1734664423
param:b'{\n            "auth_id": "54d9e4e08261402abdcf7d240ca32f69",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cide036b660@dx1a541ac02ce73eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "19e9990e5341473d9666d5560df12820","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be2a8@dx193e20e87367844532"}
started:
ws start
####################
测试进行: ctm00012f70@hu17b57ba16c7020c902#46419837.pcm
{"recordId":"ase000d26db@hu193e20e97b204d3882:19e9990e5341473d9666d5560df12820","requestId":"ase000d26db@hu193e20e97b204d3882","sessionId":"cid000be2a8@dx193e20e87367844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664427}
{"recordId":"gty000be2a9@dx193e20e89257844532:19e9990e5341473d9666d5560df12820","requestId":"gty000be2a9@dx193e20e89257844532","sessionId":"cid000be2a8@dx193e20e87367844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664428}
{"recordId":"gty000be2a9@dx193e20e89257844532:19e9990e5341473d9666d5560df12820","requestId":"gty000be2a9@dx193e20e89257844532","sessionId":"cid000be2a8@dx193e20e87367844532","eof":"1","text":"今天是农历多少","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664428}
send data finished:1734664429188.9514
{"recordId":"gty000be2a9@dx193e20e89257844532:19e9990e5341473d9666d5560df12820","requestId":"gty000be2a9@dx193e20e89257844532","sessionId":"cid000be2a8@dx193e20e87367844532","topic":"dm.output","skill":"时间日期","skillId":"IFLYTEK.datetimePro","speakUrl":"","error":{},"dm":{"input":"今天是农历多少","intentId":"WHATDATE","intentName":"查日期","nlg":"今天是甲辰年冬月二十，星期五，2024年12月20号。","shouldEndSession":true},"nlu":{"input":"今天是农历多少","skill":"时间日期","skillId":"IFLYTEK.datetimePro","skillVersion":"463.0","semantics":{"request":{"slots":[{"name":"datetime","normValue":"{\"datetime\":\"2024-12-20\",\"suggestDatetime\":\"2024-12-20\"}","value":"今天"},{"name":"lunar","value":"农历"}]}}},"timestamp":1734664428}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid3f3f067e@dx25fc1ac02cec3eef00"}
连接正常关闭
1734664429
param:b'{\n            "auth_id": "5a764fee62014781827b16a293883d2a",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid9e569cf1@dx00fc1ac02cec3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "6224d190409c423287833152ea5f6db9","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bdd71@dx193e20e9d53b86a532"}
started:
ws start
####################
测试进行: ctm00012f7d@hu17b57ba1d26020c902#46419853.pcm
{"recordId":"ase000d9e72@hu193e20eafde0427882:6224d190409c423287833152ea5f6db9","requestId":"ase000d9e72@hu193e20eafde0427882","sessionId":"cid000bdd71@dx193e20e9d53b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664433}
{"recordId":"gty000bdd74@dx193e20e9f2fb86a532:6224d190409c423287833152ea5f6db9","requestId":"gty000bdd74@dx193e20e9f2fb86a532","sessionId":"cid000bdd71@dx193e20e9d53b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664433}
{"recordId":"gty000bdd74@dx193e20e9f2fb86a532:6224d190409c423287833152ea5f6db9","requestId":"gty000bdd74@dx193e20e9f2fb86a532","sessionId":"cid000bdd71@dx193e20e9d53b86a532","eof":"1","text":"播放来生缘","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664433}
{"recordId":"gty000bdd74@dx193e20e9f2fb86a532:6224d190409c423287833152ea5f6db9","requestId":"gty000bdd74@dx193e20e9f2fb86a532","sessionId":"cid000bdd71@dx193e20e9d53b86a532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"播放来生缘","intentId":"PLAY","intentName":"点歌","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"播放来生缘","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"歌曲名","value":"来生缘"}]}}},"timestamp":1734664434}
发送结束标识
发送断开连接标识
send data finished:1734664434873.981
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid09893858@dxcbda1ac02cf23eef00"}
连接正常关闭
1734664434
param:b'{\n            "auth_id": "27094ceddeda4bbf84fbde47a1f151ba",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cida2397f07@dxb2351ac02cf23eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "be2c4a2c71f5491f96e88a9f9e525155","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bdff0@dx193e20eb313b8aa532"}
started:
ws start
####################
测试进行: ctm00012f85@hu17b57ba2065020c902#46419861.pcm
{"recordId":"ase000edfef@hu193e20ec33f05c3882:be2c4a2c71f5491f96e88a9f9e525155","requestId":"ase000edfef@hu193e20ec33f05c3882","sessionId":"cid000bdff0@dx193e20eb313b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664438}
{"recordId":"gty000bdff1@dx193e20eb4f7b8aa532:be2c4a2c71f5491f96e88a9f9e525155","requestId":"gty000bdff1@dx193e20eb4f7b8aa532","sessionId":"cid000bdff0@dx193e20eb313b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664439}
{"recordId":"gty000bdff1@dx193e20eb4f7b8aa532:be2c4a2c71f5491f96e88a9f9e525155","requestId":"gty000bdff1@dx193e20eb4f7b8aa532","sessionId":"cid000bdff0@dx193e20eb313b8aa532","eof":"1","text":"赌神国语","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664439}
send data finished:1734664440491.4504
{"recordId":"gty000bdff1@dx193e20eb4f7b8aa532:be2c4a2c71f5491f96e88a9f9e525155","requestId":"gty000bdff1@dx193e20eb4f7b8aa532","sessionId":"cid000bdff0@dx193e20eb313b8aa532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"赌神国语","intentId":"chat","intentName":"闲聊","nlg":"然后呢然后呢，多告诉一些吧。","shouldEndSession":true},"nlu":{"input":"赌神国语","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1734664439}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid339bfbf7@dx4da91ac02cf73eef00"}
连接正常关闭
1734664440
param:b'{\n            "auth_id": "1f057533d2144ec58a431bade5301f04",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"ciddd4a8924@dxd8121ac02cf83eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "74de4659a146459a8bdb86e1e36e8d2b","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bdff4@dx193e20eca13b8aa532"}
started:
ws start
####################
测试进行: ctm00013149@hu17b4fb834340212902#46308369.pcm
{"recordId":"ase000e11b9@hu193e20ee13405bf882:74de4659a146459a8bdb86e1e36e8d2b","requestId":"ase000e11b9@hu193e20ee13405bf882","sessionId":"cid000bdff4@dx193e20eca13b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664446}
{"recordId":"gty000bdff5@dx193e20ecc0eb8aa532:74de4659a146459a8bdb86e1e36e8d2b","requestId":"gty000bdff5@dx193e20ecc0eb8aa532","sessionId":"cid000bdff4@dx193e20eca13b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664447}
{"recordId":"gty000bdff5@dx193e20ecc0eb8aa532:74de4659a146459a8bdb86e1e36e8d2b","requestId":"gty000bdff5@dx193e20ecc0eb8aa532","sessionId":"cid000bdff4@dx193e20eca13b8aa532","eof":"1","text":"关于海底两万里故事","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664447}
send data finished:1734664448381.1711
{"recordId":"gty000bdff5@dx193e20ecc0eb8aa532:74de4659a146459a8bdb86e1e36e8d2b","requestId":"gty000bdff5@dx193e20ecc0eb8aa532","sessionId":"cid000bdff4@dx193e20eca13b8aa532","topic":"dm.output","skill":"故事","skillId":"2019031500001010","speakUrl":"","error":{},"dm":{"input":"关于海底两万里故事","intentId":"QUERY","intentName":"故事点播","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"关于海底两万里故事","skill":"故事","skillId":"2019031500001010","skillVersion":"55","semantics":{"request":{"slots":[{"name":"name","value":"海底两万"}]}}},"timestamp":1734664447}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid9043338a@dx23881ac02cff3eef00"}
连接正常关闭
1734664448
param:b'{\n            "auth_id": "55276e8182de4a8cb57101bd02b71ac1",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid8526519d@dx026d1ac02cff3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "9212155b7ded49a3ae2ba73cbf73ebcc","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be3e4@dx193e20ee7f6b8a9532"}
started:
ws start
####################
测试进行: ctm0001314f@hu17b59b593890212902#46470179.pcm
Error reading file[Errno 2] No such file or directory: 'data/silence.wav'
{"action":"error","code":"20010108","data":"","desc":"{\"code\":20010108,\"sub\":\"\"}","sid":"gty000be3e5@dx193e20eea0bb8a9532"}
{'action': 'error', 'code': '20010108', 'data': '', 'desc': '{"code":20010108,"sub":""}', 'sid': 'gty000be3e5@dx193e20eea0bb8a9532'}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid2a474cb2@dxaea31ac02d1a3eef00"}
连接正常关闭
1734664475
param:b'{\n            "auth_id": "a9b8ff5420ac47868debb975f0d4f496",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid3ed2d89f@dx6c631ac02d1a3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "a3db1329169046fe8038413e6883a859","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be400@dx193e20f506fb8a9532"}
started:
ws start
####################
测试进行: ctm00013177@hu17b59b5a74c0212902#46470228.pcm
Error reading file[Errno 2] No such file or directory: 'data/silence.wav'
{"recordId":"gty000be403@dx193e20f5252b8a9532:a3db1329169046fe8038413e6883a859","requestId":"gty000be403@dx193e20f5252b8a9532","sessionId":"cid000be400@dx193e20f506fb8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664484}
{"recordId":"gty000be403@dx193e20f5252b8a9532:a3db1329169046fe8038413e6883a859","requestId":"gty000be403@dx193e20f5252b8a9532","sessionId":"cid000be400@dx193e20f506fb8a9532","eof":"1","text":"","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1734664484}
{"recordId":"gty000be403@dx193e20f5252b8a9532:a3db1329169046fe8038413e6883a859","requestId":"gty000be403@dx193e20f5252b8a9532","sessionId":"cid000be400@dx193e20f506fb8a9532","topic":"dm.output","skill":"","skillId":"","speakUrl":"","error":{"errId":"010305","errMsg":"asr result is null"},"dm":{},"nlu":{"input":"","skill":"","skillId":""},"timestamp":1734664484}
{"recordId":"ase000e1d87@hu193e20f78ce05c4882:a3db1329169046fe8038413e6883a859","requestId":"ase000e1d87@hu193e20f78ce05c4882","sessionId":"cid000be400@dx193e20f506fb8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664485}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid99119c32@dxe72b1ac02d253eef00"}
连接正常关闭
1734664486
param:b'{\n            "auth_id": "42453d7767b14021b95ba7658b29b6f0",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid9cde6c60@dxe2491ac02d253eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "94b13fb576794d53ae514331fd36c1ac","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be010@dx193e20f7ad8b8aa532"}
started:
ws start
####################
测试进行: ctm0001318e@hu17b59b5b54b0212902#46470267.pcm
{"recordId":"ase000e1057@hu193e20f8a5505c3882:94b13fb576794d53ae514331fd36c1ac","requestId":"ase000e1057@hu193e20f8a5505c3882","sessionId":"cid000be010@dx193e20f7ad8b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664489}
{"recordId":"gty000be011@dx193e20f7ce9b8aa532:94b13fb576794d53ae514331fd36c1ac","requestId":"gty000be011@dx193e20f7ce9b8aa532","sessionId":"cid000be010@dx193e20f7ad8b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664491}
{"recordId":"gty000be011@dx193e20f7ce9b8aa532:94b13fb576794d53ae514331fd36c1ac","requestId":"gty000be011@dx193e20f7ce9b8aa532","sessionId":"cid000be010@dx193e20f7ad8b8aa532","eof":"1","text":"我要听单田芳的裸体","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664491}
send data finished:1734664492742.1616
{"recordId":"gty000be011@dx193e20f7ce9b8aa532:94b13fb576794d53ae514331fd36c1ac","requestId":"gty000be011@dx193e20f7ce9b8aa532","sessionId":"cid000be010@dx193e20f7ad8b8aa532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"我要听单田芳的裸体","intentId":"chat","intentName":"闲聊","nlg":"我还小呢，妈妈说不能聊这个！","shouldEndSession":true},"nlu":{"input":"我要听单田芳的裸体","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1734664491}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidd7650d4b@dx52961ac02d2b3eef00"}
连接正常关闭
1734664492
param:b'{\n            "auth_id": "a3feefb117d54a478e0a73f74834f295",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidacffecdf@dx8a651ac02d2c3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "4bb255d35e7047cb9839fe1b2a0781a3","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be016@dx193e20f9530b8aa532"}
started:
ws start
####################
测试进行: ctm0001319b@hu17b59b5bc840212902#46470281.pcm
{"recordId":"gty000be017@dx193e20f96f7b8aa532:4bb255d35e7047cb9839fe1b2a0781a3","requestId":"gty000be017@dx193e20f96f7b8aa532","sessionId":"cid000be016@dx193e20f9530b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1734664497}
{"recordId":"gty000be017@dx193e20f96f7b8aa532:4bb255d35e7047cb9839fe1b2a0781a3","requestId":"gty000be017@dx193e20f96f7b8aa532","sessionId":"cid000be016@dx193e20f9530b8aa532","eof":"1","text":"地暖模式","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1734664497}
send data finished:1734664498268.6018
{"recordId":"gty000be017@dx193e20f96f7b8aa532:4bb255d35e7047cb9839fe1b2a0781a3","requestId":"gty000be017@dx193e20f96f7b8aa532","sessionId":"cid000be016@dx193e20f9530b8aa532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"地暖模式","intentId":"chat","intentName":"闲聊","nlg":"咱们聊聊生活上的事情怎么样？","shouldEndSession":true},"nlu":{"input":"地暖模式","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1734664497}
{"recordId":"ase000fb81f@hu193e20fa95105c0882:4bb255d35e7047cb9839fe1b2a0781a3","requestId":"ase000fb81f@hu193e20fa95105c0882","sessionId":"cid000be016@dx193e20f9530b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1734664497}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid49f41e58@dx15631ac02d313eef00"}
连接正常关闭
1734664498
param:b'{\n            "auth_id": "e048ee99a61d49ef855fb1688131f658",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidc4b18b88@dxa43d1ac02d323eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "2a05aed5a7ca4aafbd0d1292e26b2ccc","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bdd9d@dx193e20fac3cb86a532"}
started:
ws start
####################
测试进行: ctm00013521@hu17b4fbfcfa30212902#46308926.pcm
