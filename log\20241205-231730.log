1733411851
param:b'{\n            "auth_id": "b948062952334ea696b505cac8e15e27",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid640adf44@dxe6921aad100c3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"fullduplex", "params": {"mid": "opus-396e911f49af4439a905c5d5e94368d1","fullduplex":{"lat": "31.83","lng": "117.14","aue": "raw","encoding": "opus-wb","eos":300,"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw","sample_rate": 16000, "channels":1,"bit_depth": 16}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000dc95a@dx1939765cfce7824532"}
started:
ws start
####################
测试进行: iflyAudioRaw987077508154796300_2.tmp
{"recordId":"wgw000dc95b@dx1939765d7867824532:opus-396e911f49af4439a905c5d5e94368d1:cid000dc95a@dx1939765cfce7824532","requestId":"wgw000dc95b@dx1939765d7867824532","sessionId":"cid000dc95a@dx1939765cfce7824532","vadEndState":0,"vadState":1,"topic":"asr.vad.start","timestamp":1733411854}
{"recordId":"wgw000dc95b@dx1939765d7867824532:opus-396e911f49af4439a905c5d5e94368d1:cid000dc95a@dx1939765cfce7824532","requestId":"wgw000dc95b@dx1939765d7867824532","sessionId":"cid000dc95a@dx1939765cfce7824532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1733411856}
{"recordId":"wgw000dc95b@dx1939765d7867824532:opus-396e911f49af4439a905c5d5e94368d1:cid000dc95a@dx1939765cfce7824532","requestId":"wgw000dc95b@dx1939765d7867824532","sessionId":"cid000dc95a@dx1939765cfce7824532","pinyin":"","topic":"asr.speech.sentence","language_class":"mandarin","timestamp":1733411856,"contentId":"wgw000dc95b@dx1939765d7867824532","sentence":"1234","conf":"1"}
发送结束标识
发送断开连接标识
send data finished:1733411855877.0854
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid9030d6e4@dx63fa1aad10103eef00"}
连接正常关闭
#####运行完成,点击查看结果按钮查看结果!##########
