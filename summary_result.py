import glob

import xlrd
import pandas as pd

result_dict = {'识别结果': [],
               '测试结果': [],
               'mid': [],
               '第一帧到接收vad的时间': [],
               '接收vad到finish为true的时间': [],
               '日志': []}


def find_position(my_list, string):
    for index, item in enumerate(my_list):
        if string in item:
            return index
    return -1


def write_excel():
    result_df = pd.DataFrame(result_dict)

    # create an excel writer object
    with pd.ExcelWriter("data/测试结果汇总.xlsx") as writer:
        # use to_excel function and specify the sheet_name and index
        # to store the dataframe in specified sheet
        result_df.to_excel(writer, sheet_name="测试结果汇总", index=False)


if __name__ == '__main__':
    file_list = glob.glob('data/*sum*.xls')
    for file_name in file_list:
        print(file_name)
        old_workbook = xlrd.open_workbook(file_name, formatting_info=True)

        # Get worksheet names
        worksheet_names = old_workbook.sheet_names()

        for sheet_name in worksheet_names:
            if sheet_name == 'Sheet2':
                old_worksheet = old_workbook.sheet_by_name(sheet_name)
                # 获取表的行数与列数（并判断是否抽取行数，不够抽取所有行）
                rows = old_worksheet.nrows
                cols = old_worksheet.ncols

                # 获取对应的列名和索引
                col_name = [old_worksheet.cell(0, i).value for i in range(cols)]
                lst = ["这是一个测试", "这是识别结果", "这是另一个测试"]
                asr_result_index = find_position(col_name, '识别结果')
                test_result_index = find_position(col_name, '测试结果')
                mid_index = find_position(col_name, 'mid')
                vad_time_index = find_position(col_name, '第一帧到接收vad的时间')
                finish_time_index = find_position(col_name, '接收vad到finish为true的时间')
                result_dict['识别结果'].append(old_worksheet.row_values(1)[asr_result_index])
                result_dict['测试结果'].append(old_worksheet.row_values(1)[test_result_index])
                result_dict['mid'].append(old_worksheet.row_values(1)[mid_index])
                result_dict['第一帧到接收vad的时间'].append(old_worksheet.row_values(1)[vad_time_index])
                result_dict['接收vad到finish为true的时间'].append(old_worksheet.row_values(1)[finish_time_index])
                result_dict['日志'].append(file_name)

    write_excel()
