1751017658
param:b'{\n            "auth_id": "6bcf519f1178420087d8b5de22b6229a",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid43aef02e@dx8ebe1bb9443b3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "2937189dab9b4db7996716206439fa23","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bdfb7@dx197b0c91c24b8a9532"}
started:
ws start
####################
测试进行: F004_normal_01.pcm
{"recordId":"ase000eeb5d@hu197b0c9268605c2882:2937189dab9b4db7996716206439fa23","requestId":"ase000eeb5d@hu197b0c9268605c2882","sessionId":"cid000bdfb7@dx197b0c91c24b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1751017662}
ts:1751017662.2516546, sec_count:1, send silence begin
{"recordId":"gty000bdfbd@dx197b0c91dd1b8a9532:2937189dab9b4db7996716206439fa23","requestId":"gty000bdfbd@dx197b0c91dd1b8a9532","sessionId":"cid000bdfb7@dx197b0c91c24b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1751017662}
{"recordId":"gty000bdfbd@dx197b0c91dd1b8a9532:2937189dab9b4db7996716206439fa23","requestId":"gty000bdfbd@dx197b0c91dd1b8a9532","sessionId":"cid000bdfb7@dx197b0c91c24b8a9532","eof":"1","text":"空调打开送风","pinyin":"","topic":"asr.speech.result","age":"child","ageScore":"","gender":"","genderScore":"","language_class":"mandarin","timestamp":1751017662}
send data finished:1751017662.9672182
{"recordId":"gty000bdfbd@dx197b0c91dd1b8a9532:2937189dab9b4db7996716206439fa23","requestId":"gty000bdfbd@dx197b0c91dd1b8a9532","sessionId":"cid000bdfb7@dx197b0c91c24b8a9532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"空调打开送风","intentId":"chat","intentName":"闲聊","nlg":"这个我不太懂，我们聊聊别的吧。","shouldEndSession":true},"nlu":{"input":"空调打开送风","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1751017663}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidcfa1a65d@dx99781bb9443f3eef00"}
连接正常关闭
#####运行完成,点击查看结果按钮查看结果!##########
