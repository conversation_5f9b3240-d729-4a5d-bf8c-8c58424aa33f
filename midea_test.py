import io
import threading
import uuid

import asyncio
import time
import os
import hashlib
import base64
import json
import argparse
import sys
from queue import Queue, Empty
from datetime import datetime

from xlutils.copy import copy
from concurrent.futures import ThreadPoolExecutor
from ws4py.client.threadedclient import WebSocketClient

from threading import Lock
import xlrd
from asr_common.asr_common import getRealText, handle_str

log_lock = Lock()
excel_lock = Lock()
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf8')

# 线上环境
# base_url = "wss://wsapi.xfyun.cn/midea/proxy"
# trans_url = "https://itrans.xfyun.cn/v2/its"
# app_id = "5e017b34"
# api_key = "eed5bf68c7e01d85b3713d90bde3154a"
# api_secret = "0e3724a6013368a53ba21c34293705d4"

# UAT环境
base_url = "wss://staging-beijing-midea.listenai.com/midea/proxy"
trans_url = "https://itrans.xfyun.cn/v2/its"
app_id = "5e017b34"
api_key = "eed5bf68c7e01d85b3713d90bde3154a"
api_secret = "0e3724a6013368a53ba21c34293705d4"

# 灰度环境
# base_url = "wss://aiui-test.openspeech.cn/midea/proxy"
# trans_url = "https://itrans.xfyun.cn/v2/its"
# app_id = "5e017b34"
# api_key = "eed5bf68c7e01d85b3713d90bde3154a"
# api_secret = "0e3724a6013368a53ba21c34293705d4"

scene = "mqasr"
# 数据类型，（text、audio）
data_type = "audio"
eos = 500
enable_vpr_rec = True

start_date = datetime.now().strftime("%Y%m%d")
start_time = datetime.now().strftime("%Y%m%d-%H%M%S")

# 从此路径中获取test_data表格里的音频
audio_path = "./data/方言免切/粤语"
# 表格里运行的音频和预期的识别文本
test_data = "./data/方言免切.xls"

# 发送的文本
text_msg = "深圳今天天气怎么样"

# encode = 'opus-wb'
encode = 'raw'

# Interval
INTERVAL = 40
# Slice Size
if encode == 'raw':
    SLICE_SIZE = int(16000 * 1 * 2 * INTERVAL / 1000)
else:
    SLICE_SIZE = 124
SAMPLE_RATE = 16000
SAMPLE_SIZE = 2
CHANNEL_COUNT = 1
new_excel_name = None
new_workbook = None
new_worksheet = None
iat_success = 0
iat_fail = 0


def init_args():
    parse = argparse.ArgumentParser()
    parse.add_argument('--thread-no', type=str, default="None", help='Thread no')
    args = parse.parse_args()
    return vars(args)


def custom_print(content):
    # 打印到控制台
    print(content)
    # 打印到文件
    log_dir = './log'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    with log_lock:
        args = init_args()
        thread_no = args['thread_no']
        if thread_no != "None":
            log_name = f'{log_dir}/{start_time}_sum_{thread_no}.log'
        else:
            log_name = f'{log_dir}/{start_time}.log'
        with open(log_name, 'a', encoding='utf-8') as f:
            print(content, file=f)


def find_dir(dir_name):
    '''
    获取原始音频下的所有文件
    :param dir_name:
    :return:
    '''
    file_list = []
    for path_name, _, files_name in os.walk(dir_name):
        for file in files_name:
            file_list.append(os.path.join(path_name, file))
    return file_list

def send_by_slice(conn, data, row, cols, queue):
    '''
    将大的数据块分割成小的片段
    :param conn:
    :param data:
    :return:
    '''
    sent_data_size = 0
    end_point_flag = False
    vad_flag = False
    # silent_data_size = int(SAMPLE_RATE*SAMPLE_SIZE*CHANNEL_COUNT*(eos/1000))
    # silent_data = b'0x00'*int(silent_data_size/SAMPLE_SIZE)
    # modified_data = data + silent_data
    slice_num = (len(data) + SLICE_SIZE - 1) // SLICE_SIZE

    for i in range(slice_num):
        time_start = int(time.time() * 1000)
        if (i + 1) * SLICE_SIZE < len(data):
            slice_data = data[i * SLICE_SIZE: (i + 1) * SLICE_SIZE]
        else:
            slice_data = data[i * SLICE_SIZE:]

        sent_data_size += len(slice_data)  # 更新计数器

        try:
            # custom_print(f"ts:{time.time()}, audio name:{audio_name}, slice_num:{i}")
            conn.send(slice_data, True)
            # custom_print(f"slice_num:{(i+1)*2}")

        except Exception as e:
            custom_print(f"send msg err:{str(e)}")

        vad_data = sent_data_size
        if conn.end_point_ms is not None:
            if sent_data_size / (32000 / 1000) > conn.end_point_ms and not end_point_flag:
                conn.end_point = time.time() * 1000
                end_point_flag = True
        try:
            msg = queue.get_nowait()
            if msg == "asr.vad.end":
                vad_flag = True
                break
        except Empty:
            pass
        time_end = int(time.time() * 1000)
        time_elapsed = time_end - time_start
        # if not time_elapsed == 0:
            # print(f"script time:{time_elapsed}")
        if time_elapsed / 1000 <= INTERVAL / 1000 - 0.001:
            time.sleep(INTERVAL / 1000 - 0.001 - time_elapsed / 1000)

    if not scene == "fullduplex":
        if not vad_flag:
            sec_count = 1
            custom_print(f"ts:{time.time()}, sec_count:{sec_count}, send silence begin")
            if encode == 'raw':
                f = open('data/silence.pcm', 'rb')
            else:
                f = open('data/silence.opus', 'rb')
            i = 0
            while True:
                time_start = int(time.time() * 1000)
                slice_data = f.read(SLICE_SIZE)
                # print(f"slice_data:{slice_data}")
                if len(slice_data) > 0:
                    sent_data_size += len(slice_data)  # 更新计数器
                    try:
                        # custom_print(f"ts:{time.time()}, slice_num:{slice_num + i}")
                        conn.send(slice_data, True)
                        # custom_print(f"slice_num:{(slice_num + i + 1)*2}")

                    except Exception as e:
                        custom_print(f"send msg err:{str(e)}")

                    vad_data = sent_data_size
                    if conn.end_point_ms is not None:
                        if sent_data_size / (32000 / 1000) > conn.end_point_ms and not end_point_flag:
                            conn.end_point = time.time() * 1000
                            end_point_flag = True
                    try:
                        msg = queue.get_nowait()
                        if msg == "asr.vad.end":
                            vad_flag = True
                            break
                    except Empty:
                        pass
                    time_end = int(time.time() * 1000)
                    time_elapsed = time_end - time_start
                    # if not time_elapsed == 0:
                    #     print(f"script time:{time_elapsed}")
                    if time_elapsed / 1000 <= INTERVAL / 1000 - 0.001:
                        time.sleep(INTERVAL / 1000 - 0.001 - time_elapsed / 1000)
                else:
                    if sec_count >= 10:
                        break
                    else:
                        f.close()
                        if encode == 'raw':
                            f = open('data/silence.pcm', 'rb')
                        else:
                            f = open('data/silence.opus', 'rb')
                        sec_count += 1
                        custom_print(f"sec_count:{sec_count}")
                i += 1

            f.close()

    with excel_lock:
        new_worksheet.write(row, cols + 4, vad_data)
        # 每毫秒的字节数 =32000 /1000=32 字节
        new_worksheet.write(row, cols + 5, vad_data / (32000 / 1000))
        # new_workbook.save(new_excel_name)

    custom_print(f"send data finished:{time.time()}")
    # 写入音频数据
    # pylint: disable=no-member
    # 保存为.wav文件
    # with wave.open("./vadwav/" + audio_name + ".wav", 'wb') as wav_file:
    #     wav_file.setnchannels(1)  # 设置声道数
    #     wav_file.setsampwidth(2)  # 设置采样宽度（字节）
    #     wav_file.setframerate(16000)  # 设置采样率
    #     wav_file.writeframes(data[: vad_data])


def get_auth_id():
    mac = uuid.UUID(int=uuid.getnode()).hex[-12:]
    return hashlib.md5(":".join([mac[e:e + 2] for e in range(0, 11, 2)]).encode("utf-8")).hexdigest()


async def process_task(sheet_name, audio_name, row, cols, expected_text, end_point=None):
    try:
        # 构造握手参数
        cur_time = int(time.time())
        custom_print(cur_time)
        auth_id = uuid.uuid4().hex
        # auth_id = "device123"

        param_general = """{{
            "auth_id": "{0}",
            "data_type": "{1}"
        }}"""

        param = param_general.format(auth_id, data_type).encode(encoding="utf-8")
        custom_print("param:{}".format(param))
        # param_general = "{\"auth_id\": \"a8dc76628e860a5577e950e6ef7e3309\",\"data_type\": \"audio\"}"
        # param = param_general.encode(encoding="utf-8")
        param_base64 = base64.b64encode(param).decode()
        check_sum_pre = api_key + str(cur_time) + param_base64
        checksum = hashlib.md5(check_sum_pre.encode("utf-8")).hexdigest()
        conn_param = "?appid=" + app_id + "&checksum=" + checksum + "&curtime=" + str(
            cur_time) + "&param=" + param_base64 + "&signtype=md5"

        queue = Queue()
        ws = WsapiClient(url=base_url + conn_param, protocols=['chat'], headers=[("Origin", "http://127.0.0.1:1024")],
                         sheet_name=sheet_name, row=row, cols=cols, expected_text=expected_text,
                         start_timestamp=cur_time,
                         queue=queue, audio_name=audio_name, end_point=end_point)
        ws.connect()
        # task = asyncio.create_task(send_data(ws, audio_name, row, cols, queue))
        # await task
        ws.run_forever()
    except Exception as e:
        custom_print(f"连接异常：{e}")


def process_row(args):
    sheet_name, audio_name, row, cols, except_result = args
    asyncio.run(process_task(sheet_name, audio_name, row, cols, except_result))


def main(config_file, extract_sheet=None, extract_sheet_num=0, is_parallel=False):
    if extract_sheet is None:
        extract_sheet = []
    # pylint: disable=global-statement
    global new_excel_name, new_workbook, new_worksheet, iat_success, iat_fail

    # 复制表格并读新表内容
    name, extension = os.path.splitext(config_file)
    args = init_args()
    thread_no = args['thread_no']

    if thread_no != "None":
        new_excel_name = f"{name}_sum_{start_time}_{thread_no}{extension}"
    else:
        new_excel_name = f"{name}_结果汇总_{start_date}{extension}"
    old_workbook = xlrd.open_workbook(config_file, formatting_info=True)
    new_workbook = copy(old_workbook)
    # new_workbook.save(new_excel_name)

    # Get worksheet names
    worksheet_names = extract_sheet if extract_sheet else old_workbook.sheet_names()

    for sheet_name in worksheet_names:
        iat_success = 0
        iat_fail = 0

        old_worksheet = old_workbook.sheet_by_name(sheet_name)
        new_worksheet = new_workbook.get_sheet(sheet_name)

        # 获取表的行数与列数（并判断是否抽取行数，不够抽取所有行）
        rows = min(old_worksheet.nrows,
                   extract_sheet_num + 1 if 1 < extract_sheet_num + 1 < old_worksheet.nrows else old_worksheet.nrows)
        cols = old_worksheet.ncols

        # 获取对应的列名和索引
        col_name = [old_worksheet.cell(0, i).value for i in range(cols)]
        # custom_print("列名:", col_name)
        audio_index = col_name.index('音频')
        expect_result_index = col_name.index('文本') if '文本' in col_name else None
        end_point_index = col_name.index('后端点') if '后端点' in col_name else None

        with excel_lock:
            # Write headers to new worksheet
            new_worksheet.write(0, cols, f'识别结果-{start_date}')
            new_worksheet.write(0, cols + 2, f'asr结果的requestId-{start_date}')
            new_worksheet.write(0, cols + 3, f'mid-{start_date}')
            new_worksheet.write(0, cols + 4, f'接收vad时已发送数据大小-{start_date}')
            new_worksheet.write(0, cols + 5, f'接收vad时的ms数-{start_date}')
            new_worksheet.write(0, cols + 6, f'第一帧的时间-{start_date}')
            new_worksheet.write(0, cols + 7, f'接收vad的时间-{start_date}')
            new_worksheet.write(0, cols + 8, f'第一帧到接收vad的时间-{start_date}')
            new_worksheet.write(0, cols + 9, f'接收finish为true的时间-{start_date}')
            new_worksheet.write(0, cols + 10, f'接收vad到finish为true的时间-{start_date}')
            new_worksheet.write(0, cols + 11, f'接收asr到nlp的时间-{start_date}')
            if end_point_index is not None:
                new_worksheet.write(0, cols + 12, f'从端点2到VAD时间-{start_date}')
            if expect_result_index is not None:
                new_worksheet.write(0, cols + 1, f'测试结果-{start_date}')
            # new_workbook.save(new_excel_name)

        if end_point_index is not None:
            # Prepare tasks for processing
            tasks = [(sheet_name, old_worksheet.row_values(row)[audio_index], row, cols,
                      old_worksheet.row_values(row)[expect_result_index] if expect_result_index is not None else "",
                      old_worksheet.row_values(row)[end_point_index],)
                     for row in range(1, rows) if old_worksheet.row_values(row)[audio_index] != '']
        else:
            # Prepare tasks for processing
            tasks = [(sheet_name, old_worksheet.row_values(row)[audio_index], row, cols,
                      old_worksheet.row_values(row)[expect_result_index] if expect_result_index is not None else "",
                      )
                     for row in range(1, rows) if old_worksheet.row_values(row)[audio_index] != '']

        # Process tasks
        if is_parallel and sheet_name != '云端识别耗时':
            # 并行执行
            with ThreadPoolExecutor(max_workers=20) as executor:
                executor.map(process_row, tasks)
        else:
            # 串行执行
            for index, task in enumerate(tasks):
                print("{}/{}".format(index, len(tasks)))
                asyncio.run(process_task(*task))

        if expect_result_index:
            success_rate = round(iat_success / (iat_success + iat_fail) * 100, 2)
            new_worksheet.write(rows, cols, '识别成功率：')
            new_worksheet.write(rows, cols + 1, f'{success_rate}%')
            new_workbook.save(new_excel_name)

    custom_print("#####运行完成,点击查看结果按钮查看结果!##########")


class WsapiClient(WebSocketClient):
    def __init__(self, sheet_name, row, cols, expected_text, start_timestamp, queue, url, protocols, headers,
                 audio_name, end_point=None):
        super().__init__(url, protocols=protocols, headers=headers)
        self.sheet_name = sheet_name
        self.row = row
        self.cols = cols
        self.expected_text = expected_text
        self.start_time = start_timestamp
        self.queue = queue
        self.real_text_list = []
        self.timestamp_end = 0
        self.timestamp_isfinish = 0
        self.timestamp_nlp = 0
        self.vpr_end_flag = False
        self.nlp_end_flag = False
        self.t1 = None
        self.audio_name = audio_name
        if end_point is not None:
            self.end_point_ms = int(end_point)
            self.end_point = 0
        else:
            self.end_point_ms = None
            self.end_point = None

    def opened(self):
        def send_data():
            # 找到音频的真实路径
            custom_print("#" * 20)
            custom_print("测试进行: " + self.audio_name)
            # 搜索 audio_path 下匹配 audio_name 的文件
            found_file = None
            for root, _, files in os.walk(audio_path):
                if self.audio_name in files:
                    found_file = os.path.join(root, self.audio_name)
                    break

            if not found_file:
                raise FileNotFoundError(f"未在目录 {audio_path} 中找到文件 {self.audio_name}")

            # 发送音频
            try:
                with open(found_file, 'rb') as f:
                    audio_data = f.read()
                    send_by_slice(self, audio_data, self.row, self.cols, self.queue)
            except Exception as e:
                custom_print(f"Error reading file: {str(e)}")

        self.t1 = threading.Thread(target=send_data, args=())

    def join(self, timeout=None):
        pass

    def closed(self, code, reason=None):
        if code == 1000:
            custom_print("连接正常关闭")
        elif code == 1006:
            # self.terminate()
            pass
        else:
            custom_print("连接异常关闭，code：" + str(code) + " ，reason：" + str(reason))

    def create_session(self):
        mid = uuid.uuid4().hex
        if encode == "opus-wb":
            mid = f"opus-{mid}"
        if enable_vpr_rec:
            vpr_rec_string = "\"enable_vpr_rec\": true,\"enable_vpr_rec_info\": true,\"enable_vpr_cluster\": true,\"vpr_rec\":{\"group_id\":\"abc_123\"},"
        else:
            vpr_rec_string = "\"enable_vpr_rec\": false,\"enable_vpr_rec_info\": false,"
        param_iat = "{\"action\":\"start\",\"scene\":\"" + scene + "\", \"params\": {\"mid\": \"" + mid + "\",\"asr\":{\"eos\":" + str(
            eos) + "," + vpr_rec_string + f"\"encoding\": \"{encode}\",\"channels\": " + str(
            CHANNEL_COUNT) + ",\"bit_depth\": " + str(SAMPLE_SIZE * 8) + ",\"sample_rate\": " + str(
            SAMPLE_RATE) + ",\"pd\":\"midea|midea-dragon\"},\"nlu\":{\"vcn\": \"x2_xiaojuan\",\"pitch\": 60,\"speed\": 60,\"volume\": 50,\"tts_aue\": \"raw\"}}}"
        # param_iat = "{\"action\":\"start\",\"scene\":\"" + scene + "\", \"params\": {\"mid\": \"" + mid + "\",\"asr\":{\"asr_vad\": \"1\", \"eos\":" + str(
        #     eos) + ", \"maybe_eos\": 600, \"max_eos\": 1000," + vpr_rec_string + f"\"encoding\": \"{encode}\",\"channels\": " + str(
        #     CHANNEL_COUNT) + ",\"bit_depth\": " + str(SAMPLE_SIZE * 8) + ",\"sample_rate\": " + str(
        #     SAMPLE_RATE) + "},\"nlu\":{\"vcn\": \"x2_xiaojuan\",\"pitch\": 60,\"speed\": 60,\"volume\": 50,\"tts_aue\": \"raw\"}}}"

        param_fullduplex = "{\"action\":\"start\",\"scene\":\"" + scene + "\", \"params\": {\"mid\": \"" + mid + "\",\"fullduplex\":{\"lat\": \"31.83\",\"lng\": \"117.14\",\"aue\": \"raw\",\"encoding\": \"" + encode + "\",\"eos\":300,\"vcn\": \"x2_xiaojuan\",\"pitch\": 60,\"speed\": 60,\"volume\": 50,\"tts_aue\": \"raw\",\"sample_rate\": " + str(
            SAMPLE_RATE) + ", \"channels\":" + str(CHANNEL_COUNT) + ",\"bit_depth\": " + str(SAMPLE_SIZE * 8) + "}}}"

        # param_text = ""

        param_tts = ""

        param_nluandtts = "{\"action\":\"start\",\"scene\":\"" + scene + "\", \"params\": {\"mid\": \"" + f"opus-{mid}" + "\",\"nlu\":{\"text\": \"我想听故事\",\"vcn\": \"x2_xiaojuan\",\"pitch\": 60,\"speed\": 60,\"volume\": 50,\"tts_aue\": \"raw\"}}}"
        param = ""
        with excel_lock:
            new_worksheet.write(self.row, self.cols + 3, f"{mid}")
            # new_workbook.save(new_excel_name)
        if scene == 'tts':

            param = param_tts.encode(encoding="utf-8")
        else:
            if data_type == 'text':
                param = param_nluandtts.encode(encoding="utf-8")
            if data_type == 'audio':
                if scene == 'fullduplex':
                    param = param_fullduplex.encode(encoding="utf-8")
                else:
                    param = param_iat.encode(encoding="utf-8")
        custom_print(str(param))
        self.send(param)

    def send_end_tag(self):
        end_tag_v2 = "{\"action\": \"end\"}"
        self.send(end_tag_v2.encode("utf-8"))
        custom_print("发送结束标识")

    def send_close_session(self):
        close_v2 = "{\"action\": \"close\"}"
        self.send(close_v2.encode("utf-8"))
        custom_print("发送断开连接标识")

    def received_message(self, message):
        # pylint: disable=global-variable-not-assigned, global-statement
        global iat_success
        global iat_fail
        global new_workbook
        global new_worksheet
        test_result = "FALSE"
        try:
            custom_print(str(message))
        except UnicodeEncodeError:
            pass
        s = json.loads(str(message))
        #         if (str(s).find("action")== -1):
        if str(s).find("action") == -1:
            if s['topic'] == "asr.speech.result":
                #                 custom_print("===============================返回结果=====================================")
                # is_finish: true的时间，表示识别结果已经完整结束
                self.timestamp_isfinish = time.time()
                requestId = s['requestId']
                with excel_lock:
                    new_worksheet.write(self.row, self.cols + 2, requestId)
                    new_worksheet.write(self.row, self.cols + 9, self.timestamp_isfinish)
                    new_worksheet.write(self.row, self.cols + 10,
                                        int(round((self.timestamp_isfinish - self.timestamp_end) * 1000)))
                    # new_workbook.save(new_excel_name)
                iat_text = s['text']
                self.real_text_list.append(iat_text)
                self.queue.put("asr.vad.end")
                # 判断识别结果
                real_text = getRealText(self.real_text_list)
                handle_real_text = handle_str(real_text)
                handle_except_text = handle_str(self.expected_text)

                if (handle_except_text.lower() == handle_real_text.lower()) or (
                        self.expected_text.lower() == real_text.lower()) or (
                        self.expected_text.lower() == handle_real_text.lower()) or (
                        handle_except_text.lower() == real_text.lower()):
                    test_result = "TRUE"
                    iat_success = iat_success + 1
                else:
                    iat_fail = iat_fail + 1

                with excel_lock:
                    if self.expected_text != "":
                        new_worksheet.write(self.row, self.cols, real_text)
                        new_worksheet.write(self.row, self.cols + 1, test_result)
                    # new_workbook.save(new_excel_name)
            #                 custom_print("===============================end=====================================")
            elif s['topic'] == "asr.vad.end":
                self.timestamp_end = time.time()
                with excel_lock:
                    new_worksheet.write(self.row, self.cols + 7, self.timestamp_end)
                    new_worksheet.write(self.row, self.cols + 8,
                                        int(round((self.timestamp_end - self.start_time) * 1000)))
                    # new_workbook.save(new_excel_name)
                    if self.end_point_ms is not None:
                        if not self.end_point == 0:
                            new_worksheet.write(self.row, self.cols + 12, int(self.timestamp_end - self.end_point))
                        else:
                            new_worksheet.write(self.row, self.cols + 12, int(self.timestamp_end - self.start_time - self.end_point_ms))
            elif s['topic'] == "dm.output":
                self.timestamp_nlp = time.time()
                print(f"dm.output:{self.timestamp_nlp}")
                with excel_lock:
                    new_worksheet.write(self.row, self.cols + 11,
                                        int(round((self.timestamp_nlp - self.timestamp_isfinish) * 1000)))
                    # new_workbook.save(new_excel_name)
                self.nlp_end_flag = True
                if enable_vpr_rec:
                    if self.vpr_end_flag:
                        self.send_end_tag()
                        self.send_close_session()
                else:
                    self.send_end_tag()
                    self.send_close_session()
            elif s['topic'] == "asrplus.voiceprint.result":
                self.vpr_end_flag = True
                if enable_vpr_rec:
                    if self.nlp_end_flag:
                        self.send_end_tag()
                        self.send_close_session()
            elif s['topic'] == "asr.speech.sentence":
                #                 custom_print("===============================返回结果=====================================")
                # is_finish: true的时间，表示识别结果已经完整结束
                self.timestamp_isfinish = time.time()

                print(f"asr.speech.sentence:{self.timestamp_isfinish}")
                self.queue.put("asr.vad.end")

                self.send_end_tag()
                self.send_close_session()
                requestId = s['requestId']
                with excel_lock:
                    new_worksheet.write(self.row, self.cols + 2, requestId)
                    new_worksheet.write(self.row, self.cols + 9, self.timestamp_isfinish)
                    new_worksheet.write(self.row, self.cols + 10,
                                        int(round((self.timestamp_isfinish - self.timestamp_end) * 1000)))
                    # new_workbook.save(new_excel_name)
                iat_text = s['sentence']
                self.real_text_list.append(iat_text)
                # 判断识别结果
                real_text = getRealText(self.real_text_list)
                handle_real_text = handle_str(real_text)
                handle_except_text = handle_str(self.expected_text)

                if (handle_except_text.lower() == handle_real_text.lower()) or (
                        self.expected_text.lower() == real_text.lower()) or (
                        self.expected_text.lower() == handle_real_text.lower()) or (
                        handle_except_text.lower() == real_text.lower()):
                    test_result = "TRUE"
                    iat_success = iat_success + 1
                else:
                    iat_fail = iat_fail + 1

                with excel_lock:
                    if self.expected_text != "":
                        new_worksheet.write(self.row, self.cols, real_text)
                        new_worksheet.write(self.row, self.cols + 1, test_result)
                    # new_workbook.save(new_excel_name)
        elif s['action'] == "connected":
            self.start_time = time.time()
            with excel_lock:
                new_worksheet.write(self.row, self.cols + 6, self.start_time)
                # new_workbook.save(new_excel_name)
            self.create_session()

        elif s['action'] == "started":
            custom_print("started:")

            print(f"started:{time.time()}")
            if scene == "tts":
                custom_print("1111")
                self.send(text_msg.encode("utf-8"), True)
            else:
                if data_type == "text":
                    custom_print(f"发送文本：{text_msg}")
                    # self.send(text_msg.encode("utf-8"), True)
                if data_type == "audio":
                    custom_print("ws start")
                    self.t1.start()

            # 数据发送结束之后发送结束标识
            # self.send_end_tag()

        elif s['action'] == "error":
            self.queue.put("asr.vad.end")
            custom_print(s)
            self.send_end_tag()
            self.send_close_session()

        elif s['action'] == "result":
            custom_print(s)
            data = s['data']
            if data['sub'] == "iat":
                custom_print(f"user: {str(data['text'])}")
            elif data['sub'] == "nlp":
                intent = data['intent']
                if str(intent) == "{}":
                    return
                if intent['rc'] == 0:
                    custom_print(f"server: {intent['answer']['text']}")
                else:
                    custom_print("我没有理解你说的话啊")
            elif data['sub'] == "tts":
                # TODO 播报pcm音频
                # byte_str = base64.b64decode(data['content'])
                # tts_bytes = byte_str.decode("utf-8")
                # custom_print("tts: " + tts_bytes)
                pass
        elif s['action'] == "finish":
            custom_print(s)
            # self.send_close_session()

        else:
            pass


if __name__ == '__main__':
    # if os.path.exists(f'{"./log"}/{starttime}.log'): os.remove(f'{"./log"}/{starttime}.log')
    main(test_data, ['Sheet2'], 1, False)
