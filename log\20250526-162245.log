1748247765
param:b'{\n            "auth_id": "a56c1f812db14bb195cad688e4f41e74",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"ciddab34e41@dxe39f1b8f00563eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "450ad02028d44b9ab1332f7988c60cf6","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b2576@dx1970bafe4adb8a9532"}
started:
ws start
####################
测试进行: ctm0001030e@hu17b4cb4fb36020c902#46275217.pcm
{"recordId":"ase000ee84f@hu1970baff2cf05c2882:450ad02028d44b9ab1332f7988c60cf6","requestId":"ase000ee84f@hu1970baff2cf05c2882","sessionId":"cid000b2576@dx1970bafe4adb8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1748247769}
{"recordId":"gty000b2577@dx1970bafe4feb8a9532:450ad02028d44b9ab1332f7988c60cf6","requestId":"gty000b2577@dx1970bafe4feb8a9532","sessionId":"cid000b2576@dx1970bafe4adb8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1748247770}
{"recordId":"gty000b2577@dx1970bafe4feb8a9532:450ad02028d44b9ab1332f7988c60cf6","requestId":"gty000b2577@dx1970bafe4feb8a9532","sessionId":"cid000b2576@dx1970bafe4adb8a9532","eof":"1","text":"电视怎么调台","pinyin":"","topic":"asr.speech.result","age":"elder","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1748247770}
send data finished:1748247770.950919
{"recordId":"gty000b2577@dx1970bafe4feb8a9532:450ad02028d44b9ab1332f7988c60cf6","requestId":"gty000b2577@dx1970bafe4feb8a9532","sessionId":"cid000b2576@dx1970bafe4adb8a9532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"电视怎么调台","intentId":"chat","intentName":"闲聊","nlg":"机器人也不是万能的噢，这个我也不知道。","shouldEndSession":true},"nlu":{"input":"电视怎么调台","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1748247771}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid9c5bf187@dxb5e91b8f005b3eef00"}
连接正常关闭
#####运行完成,点击查看结果按钮查看结果!##########
