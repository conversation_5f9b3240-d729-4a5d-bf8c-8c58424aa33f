import os


if __name__ == '__main__':
    audio_path = 'data/313sp_off_a2'
    pcm_files = []
    for root, dirs, files in os.walk(audio_path):
        for file in files:
            if file.endswith('.pcm'):
                pcm_files.append(os.path.join(audio_path, file))
    f = open("rename.sh", "wt")
    for audio_file in pcm_files:
        f.write(f"go run codec.go -input={audio_file} -output={audio_file.replace('pcm', 'opus')}\n")
    f.close()