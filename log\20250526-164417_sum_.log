Starting Midea test script in Go...
Thread No (from arg): 
Processing sheet: 313sp_off_a1
Sheet 313sp_off_a1: 1/505
Processing task: Sheet=313sp_off_a1, Audio=ctm0001030e@hu17b4cb4fb36020c902#46275217.pcm, Row=2, Expected=电视怎么调台
Connecting to: wss://beijing-midea.listenai.com/midea/proxy?appid=5e017b34&checksum=e8e05e0888d08bb53019546c5c45da8a&curtime=1748249057&param=eyJhdXRoX2lkIjoiMzM4ODNkYjctZGY0ZS00YjIxLWE0ZDktOTU3ZjYwNWQ0ZGEyIiwiZGF0YV90eXBlIjoiYXVkaW8ifQ==&signtype=md5
Received for ctm0001030e@hu17b4cb4fb36020c902#46275217.pcm: {"action":"connected","cid":"cid3ce476c1@dx903c1b8f05623eef00","code":"0","data":"","desc":"success"}
Error unmarshalling message for ctm0001030e@hu17b4cb4fb36020c902#46275217.pcm: json: cannot unmarshal string into Go struct field WebSocketReceivePayload.code of type int. Raw: {"action":"connected","cid":"cid3ce476c1@dx903c1b8f05623eef00","code":"0","data":"","desc":"success"}
WebSocket read error for ctm0001030e@hu17b4cb4fb36020c902#46275217.pcm: websocket: close 1006 (abnormal closure): unexpected EOF
Finished processing for audio: ctm0001030e@hu17b4cb4fb36020c902#46275217.pcm
Sheet 313sp_off_a1: 2/505
Processing task: Sheet=313sp_off_a1, Audio=ctm00010320@hu17b4cb503aa020c902#46275237.pcm, Row=3, Expected=杨幂是谁
Connecting to: wss://beijing-midea.listenai.com/midea/proxy?appid=5e017b34&checksum=db95d97921921fdeba1f90e9fa84db8f&curtime=1748249118&param=eyJhdXRoX2lkIjoiMzI2ZjMxZjUtNTc1My00YWViLTljMTQtNmRhM2MxOGNiODVkIiwiZGF0YV90eXBlIjoiYXVkaW8ifQ==&signtype=md5
Received for ctm00010320@hu17b4cb503aa020c902#46275237.pcm: {"action":"connected","cid":"cid639f6441@dxe7da1b8f059e3eef00","code":"0","data":"","desc":"success"}
Error unmarshalling message for ctm00010320@hu17b4cb503aa020c902#46275237.pcm: json: cannot unmarshal string into Go struct field WebSocketReceivePayload.code of type int. Raw: {"action":"connected","cid":"cid639f6441@dxe7da1b8f059e3eef00","code":"0","data":"","desc":"success"}
