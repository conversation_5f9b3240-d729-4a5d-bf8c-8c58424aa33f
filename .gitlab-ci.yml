variables:
  GIT_SUBMODULE_STRATEGY: recursive

stages:
  - build
  - test

build:
  stage: build
  script:
    - cd asr_common
    - git checkout master
    - cd ..
    - docker build -t mideatest:$CI_COMMIT_REF_NAME-${CI_COMMIT_SHA:0:8} .
  only:
    - master
  tags:
    - iflyos-builder-2

pylint:
  stage: test
  script:
    - docker run mideatest:$CI_COMMIT_REF_NAME-${CI_COMMIT_SHA:0:8} -m pylint --rcfile=.pylintrc  --output-format=parseable --disable=R -rn .
  only:
    - master
  tags:
    - iflyos-builder-2
