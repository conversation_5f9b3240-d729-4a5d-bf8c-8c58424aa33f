1751442783
param:b'{\n            "auth_id": "b41d7ad581374f5686346825144e53a7",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid596a080d@dxfb4f1bc031603eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"mqasr", "params": {"mid": "8328c33a93bf499aa6aefbb1bb31a1d3","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b126f@dx197ca200038b8aa532"}
started:
ws start
####################
测试进行: ctm000d19d6@hu1934797d27204e0902#117600936.wav
{"recordId":"ase000e4cd1@hu197ca2007b905c3882:8328c33a93bf499aa6aefbb1bb31a1d3","requestId":"ase000e4cd1@hu197ca2007b905c3882","sessionId":"cid000b126f@dx197ca200038b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1751442787}
{"recordId":"gty000b1271@dx197ca2001bdb8aa532:8328c33a93bf499aa6aefbb1bb31a1d3","requestId":"gty000b1271@dx197ca2001bdb8aa532","sessionId":"cid000b126f@dx197ca200038b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1751442787}
{"recordId":"gty000b1271@dx197ca2001bdb8aa532:8328c33a93bf499aa6aefbb1bb31a1d3","requestId":"gty000b1271@dx197ca2001bdb8aa532","sessionId":"cid000b126f@dx197ca200038b8aa532","eof":"1","text":"音量10%","oneshot_asr":"${oneshot_asr}","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1751442787}
send data finished:1751442787.699562
{"recordId":"gty000b1271@dx197ca2001bdb8aa532:8328c33a93bf499aa6aefbb1bb31a1d3","requestId":"gty000b1271@dx197ca2001bdb8aa532","sessionId":"cid000b126f@dx197ca200038b8aa532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"音量10%","intentId":"INSTRUCTION","intentName":"音乐控制","nlg":"","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"音量10%","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"操作","value":"volume_select"},{"name":"百分比","value":"10"}]}}},"timestamp":1751442787}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidec7901ed@dxbc961bc031873eef00"}
连接正常关闭
#####运行完成,点击查看结果按钮查看结果!##########
