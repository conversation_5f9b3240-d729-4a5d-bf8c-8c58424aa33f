#!bin/python
#coding=gbk
import os
import re
import sys

outputdir = '../../output'

def getFileImfoemation(filepath,filenamekey,csvfile,csv_all_file):
    sumH=0
    sumN=0
    wordSumH=0
    wordSumI=0
    wordSumN=0
    for filename in os.listdir(filepath):
        if filenamekey in filename:
            list_information = []
            fname = filename
            list_information.append(fname)
            f1 = open(os.path.join(filepath,filename),"r", encoding='GBK', errors='ignore')
            sentence = ""
            word = ""
            corr = ''
            sentenceH = ""
            sentenceN = ""
            for eachline in f1.readlines():
                if re.search("SENT: ",eachline):#get file sentence information
                    sentence = eachline.split("%Correct=")[-1].split(" ")[0].strip()
                    list_information.append(sentence)
                    sentenceH = eachline.split("H=")[-1].split(",")[0].strip()
                    list_information.append(sentenceH)
                    sumH=sumH+int(sentenceH)
                    #print sumH
                    sentenceN = eachline.split("N=")[-1].split("]")[0].strip()
                    list_information.append(sentenceN)
                    sumN=sumN+int(sentenceN)
                    #print sumN
                if re.search("WORD: ",eachline):#get file word information
                    word = eachline.split("Acc=")[-1].split(" ")[0].strip()
                    list_information.append(word)
                    corr = eachline.split("%Corr=")[-1].split(",")[0].strip()
                    list_information.append(corr)
                    wordH = eachline.split("H=")[-1].split(",")[0].strip()
                    list_information.append(wordH)
                    wordSumH=wordSumH+int(wordH)
                    wordD = eachline.split("D=")[-1].split(",")[0].strip()
                    list_information.append(wordD)
                    wordS = eachline.split("S=")[-1].split(",")[0].strip()
                    list_information.append(wordS)
                    wordI = eachline.split("I=")[-1].split(",")[0].strip()
                    list_information.append(wordI)
                    wordSumI=wordSumI+int(wordI)
                    wordN = eachline.split("N=")[-1].split("]")[0].strip()
                    list_information.append(wordN)
                    wordSumN=wordSumN+int(wordN)
            for eachlist in list_information:
                csvfile.write("%s,"%eachlist)
                csv_all_file.write("%s,"%eachlist)
            csvfile.write("\n")
            csv_all_file.write("\n")
            print ('%s import over'%fname)
        else:
            pass
    csvfile.write("avg,")
    avg_sent = sumH/float(sumN) if sumN > 0 else 0.00
    avg_word = (wordSumH-wordSumI)/float(wordSumN) if wordSumN > 0 else 0.00
    avg_corr = (wordSumH)/float(wordSumN) if wordSumN > 0 else 0.00
    csvfile.write(str(avg_sent)+',')
    csvfile.write(" ,")
    csvfile.write(" ,")
    csvfile.write(str(avg_word)+",")
    csvfile.write(str(avg_corr)+",")
    csvfile.write("\n")
    csv_all_file.write("avg,")
    #csvfile.write(" ,")
    csv_all_file.write(str(avg_sent)+',')
    csv_all_file.write(" ,")
    csv_all_file.write(" ,")
    csv_all_file.write(str(avg_word)+",")
    csv_all_file.write(str(avg_corr)+",")
    csv_all_file.write("\n\n")    

def getSmoothFileImfoemation(filepath,filenamekey,csvfile,csv_all_file):
    sumPgs_word=0
    sumPgs_count=0
    sumpgs_rate=0.00
    for filename in os.listdir(filepath):
        if filenamekey in filename:
            list_information = []
            fname = filename
            list_information.append(fname)
            f1 = open(os.path.join(filepath,filename),"r", encoding='GBK', errors='ignore')
            Pgs_word = ''
            Pgs_count = ''
            Pgs_rate = ''
            for eachline in f1.readlines():
                if re.search("testset_pgs_word:",eachline):
                    Pgs_word = eachline.split("testset_pgs_word:")[-1].strip()
                    list_information.append(Pgs_word)
                    sumPgs_word += int(Pgs_word)
                if re.search("testset_pgs_count:",eachline):
                    Pgs_count = eachline.split("testset_pgs_count:")[-1].strip()
                    list_information.append(Pgs_count)
                    sumPgs_count += int(Pgs_count)
                if re.search("testset_pgs_rate:",eachline):
                    Pgs_rate = eachline.split("testset_pgs_rate:")[-1].strip()
                    list_information.append(Pgs_rate)
            for eachlist in list_information:
                csvfile.write("%s,"%eachlist)
                csv_all_file.write("%s,"%eachlist)
            csvfile.write("\n")
            csv_all_file.write("\n")
            print ('%s import over'%fname)
        else:
            pass
    csvfile.write("avg,")
    avg_rate = (sumPgs_word)/float(sumPgs_count) if sumPgs_count > 0 else 0.00
    csvfile.write(" ,")
    csvfile.write(" ,")
    csvfile.write(str(avg_rate)+",")
    csvfile.write("\n")
    csv_all_file.write("avg,")
    csv_all_file.write(" ,")
    csv_all_file.write(" ,")
    csv_all_file.write(str(avg_rate)+",")
    csv_all_file.write("\n\n")    
    
    
def CsvGetFromStaEachTask(taskstr,filenamekey):
    tasklist = taskstr.split(',')
    #csv_all_file =  os.path.join(outputdir,'all_result.csv')
    csv_all_file = open(os.path.join(outputdir,'all_PGS_result.csv'), 'w', encoding='GBK', errors='ignore')
    csv_all_file.write("filename,")
    csv_all_file.write("sent,")
    csv_all_file.write("H,")
    csv_all_file.write("sent_num,")
    csv_all_file.write("word,")
    csv_all_file.write("corr,")
    
    csv_all_file.write("H,")
    csv_all_file.write("D,")
    csv_all_file.write("S,")
    csv_all_file.write("I,")
    csv_all_file.write("N")
    csv_all_file.write("\n")    
    for each in tasklist:
        outcsvname = "PGS_" + each + '.csv'
        testset_dir = os.path.join(outputdir,each)  
        csvfile=open(os.path.join(testset_dir,outcsvname), 'w', encoding='GBK', errors='ignore')
        csvfile.write("filename,")
        csvfile.write("sent,")
        csvfile.write("H,")
        csvfile.write("sent_num,")
        csvfile.write("word,")
        csvfile.write("corr,")
        csvfile.write("H,")
        csvfile.write("D,")
        csvfile.write("S,")
        csvfile.write("I,")
        csvfile.write("N")
        csvfile.write("\n")
        getFileImfoemation(testset_dir,filenamekey,csvfile,csv_all_file)
        print ('csving in %s'%testset_dir)
        csvfile.close()    

def CsvStaSmooth(taskstr,filenamekey):
    tasklist = taskstr.split(',')
    #csv_all_file =  os.path.join(outputdir,'all_result.csv')
    csv_all_file = open(os.path.join(outputdir,'all_PGS_smooth_result.csv'), 'w', encoding='GBK', errors='ignore')
    csv_all_file.write("filename,")
    csv_all_file.write("pgs_word,")
    csv_all_file.write("pgs_count,")
    csv_all_file.write("pgs_rate,")
    csv_all_file.write("\n")    
    for each in tasklist:
        outcsvname = "PGS_smooth_" + each + '.csv'
        testset_dir = os.path.join(outputdir,each)  
        csvfile=open(os.path.join(testset_dir,outcsvname), 'w', encoding='GBK', errors='ignore')
        csvfile.write("filename,")
        csvfile.write("pgs_word,")
        csvfile.write("pgs_count,")
        csvfile.write("pgs_rate,")
        csvfile.write("\n")
        getSmoothFileImfoemation(testset_dir,filenamekey,csvfile,csv_all_file)
        print ('csving in %s'%testset_dir)
        csvfile.close()    
    

