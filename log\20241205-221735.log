1733408255
param:b'{\n            "auth_id": "d10220731f074d478e004170d1a65925",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid32e7cd3c@dx75a51aad02013eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "f7f366be7cf84a6c83b10b67b1ac1219","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "opus-wb","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000dc8e6@dx193972ef4d57824532"}
started:
ws start
####################
测试进行: iflyAudioRaw987077508154796300_2.tmp
{"recordId":"ase000ece58@hu193972f02a81323882:f7f366be7cf84a6c83b10b67b1ac1219","requestId":"ase000ece58@hu193972f02a81323882","sessionId":"cid000dc8e6@dx193972ef4d57824532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1733408260}
{"recordId":"wgw000dc8e7@dx193972ef6d07824532:f7f366be7cf84a6c83b10b67b1ac1219","requestId":"wgw000dc8e7@dx193972ef6d07824532","sessionId":"cid000dc8e6@dx193972ef4d57824532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1733408267}
{"recordId":"wgw000dc8e7@dx193972ef6d07824532:f7f366be7cf84a6c83b10b67b1ac1219","requestId":"wgw000dc8e7@dx193972ef6d07824532","sessionId":"cid000dc8e6@dx193972ef4d57824532","eof":"1","text":"123456789 10 11 12 13 14 15","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1733408267}
send data finished:1733408266764.878
{"recordId":"wgw000dc8e7@dx193972ef6d07824532:f7f366be7cf84a6c83b10b67b1ac1219","requestId":"wgw000dc8e7@dx193972ef6d07824532","sessionId":"cid000dc8e6@dx193972ef4d57824532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"123456789 10 11 12 13 14 15","intentId":"chat","intentName":"闲聊","nlg":"你这是为难我胖虎！我拒绝。","shouldEndSession":true},"nlu":{"input":"123456789 10 11 12 13 14 15","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1733408267}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid35419958@dx9b621aad020b3eef00"}
连接正常关闭
#####运行完成,点击查看结果按钮查看结果!##########
