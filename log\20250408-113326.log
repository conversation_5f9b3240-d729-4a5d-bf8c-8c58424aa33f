1744083206
param:b'{\n            "auth_id": "55194b5c061646cb8f3519ea427898e1",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid6d1948eb@dx07371b4f74873eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "5358f7856b234905bdf77ff5828beb88","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be61b@dx1961375c4ab7844532"}
started:
ws start
####################
测试进行: 0_baibu_wenben_0.wav
ts:1744083208.8426988, sec_count:1, send silence begin
{"recordId":"gty000be61c@dx1961375c5067844532:5358f7856b234905bdf77ff5828beb88","requestId":"gty000be61c@dx1961375c5067844532","sessionId":"cid000be61b@dx1961375c4ab7844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1744083209}
{"recordId":"gty000be61c@dx1961375c5067844532:5358f7856b234905bdf77ff5828beb88","requestId":"gty000be61c@dx1961375c5067844532","sessionId":"cid000be61b@dx1961375c4ab7844532","eof":"1","text":"开机","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1744083209}
send data finished:1744083209.163658
{"recordId":"gty000be61c@dx1961375c5067844532:5358f7856b234905bdf77ff5828beb88","requestId":"gty000be61c@dx1961375c5067844532","sessionId":"cid000be61b@dx1961375c4ab7844532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"开机","intentId":"chat","intentName":"闲聊","nlg":"那这之后呢，再给我多说点吧。","shouldEndSession":true},"nlu":{"input":"开机","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1744083209}
{"recordId":"ase000ef89e@hu1961375cbd605c3882:5358f7856b234905bdf77ff5828beb88","requestId":"ase000ef89e@hu1961375cbd605c3882","sessionId":"cid000be61b@dx1961375c4ab7844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1744083209}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid13f343ba@dx64bb1b4f74893eef00"}
连接正常关闭
#####运行完成,点击查看结果按钮查看结果!##########
