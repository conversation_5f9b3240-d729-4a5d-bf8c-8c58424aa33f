import os
import re
from datetime import datetime
from threading import Lock

CN_NUM_D = {'十': 10, '百': 100, "千": 1000, '万': 10000, '亿': 100000000}
CN_NUM = {'一': '1', '二': '2', '三': '3', '四': '4', '五': '5', '六': '6', '七': '7', '八': '8', '九': '9', '零': '0',
          '廿': '2', '两': '2'}

start_date = datetime.now().strftime("%Y%m%d")
start_time = datetime.now().strftime("%Y%m%d-%H%M%S")
log_lock = Lock()


def custom_print(content):
    # 打印到控制台
    print(content)
    # 打印到文件
    log_dir = './log'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    with log_lock:
        with open(f'{log_dir}/new_protocol_{start_time}.log', 'a', encoding='utf-8') as f:
            print(content, file=f)





# 转小写数字的处理
def handle_str(result):
    # 先将中文数字转换为阿拉伯数字


    # 处理百分之几
    # 新增对"百分之X"表达的处理，支持"百分之十"转"10%"
    m = re.search('百分之([一二三四五六七八九十0-9]+)', handle_result, re.IGNORECASE)
    if m is not None:
        num_str = m.group(1)
        # 将中文数字转为阿拉伯数字
        num_map = {'一':'1','二':'2','三':'3','四':'4','五':'5','六':'6','七':'7','八':'8','九':'9','十':'10','零':'0'}
        if num_str in num_map:
            num = num_map[num_str]
        else:
            num = num_str
        old = '百分之' + num_str
        new = num + '%'
        handle_result = handle_result.replace(old, new)

    denominator = '%'
    m = re.search('100分之(\d+)', handle_result, re.IGNORECASE)
    if m is not None:
        numerator = m.group(1)
        old = '100分之' + numerator
        new = numerator + denominator
        handle_result = handle_result.replace(old, new)

    # 处理几分之几
    m = re.search('(\d+)分之', handle_result, re.IGNORECASE)
    if m is not None:
        denominator = m.group(1)
    m = re.search('分之(\d+)', handle_result, re.IGNORECASE)
    if m is not None:
        numerator = m.group(1)
        old = denominator + '分之' + numerator
        new = numerator + '/' + denominator
        handle_result = handle_result.replace(old, new)

    return handle_result


def updateIATPGS(cntJson, result_level="complete"):
    if result_level == "plain":
        data = cntJson.get("data")
        if data is None: return
        text = data.get("text")
        is_finish = data.get("is_last")
        if text is None: return
        return text, is_finish
    else:
        mIATPGSStack = [""] * 50
        data = cntJson.get("data")
        if data is None: return
        text = data.get("text")
        is_finish = data.get("is_finish")
        if text is None: return
        iatText = ""
        words = text.get("ws", [])
        lastResult = text.get("ls", False)
        for word in words:
            charWord = word.get("cw", [])
            for char in charWord:
                iatText += char.get("w", "")
        voiceIAT = ""
        pgsMode = text.get("pgs", "")
        if pgsMode != "":
            serialNumber = text.get("sn", 0)
            mIATPGSStack[serialNumber] = iatText
            if pgsMode == "rpl":
                replaceRange = text.get("rg", [0, 0])
                start, end = replaceRange[0], replaceRange[1]
                for index in range(start, end + 1):
                    mIATPGSStack[index] = ""
            PGSResult = ""
            for index in range(len(mIATPGSStack)):
                if mIATPGSStack[index] == "": continue
                PGSResult += mIATPGSStack[index]
                if lastResult:
                    mIATPGSStack[index] = ""
            voiceIAT = PGSResult
        return voiceIAT, is_finish


def getRealText(real_text_list):
    if (not real_text_list or len(real_text_list) <= 0): return ""
    if (len(real_text_list) == 1): return real_text_list[0]
    real_text = real_text_list[-1]
    if real_text == "" or real_text == [''] or isinstance(real_text, bool):
        return getRealText(real_text_list[:-1])
    else:
        return real_text


if __name__ == '__main__':
    pass
