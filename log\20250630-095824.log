1751248705
param:b'{\n            "auth_id": "6d20eb4dc60848dfab489e93e1f89e57",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid450b2ddf@dx126b1bbd3b413eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"mqasr", "params": {"mid": "15854803f57f4b2fa6f4066eaf9fd1bc","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bd88a@dx197be8e98fdb86a532"}
started:
ws start
####################
测试进行: F004_normal_01.pcm
{"recordId":"ase000fbd3a@hu197be8ea18605bf882:15854803f57f4b2fa6f4066eaf9fd1bc","requestId":"ase000fbd3a@hu197be8ea18605bf882","sessionId":"cid000bd88a@dx197be8e98fdb86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1751248708}
ts:1751248708.2360022, sec_count:1, send silence begin
{"recordId":"gty000bd88b@dx197be8e993cb86a532:15854803f57f4b2fa6f4066eaf9fd1bc","requestId":"gty000bd88b@dx197be8e993cb86a532","sessionId":"cid000bd88a@dx197be8e98fdb86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1751248708}
{"recordId":"gty000bd88b@dx197be8e993cb86a532:15854803f57f4b2fa6f4066eaf9fd1bc","requestId":"gty000bd88b@dx197be8e993cb86a532","sessionId":"cid000bd88a@dx197be8e98fdb86a532","eof":"1","text":"空调打开送风","oneshot_asr":"${oneshot_asr}","pinyin":"","topic":"asr.speech.result","age":"child","ageScore":"","gender":"","genderScore":"","language_class":"mandarin","timestamp":1751248708}
send data finished:1751248708.712466
{"recordId":"gty000bd88b@dx197be8e993cb86a532:15854803f57f4b2fa6f4066eaf9fd1bc","requestId":"gty000bd88b@dx197be8e993cb86a532","sessionId":"cid000bd88a@dx197be8e98fdb86a532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"空调打开送风","intentId":"chat","intentName":"闲聊","nlg":"等我学习一下，再来回答吧！现在不如问点其他的吧。","shouldEndSession":true},"nlu":{"input":"空调打开送风","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1751248708}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid8cee7ebe@dx177a1bbd3b443eef00"}
连接正常关闭
#####运行完成,点击查看结果按钮查看结果!##########
