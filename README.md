# 美的云端协议测试脚本


## 美的云端识别批量测试脚本说明
```
# 线上环境
# base_url = "wss://wsapi.xfyun.cn/midea/proxy"
# trans_url = "https://itrans.xfyun.cn/v2/its"
# app_id = "5e017b34"
# api_key = "eed5bf68c7e01d85b3713d90bde3154a"
# api_secret = "0e3724a6013368a53ba21c34293705d4"

# 灰度环境
base_url = "wss://aiui-test.openspeech.cn/midea/proxy"
trans_url = "https://itrans.xfyun.cn/v2/its"
app_id = "5e017b34"
api_key = "eed5bf68c7e01d85b3713d90bde3154a"
api_secret = "0e3724a6013368a53ba21c34293705d4"
```
环境配置


`audio_path`

音频文件路径

`test_data`

音频文件列表与预期识别结果。必需使用xls文件，不支持xlsx

`result_level`

协议返回格式，plain为简易协议，complete为完整协议

```
    main(test_data, ['Sheet1'], 0, False)
```
运行main时第二个参数填写excel页面名称。第三个参数填运行的行数，填写0全部运行。第四个参数决定是否并行运行。

此脚本依赖asr_common，需要git clone --recurse-submodules拉取代码


