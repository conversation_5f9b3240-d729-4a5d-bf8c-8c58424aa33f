1741597302
param:b'{\n            "auth_id": "6877229f06f7445f8eb886a7b9fba9f0",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid2b57818d@dx13301b29f6763eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "d1739175549a4b0b96185209404fb679","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb6cc@dx1957f49df58b8aa532"}
started:
ws start
####################
测试进行: ctm0001030e@hu17b4cb4fb36020c902#46275217.pcm
ts:1741597312.7688525, sec_count:1, send silence begin
ts:1741597312.7693691, slice_num:250
ts:1741597312.8094518, slice_num:251
ts:1741597312.8499126, slice_num:252
ts:1741597312.8889108, slice_num:253
ts:1741597312.9279058, slice_num:254
ts:1741597312.966943, slice_num:255
ts:1741597313.0064013, slice_num:256
ts:1741597313.0465162, slice_num:257
ts:1741597313.0858684, slice_num:258
ts:1741597313.1248462, slice_num:259
ts:1741597313.1639414, slice_num:260
ts:1741597313.2032778, slice_num:261
ts:1741597313.2432938, slice_num:262
ts:1741597313.283579, slice_num:263
ts:1741597313.3228173, slice_num:264
ts:1741597313.3622339, slice_num:265
ts:1741597313.401735, slice_num:266
ts:1741597313.4411714, slice_num:267
ts:1741597313.4816341, slice_num:268
ts:1741597313.5208538, slice_num:269
ts:1741597313.5594738, slice_num:270
ts:1741597313.5990865, slice_num:271
ts:1741597313.63887, slice_num:272
ts:1741597313.6783333, slice_num:273
ts:1741597313.7178447, slice_num:274
sec_count:2
ts:1741597313.7576017, slice_num:276
ts:1741597313.7967207, slice_num:277
ts:1741597313.8357184, slice_num:278
ts:1741597313.8746748, slice_num:279
ts:1741597313.9137304, slice_num:280
ts:1741597313.9531095, slice_num:281
ts:1741597313.9935637, slice_num:282
ts:1741597314.032939, slice_num:283
ts:1741597314.0720205, slice_num:284
ts:1741597314.112201, slice_num:285
ts:1741597314.1521354, slice_num:286
ts:1741597314.1922858, slice_num:287
ts:1741597314.2327256, slice_num:288
ts:1741597314.2717571, slice_num:289
ts:1741597314.3107445, slice_num:290
ts:1741597314.3497171, slice_num:291
ts:1741597314.388614, slice_num:292
ts:1741597314.4275835, slice_num:293
ts:1741597314.46744, slice_num:294
ts:1741597314.507301, slice_num:295
ts:1741597314.5473075, slice_num:296
ts:1741597314.5872269, slice_num:297
ts:1741597314.627232, slice_num:298
ts:1741597314.6667461, slice_num:299
ts:1741597314.706177, slice_num:300
sec_count:3
ts:1741597314.7459216, slice_num:302
ts:1741597314.785383, slice_num:303
ts:1741597314.8257248, slice_num:304
ts:1741597314.8647468, slice_num:305
ts:1741597314.9036348, slice_num:306
ts:1741597314.9428856, slice_num:307
ts:1741597314.9823909, slice_num:308
ts:1741597315.0227487, slice_num:309
ts:1741597315.0620673, slice_num:310
ts:1741597315.101982, slice_num:311
ts:1741597315.1412594, slice_num:312
ts:1741597315.181437, slice_num:313
ts:1741597315.2216828, slice_num:314
ts:1741597315.2607071, slice_num:315
ts:1741597315.2998965, slice_num:316
ts:1741597315.339323, slice_num:317
ts:1741597315.3792367, slice_num:318
ts:1741597315.41948, slice_num:319
ts:1741597315.4597678, slice_num:320
ts:1741597315.4996922, slice_num:321
ts:1741597315.539007, slice_num:322
ts:1741597315.5788546, slice_num:323
ts:1741597315.6182597, slice_num:324
ts:1741597315.6585155, slice_num:325
ts:1741597315.6976404, slice_num:326
sec_count:4
ts:1741597315.7376077, slice_num:328
ts:1741597315.7768013, slice_num:329
ts:1741597315.816228, slice_num:330
ts:1741597315.8562853, slice_num:331
ts:1741597315.8957896, slice_num:332
ts:1741597315.935096, slice_num:333
ts:1741597315.9753761, slice_num:334
ts:1741597316.0155413, slice_num:335
ts:1741597316.0545082, slice_num:336
ts:1741597316.0937681, slice_num:337
ts:1741597316.132872, slice_num:338
ts:1741597316.1723545, slice_num:339
ts:1741597316.2124913, slice_num:340
ts:1741597316.2518535, slice_num:341
ts:1741597316.2912407, slice_num:342
ts:1741597316.3306582, slice_num:343
ts:1741597316.3699605, slice_num:344
ts:1741597316.4094367, slice_num:345
ts:1741597316.449423, slice_num:346
ts:1741597316.4896436, slice_num:347
ts:1741597316.5295312, slice_num:348
ts:1741597316.5689723, slice_num:349
ts:1741597316.6084557, slice_num:350
ts:1741597316.6483996, slice_num:351
ts:1741597316.6885464, slice_num:352
sec_count:5
ts:1741597316.7283647, slice_num:354
ts:1741597316.7684038, slice_num:355
ts:1741597316.8084824, slice_num:356
ts:1741597316.8485813, slice_num:357
ts:1741597316.8875468, slice_num:358
ts:1741597316.926503, slice_num:359
ts:1741597316.9656434, slice_num:360
ts:1741597317.0046957, slice_num:361
ts:1741597317.0434716, slice_num:362
ts:1741597317.0835257, slice_num:363
ts:1741597317.1228557, slice_num:364
ts:1741597317.16192, slice_num:365
ts:1741597317.2008858, slice_num:366
ts:1741597317.2406204, slice_num:367
ts:1741597317.2800179, slice_num:368
ts:1741597317.3199959, slice_num:369
ts:1741597317.3593245, slice_num:370
ts:1741597317.3996902, slice_num:371
ts:1741597317.4386847, slice_num:372
ts:1741597317.4776921, slice_num:373
ts:1741597317.5168436, slice_num:374
ts:1741597317.5561087, slice_num:375
ts:1741597317.5962346, slice_num:376
ts:1741597317.636762, slice_num:377
ts:1741597317.6759129, slice_num:378
sec_count:6
ts:1741597317.7163436, slice_num:380
ts:1741597317.756651, slice_num:381
{"recordId":"gty000bb6cd@dx1957f49dfa9b8aa532:d1739175549a4b0b96185209404fb679","requestId":"gty000bb6cd@dx1957f49dfa9b8aa532","sessionId":"cid000bb6cc@dx1957f49df58b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597317}
ts:1741597317.795802, slice_num:382
ts:1741597317.8355026, slice_num:383
{"action":"error","code":"10907","data":"","desc":"10037;code=10037","sid":"ase000eed83@hu1957f49df3d05c2882"}
{'action': 'error', 'code': '10907', 'data': '', 'desc': '10037;code=10037', 'sid': 'ase000eed83@hu1957f49df3d05c2882'}
发送结束标识
发送断开连接标识
{"recordId":"gty000bb6cd@dx1957f49dfa9b8aa532:d1739175549a4b0b96185209404fb679","requestId":"gty000bb6cd@dx1957f49dfa9b8aa532","sessionId":"cid000bb6cc@dx1957f49df58b8aa532","eof":"1","text":"","pinyin":"","topic":"asr.speech.result","language_class":"mandarin","timestamp":1741597317}
{"recordId":"gty000bb6cd@dx1957f49dfa9b8aa532:d1739175549a4b0b96185209404fb679","requestId":"gty000bb6cd@dx1957f49dfa9b8aa532","sessionId":"cid000bb6cc@dx1957f49df58b8aa532","topic":"dm.output","skill":"","skillId":"","speakUrl":"","error":{"errId":"010305","errMsg":"asr result is null"},"dm":{},"nlu":{"input":"","skill":"","skillId":""},"timestamp":1741597317}
ts:1741597317.875145, slice_num:384
send data finished:1741597317.8761516
{"action":"closed","code":"0","data":"","desc":"success","sid":"cide53fc62d@dx52591b29f6853eef00"}
连接正常关闭
1741597317
param:b'{\n            "auth_id": "94c0122d51614de3bb3589dcd492c77f",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidfeebcb94@dxa7aa1b29f6863eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "6eb3dc9ea779463e9c7b5b4cb407353f","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000baf4e@dx1957f4a1c1cb8a9532"}
started:
ws start
####################
测试进行: ctm00010320@hu17b4cb503aa020c902#46275237.pcm
{"recordId":"ase000fa7eb@hu1957f4a2cf705c4882:6eb3dc9ea779463e9c7b5b4cb407353f","requestId":"ase000fa7eb@hu1957f4a2cf705c4882","sessionId":"cid000baf4e@dx1957f4a1c1cb8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597322}
{"recordId":"gty000baf4f@dx1957f4a1c56b8a9532:6eb3dc9ea779463e9c7b5b4cb407353f","requestId":"gty000baf4f@dx1957f4a1c56b8a9532","sessionId":"cid000baf4e@dx1957f4a1c1cb8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597322}
{"recordId":"gty000baf4f@dx1957f4a1c56b8a9532:6eb3dc9ea779463e9c7b5b4cb407353f","requestId":"gty000baf4f@dx1957f4a1c56b8a9532","sessionId":"cid000baf4e@dx1957f4a1c1cb8a9532","eof":"1","text":"杨幂是谁","pinyin":"","topic":"asr.speech.result","age":"elder","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597322}
send data finished:1741597323.0530586
{"recordId":"gty000baf4f@dx1957f4a1c56b8a9532:6eb3dc9ea779463e9c7b5b4cb407353f","requestId":"gty000baf4f@dx1957f4a1c56b8a9532","sessionId":"cid000baf4e@dx1957f4a1c1cb8a9532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"杨幂是谁","intentId":"chat","intentName":"闲聊","nlg":"杨幂，1986年9月12日出生于北京市，演员、歌手、制片人，代表作有《神雕侠侣》、《亲爱的翻译官》、《三生三世十里桃花》等。","shouldEndSession":true},"nlu":{"input":"杨幂是谁","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741597323}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid9e458cfa@dxafa31b29f68b3eef00"}
连接正常关闭
1741597323
param:b'{\n            "auth_id": "15d7ee1b386849d08cc09689902f3df9",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cide9ecf5cc@dx9caf1b29f68b3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "183cc5c662d043ae90250047e91169c2","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb6df@dx1957f4a3137b8aa532"}
started:
ws start
####################
测试进行: ctm00010329@hu17b4cb507a7020c902#46275249.pcm
{"action":"error","code":"10907","data":"","desc":"10037;code=10037","sid":"ase000e4b31@hu1957f4a311c05c3882"}
{'action': 'error', 'code': '10907', 'data': '', 'desc': '10037;code=10037', 'sid': 'ase000e4b31@hu1957f4a311c05c3882'}
发送结束标识
发送断开连接标识
send data finished:1741597324.3334033
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid2ef95650@dx606d1b29f68c3eef00"}
连接正常关闭
1741597324
param:b'{\n            "auth_id": "32c66eaa947444e6b4f0fea6f2e28659",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid69235f16@dxc54e1b29f68c3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "68d8b98fbfa94bc98f0c304a994e9a27","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000baf5a@dx1957f4a3580b8a9532"}
started:
ws start
####################
测试进行: ctm00010424@hu17b59a7c006020c902#46466943.pcm
{"recordId":"ase000d8f6c@hu1957f4a462404d3882:68d8b98fbfa94bc98f0c304a994e9a27","requestId":"ase000d8f6c@hu1957f4a462404d3882","sessionId":"cid000baf5a@dx1957f4a3580b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597329}
{"recordId":"gty000baf5b@dx1957f4a35c9b8a9532:68d8b98fbfa94bc98f0c304a994e9a27","requestId":"gty000baf5b@dx1957f4a35c9b8a9532","sessionId":"cid000baf5a@dx1957f4a3580b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597330}
{"recordId":"gty000baf5b@dx1957f4a35c9b8a9532:68d8b98fbfa94bc98f0c304a994e9a27","requestId":"gty000baf5b@dx1957f4a35c9b8a9532","sessionId":"cid000baf5a@dx1957f4a3580b8a9532","eof":"1","text":"我要听易烊千玺的歌","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741597330}
send data finished:1741597330.5082304
{"recordId":"gty000baf5b@dx1957f4a35c9b8a9532:68d8b98fbfa94bc98f0c304a994e9a27","requestId":"gty000baf5b@dx1957f4a35c9b8a9532","sessionId":"cid000baf5a@dx1957f4a3580b8a9532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"我要听易烊千玺的歌","intentId":"PLAY","intentName":"点歌","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"我要听易烊千玺的歌","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"歌手名","value":"易烊千玺"}]}}},"timestamp":1741597330}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid13f0ee27@dx7a0e1b29f6923eef00"}
连接正常关闭
1741597330
param:b'{\n            "auth_id": "8c8344b092824b40899342656e3e8348",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid69c1694f@dx7a111b29f6923eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "1253580ff3654b3fae1c533dc0d19bb0","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000baf5f@dx1957f4a4dcfb8a9532"}
started:
ws start
####################
测试进行: ctm00010435@hu17b59a7c588020c902#46466960.pcm
{"recordId":"ase000e7bc9@hu1957f4a5ece1323882:1253580ff3654b3fae1c533dc0d19bb0","requestId":"ase000e7bc9@hu1957f4a5ece1323882","sessionId":"cid000baf5f@dx1957f4a4dcfb8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597335}
{"recordId":"gty000baf60@dx1957f4a4e0eb8a9532:1253580ff3654b3fae1c533dc0d19bb0","requestId":"gty000baf60@dx1957f4a4e0eb8a9532","sessionId":"cid000baf5f@dx1957f4a4dcfb8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597335}
{"recordId":"gty000baf60@dx1957f4a4e0eb8a9532:1253580ff3654b3fae1c533dc0d19bb0","requestId":"gty000baf60@dx1957f4a4e0eb8a9532","sessionId":"cid000baf5f@dx1957f4a4dcfb8a9532","eof":"1","text":"播放前一首","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741597335}
send data finished:1741597335.6667485
{"recordId":"gty000baf60@dx1957f4a4e0eb8a9532:1253580ff3654b3fae1c533dc0d19bb0","requestId":"gty000baf60@dx1957f4a4e0eb8a9532","sessionId":"cid000baf5f@dx1957f4a4dcfb8a9532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"播放前一首","intentId":"INSTRUCTION","intentName":"音乐控制","nlg":"","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"播放前一首","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"操作","value":"past"}]}}},"timestamp":1741597335}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidb7bbc83e@dx4b461b29f6973eef00"}
连接正常关闭
1741597335
param:b'{\n            "auth_id": "f01145d4a1564a47987d840bbbc3efaa",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid342ac368@dx843e1b29f6973eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "f20b2e8a290747129bb56232f36a0869","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000baf64@dx1957f4a6207b8a9532"}
started:
ws start
####################
测试进行: ctm00010443@hu17b59a7c8d1020c902#46466977.pcm
{"recordId":"ase000e6fa2@hu1957f4a727205bf882:f20b2e8a290747129bb56232f36a0869","requestId":"ase000e6fa2@hu1957f4a727205bf882","sessionId":"cid000baf64@dx1957f4a6207b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597340}
{"recordId":"gty000baf65@dx1957f4a6246b8a9532:f20b2e8a290747129bb56232f36a0869","requestId":"gty000baf65@dx1957f4a6246b8a9532","sessionId":"cid000baf64@dx1957f4a6207b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597341}
{"recordId":"gty000baf65@dx1957f4a6246b8a9532:f20b2e8a290747129bb56232f36a0869","requestId":"gty000baf65@dx1957f4a6246b8a9532","sessionId":"cid000baf64@dx1957f4a6207b8a9532","eof":"1","text":"快进一分钟6秒","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741597341}
send data finished:1741597341.3149004
{"recordId":"gty000baf65@dx1957f4a6246b8a9532:f20b2e8a290747129bb56232f36a0869","requestId":"gty000baf65@dx1957f4a6246b8a9532","sessionId":"cid000baf64@dx1957f4a6207b8a9532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"快进一分钟6秒","intentId":"INSTRUCTION","intentName":"音乐控制","nlg":"","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"快进一分钟6秒","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"操作","value":"speed"},{"name":"秒","value":"6"},{"name":"分钟","value":"1"}]}}},"timestamp":1741597341}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cide090472e@dxad0a1b29f69d3eef00"}
连接正常关闭
1741597341
param:b'{\n            "auth_id": "f42717c015e84949bef1c269fdad04d0",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidbe4e34e9@dx9ad01b29f69d3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "b4898fa37e6d48f8bf6a7aaff5a8d036","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb6f8@dx1957f4a7826b8aa532"}
started:
ws start
####################
测试进行: ctm00010558@hu17b62d10b3d020c902#46579979.pcm
{"recordId":"ase000e82f3@hu1957f4a8c441323882:b4898fa37e6d48f8bf6a7aaff5a8d036","requestId":"ase000e82f3@hu1957f4a8c441323882","sessionId":"cid000bb6f8@dx1957f4a7826b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597347}
{"recordId":"gty000bb6f9@dx1957f4a7861b8aa532:b4898fa37e6d48f8bf6a7aaff5a8d036","requestId":"gty000bb6f9@dx1957f4a7861b8aa532","sessionId":"cid000bb6f8@dx1957f4a7826b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597349}
{"recordId":"gty000bb6f9@dx1957f4a7861b8aa532:b4898fa37e6d48f8bf6a7aaff5a8d036","requestId":"gty000bb6f9@dx1957f4a7861b8aa532","sessionId":"cid000bb6f8@dx1957f4a7826b8aa532","eof":"1","text":"我想看电视剧只要你过得比我好","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741597349}
send data finished:1741597349.8359792
{"recordId":"gty000bb6f9@dx1957f4a7861b8aa532:b4898fa37e6d48f8bf6a7aaff5a8d036","requestId":"gty000bb6f9@dx1957f4a7861b8aa532","sessionId":"cid000bb6f8@dx1957f4a7826b8aa532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"我想看电视剧只要你过得比我好","intentId":"chat","intentName":"闲聊","nlg":"机器人也不是万能的噢，这个我也不知道。","shouldEndSession":true},"nlu":{"input":"我想看电视剧只要你过得比我好","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741597349}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidaddc0f6f@dxcac21b29f6a63eef00"}
连接正常关闭
1741597350
param:b'{\n            "auth_id": "5d8253865f0443b99993a6c78f9f3dc3",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid567e6e87@dxebc71b29f6a63eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "ff46db84dea344b198e3b297868d3222","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb827@dx1957f4a99e0b86a532"}
started:
ws start
####################
测试进行: ctm00010562@hu17b62d11176020c902#46579992.pcm
{"recordId":"ase000fed4f@hu1957f4ab0d005c0882:ff46db84dea344b198e3b297868d3222","requestId":"ase000fed4f@hu1957f4ab0d005c0882","sessionId":"cid000bb827@dx1957f4a99e0b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597356}
{"recordId":"gty000bb828@dx1957f4a9a1cb86a532:ff46db84dea344b198e3b297868d3222","requestId":"gty000bb828@dx1957f4a9a1cb86a532","sessionId":"cid000bb827@dx1957f4a99e0b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597357}
{"recordId":"gty000bb828@dx1957f4a9a1cb86a532:ff46db84dea344b198e3b297868d3222","requestId":"gty000bb828@dx1957f4a9a1cb86a532","sessionId":"cid000bb827@dx1957f4a99e0b86a532","eof":"1","text":"东京是首都城市吗","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741597357}
send data finished:1741597357.2314153
{"recordId":"gty000bb828@dx1957f4a9a1cb86a532:ff46db84dea344b198e3b297868d3222","requestId":"gty000bb828@dx1957f4a9a1cb86a532","sessionId":"cid000bb827@dx1957f4a99e0b86a532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"东京是首都城市吗","intentId":"chat","intentName":"闲聊","nlg":"这个我不太懂，我们聊聊别的吧。","shouldEndSession":true},"nlu":{"input":"东京是首都城市吗","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741597357}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"ciddaa10a58@dx21e11b29f6ad3eef00"}
连接正常关闭
1741597357
param:b'{\n            "auth_id": "9ffbba978a01417c87f3c0b466201edb",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidcf09fb66@dx78941b29f6ad3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "121c3a9c84664f90917e8ece89939bba","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb831@dx1957f4ab6c7b86a532"}
started:
ws start
####################
测试进行: ctm00010573@hu17b62d11b05020c902#46580006.pcm
{"recordId":"ase000fc100@hu1957f4acbba05c4882:121c3a9c84664f90917e8ece89939bba","requestId":"ase000fc100@hu1957f4acbba05c4882","sessionId":"cid000bb831@dx1957f4ab6c7b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597363}
{"recordId":"gty000bb832@dx1957f4ab701b86a532:121c3a9c84664f90917e8ece89939bba","requestId":"gty000bb832@dx1957f4ab701b86a532","sessionId":"cid000bb831@dx1957f4ab6c7b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597364}
{"recordId":"gty000bb832@dx1957f4ab701b86a532:121c3a9c84664f90917e8ece89939bba","requestId":"gty000bb832@dx1957f4ab701b86a532","sessionId":"cid000bb831@dx1957f4ab6c7b86a532","eof":"1","text":"设置今天晚上6:00的闹钟","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741597365}
send data finished:1741597365.1118608
{"recordId":"gty000bb832@dx1957f4ab701b86a532:121c3a9c84664f90917e8ece89939bba","requestId":"gty000bb832@dx1957f4ab701b86a532","sessionId":"cid000bb831@dx1957f4ab6c7b86a532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"设置今天晚上6:00的闹钟","intentId":"chat","intentName":"闲聊","nlg":"机器人也不是万能的噢，这个我也不知道。","shouldEndSession":true},"nlu":{"input":"设置今天晚上6:00的闹钟","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741597365}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid599a9be5@dx346b1b29f6b53eef00"}
连接正常关闭
1741597365
param:b'{\n            "auth_id": "e2576cd7aac4449d9993c55d4548e156",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cida675e64b@dxf55e1b29f6b53eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "93916afecd3b44b38ae1997933e0b19c","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000baf84@dx1957f4ad591b8a9532"}
started:
ws start
####################
测试进行: ctm00010586@hu17b62d120df020c902#46580018.pcm
{"recordId":"ase000fc5e5@hu1957f4aeb4e05c4882:93916afecd3b44b38ae1997933e0b19c","requestId":"ase000fc5e5@hu1957f4aeb4e05c4882","sessionId":"cid000baf84@dx1957f4ad591b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597371}
{"recordId":"gty000baf85@dx1957f4ad5c5b8a9532:93916afecd3b44b38ae1997933e0b19c","requestId":"gty000baf85@dx1957f4ad5c5b8a9532","sessionId":"cid000baf84@dx1957f4ad591b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597372}
{"recordId":"gty000baf85@dx1957f4ad5c5b8a9532:93916afecd3b44b38ae1997933e0b19c","requestId":"gty000baf85@dx1957f4ad5c5b8a9532","sessionId":"cid000baf84@dx1957f4ad591b8a9532","eof":"1","text":"今天太阳什么时候出来","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741597372}
send data finished:1741597372.8350024
{"recordId":"gty000baf85@dx1957f4ad5c5b8a9532:93916afecd3b44b38ae1997933e0b19c","requestId":"gty000baf85@dx1957f4ad5c5b8a9532","sessionId":"cid000baf84@dx1957f4ad591b8a9532","topic":"dm.output","skill":"天气","skillId":"2019042500000544","speakUrl":"","error":{},"dm":{"input":"今天太阳什么时候出来","intentId":"QUERY","intentName":"查询天气信息","nlg":"北京市今天全天霾，7℃ ~ 17℃，南风微风，有点凉。","widget":{"webhookResp":{"result":[{"airData":143,"airQuality":"轻微污染","city":"北京市","date":"2025-03-10","dateLong":1741536000,"date_for_voice":"今天","exp":{"ct":{"expName":"穿衣指数","level":"温凉","prompt":"天气偏凉，增加衣物厚度。"},"dy":{"expName":"钓鱼指数","level":"不适宜","prompt":"雾霾天气，不适宜外出钓鱼。"},"gm":{"expName":"感冒指数","level":"易发","prompt":"感冒容易发生，少去人群密集的场所有利于降低感冒的几率。"},"jt":{"expName":"交通指数","level":"良好","prompt":"天气较好，路面干燥，交通气象条件良好，车辆可以正常行驶。"},"tr":{"expName":"旅游指数","level":"适宜","prompt":"天气较好，但丝毫不会影响您出行的心情。温度适宜又有微风相伴，适宜旅游。"},"uv":{"expName":"紫外线指数","level":"弱","prompt":"辐射较弱，请涂擦SPF12-15、PA+护肤品。"},"xc":{"expName":"洗车指数","level":"较适宜","prompt":"较适宜洗车，未来一天无雨，风力较小，擦洗一新的汽车至少能保持一天。"},"yd":{"expName":"运动指数","level":"不适宜","prompt":"空气轻度污染，不宜在户外运动。"}},"extra":"","humidity":"41%","img":"http://cdn9002.iflyos.cn/osweathericon/53.png","lastUpdateTime":"2025-03-10 16:00:08","pm25":"143","precipitation":"0","sunRise":"2025-03-10 06:34:00","sunSet":"2025-03-10 18:16:00","temp":15,"tempHigh":"17℃","tempLow":"7℃","tempRange":"7℃ ~ 17℃","tempReal":"13℃","visibility":"","warning":"","weather":"霾","weatherDescription":"有点凉。","weatherDescription3":"2℃到17℃，风不大，有点凉。","weatherDescription7":"-1℃到8℃，风不大，有点冷。","weatherType":53,"week":"周一","wind":"南风微风","windLevel":0},{"airData":143,"airQuality":"轻微污染","city":"北京市","date":"2025-03-09","dateLong":1741449600,"date_for_voice":"昨天","humidity":"42%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-03-10 16:00:08","sunRise":"2025-03-09 06:35:00","sunSet":"2025-03-09 18:14:00","tempHigh":"17℃","tempLow":"7℃","tempRange":"7℃ ~ 17℃","weather":"晴转多云","weatherDescription":"有点凉。","weatherType":0,"week":"周日","wind":"西南风转南风3-4级","windLevel":1},{"airData":180,"airQuality":"轻度污染","city":"北京市","date":"2025-03-11","dateLong":1741622400,"date_for_voice":"明天","humidity":"57%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-03-10 16:00:08","sunRise":"2025-03-11 06:32:00","sunSet":"2025-03-11 18:17:00","tempHigh":"16℃","tempLow":"5℃","tempRange":"5℃ ~ 16℃","weather":"多云转浮尘","weatherDescription":"有点凉。","weatherType":1,"week":"周二","wind":"西风转西北风微风","windLevel":0},{"airData":90,"airQuality":"良","city":"北京市","date":"2025-03-12","dateLong":1741708800,"date_for_voice":"后天","humidity":"15%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-03-10 16:00:08","sunRise":"2025-03-12 06:31:00","sunSet":"2025-03-12 18:18:00","tempHigh":"16℃","tempLow":"2℃","tempRange":"2℃ ~ 16℃","weather":"晴","weatherDescription":"有点凉。","weatherType":0,"week":"周三","wind":"西北风转东南风3-4级","windLevel":1},{"airData":70,"airQuality":"良","city":"北京市","date":"2025-03-13","dateLong":1741795200,"date_for_voice":"13号","humidity":"21%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-03-10 16:00:08","sunRise":"2025-03-13 06:29:00","sunSet":"2025-03-13 18:19:00","tempHigh":"15℃","tempLow":"3℃","tempRange":"3℃ ~ 15℃","weather":"多云","weatherDescription":"有点凉。","weatherType":1,"week":"周四","wind":"西南风转东南风3-4级","windLevel":1},{"airData":90,"airQuality":"良","city":"北京市","date":"2025-03-14","dateLong":1741881600,"date_for_voice":"14号","humidity":"42%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-03-10 16:00:08","sunRise":"2025-03-14 06:27:00","sunSet":"2025-03-14 18:20:00","tempHigh":"10℃","tempLow":"3℃","tempRange":"3℃ ~ 10℃","weather":"多云转阴","weatherDescription":"有点冷。","weatherType":1,"week":"周五","wind":"东北风微风","windLevel":0},{"airData":60,"airQuality":"良","city":"北京市","date":"2025-03-15","dateLong":1741968000,"date_for_voice":"15号","humidity":"52%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-03-10 16:00:08","sunRise":"2025-03-15 06:26:00","sunSet":"2025-03-15 18:21:00","tempHigh":"8℃","tempLow":"1℃","tempRange":"1℃ ~ 8℃","weather":"阴","weatherDescription":"有点冷。","weatherType":2,"week":"周六","wind":"东北风转西北风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-16","dateLong":1742054400,"date_for_voice":"16号","humidity":"23%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-03-10 16:00:08","sunRise":"2025-03-16 06:24:00","sunSet":"2025-03-16 18:22:00","tempHigh":"8℃","tempLow":"-1℃","tempRange":"-1℃ ~ 8℃","weather":"多云","weatherDescription":"有点冷。","weatherType":1,"week":"周日","wind":"西北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-17","dateLong":1742140800,"date_for_voice":"17号","humidity":"29%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-03-10 16:00:08","sunRise":"2025-03-17 06:23:00","sunSet":"2025-03-17 18:23:00","tempHigh":"12℃","tempLow":"0℃","tempRange":"0℃ ~ 12℃","weather":"多云转晴","weatherDescription":"有点冷。","weatherType":1,"week":"下周一","wind":"西北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-18","dateLong":1742227200,"date_for_voice":"18号","humidity":"28%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-03-10 16:00:08","sunRise":"2025-03-18 06:21:00","sunSet":"2025-03-18 18:24:00","tempHigh":"9℃","tempLow":"1℃","tempRange":"1℃ ~ 9℃","weather":"晴","weatherDescription":"有点冷。","weatherType":0,"week":"下周二","wind":"西北风转西南风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-19","dateLong":1742313600,"date_for_voice":"19号","humidity":"19%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-03-10 16:00:08","sunRise":"2025-03-19 06:19:00","sunSet":"2025-03-19 18:25:00","tempHigh":"18℃","tempLow":"4℃","tempRange":"4℃ ~ 18℃","weather":"晴转阴","weatherDescription":"有点凉。","weatherType":0,"week":"下周三","wind":"西北风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-20","dateLong":1742400000,"date_for_voice":"20号","humidity":"21%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-03-10 16:00:08","sunRise":"2025-03-20 06:18:00","sunSet":"2025-03-20 18:26:00","tempHigh":"18℃","tempLow":"7℃","tempRange":"7℃ ~ 18℃","weather":"阴转小雨","weatherDescription":"有点凉。","weatherType":2,"week":"下周四","wind":"东南风转西南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-21","dateLong":1742486400,"date_for_voice":"21号","humidity":"22%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-03-10 16:00:08","sunRise":"2025-03-21 06:16:00","sunSet":"2025-03-21 18:27:00","tempHigh":"16℃","tempLow":"7℃","tempRange":"7℃ ~ 16℃","weather":"阴转小雨","weatherDescription":"有点凉。","weatherType":2,"week":"下周五","wind":"西南风转东南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-22","dateLong":1742572800,"date_for_voice":"22号","humidity":"25%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-03-10 16:00:08","sunRise":"2025-03-22 06:15:00","sunSet":"2025-03-22 18:28:00","tempHigh":"24℃","tempLow":"7℃","tempRange":"7℃ ~ 24℃","weather":"晴转多云","weatherDescription":"气候温暖。","weatherType":0,"week":"下周六","wind":"东北风转西北风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-23","dateLong":1742659200,"date_for_voice":"23号","humidity":"21%","img":"http://cdn9002.iflyos.cn/osweathericon/07.png","lastUpdateTime":"2025-03-10 16:00:08","sunRise":"2025-03-23 06:13:00","sunSet":"2025-03-23 18:29:00","tempHigh":"26℃","tempLow":"4℃","tempRange":"4℃ ~ 26℃","weather":"小雨转晴","weatherDescription":"气候温暖。","weatherType":7,"week":"下周日","wind":"东北风转西北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-24","dateLong":1742745600,"date_for_voice":"24号","humidity":"21%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-03-10 16:00:08","sunRise":"2025-03-24 06:11:00","sunSet":"2025-03-24 18:30:00","tempHigh":"14℃","tempLow":"2℃","tempRange":"2℃ ~ 14℃","weather":"晴转阴","weatherDescription":"有点凉。","weatherType":0,"week":"下下周一","wind":"西南风转西北风3-4级","windLevel":1}]},"cityName":"北京市"},"dm_intent":"custom","cityName":"北京市","shouldEndSession":true},"nlu":{"input":"今天太阳什么时候出来","skill":"天气","skillId":"2019042500000544","skillVersion":"56","semantics":{"request":{"intent":"QUERY","slots":[{"name":"queryType","value":"内容"},{"name":"subfocus","value":"日出时间"},{"name":"datetime","normValue":"{\"datetime\":\"2025-03-10\",\"suggestDatetime\":\"2025-03-10\"}","value":"今天"},{"name":"location.type","normValue":"LOC_POI","value":"LOC_POI"},{"name":"location.city","normValue":"CURRENT_CITY","value":"CURRENT_CITY"},{"name":"location.poi","normValue":"CURRENT_POI","value":"CURRENT_POI"}]}}},"timestamp":1741597372}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidcea4be03@dxf1631b29f6bc3eef00"}
连接正常关闭
1741597372
param:b'{\n            "auth_id": "6726bdcd1a044039bbebc767ecd05164",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidf381c385@dx55711b29f6bd3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "2ca948b4ba77459cbbe0ec7fe599696b","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb714@dx1957f4af358b8aa532"}
started:
ws start
####################
测试进行: ctm00010e4a@hu17b5411338f0212902#46356772.pcm
{"recordId":"ase000d1e8e@hu1957f4b04190427882:2ca948b4ba77459cbbe0ec7fe599696b","requestId":"ase000d1e8e@hu1957f4b04190427882","sessionId":"cid000bb714@dx1957f4af358b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597377}
{"recordId":"gty000bb715@dx1957f4af393b8aa532:2ca948b4ba77459cbbe0ec7fe599696b","requestId":"gty000bb715@dx1957f4af393b8aa532","sessionId":"cid000bb714@dx1957f4af358b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597379}
{"recordId":"gty000bb715@dx1957f4af393b8aa532:2ca948b4ba77459cbbe0ec7fe599696b","requestId":"gty000bb715@dx1957f4af393b8aa532","sessionId":"cid000bb714@dx1957f4af358b8aa532","eof":"1","text":"4/5÷12","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741597379}
send data finished:1741597379.4691317
{"recordId":"gty000bb715@dx1957f4af393b8aa532:2ca948b4ba77459cbbe0ec7fe599696b","requestId":"gty000bb715@dx1957f4af393b8aa532","sessionId":"cid000bb714@dx1957f4af358b8aa532","topic":"dm.output","skill":"计算器","skillId":"2019031500001072","speakUrl":"","error":{},"dm":{"input":"4/5÷12","intentId":"CALC_ANSWER","intentName":"直接返回计算结果","nlg":"等于0.0667","shouldEndSession":true},"nlu":{"input":"4/5÷12","skill":"计算器","skillId":"2019031500001072","skillVersion":"11","semantics":{"request":{"entrypoint":"ent","hazard":false,"score":1,"slots":[{"begin":0,"end":6,"name":"Calculator","normValue":"0.0667","value":"4/5÷12"}],"template":"{Calculator}"}}},"timestamp":1741597379}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid0a7863d3@dxa76a1b29f6c33eef00"}
连接正常关闭
1741597379
param:b'{\n            "auth_id": "abff6019723446d69300256f01aee6b3",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid6cc1b56d@dxc6161b29f6c33eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "e1f62f2df49445e59637b4a53b6e4525","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000baf94@dx1957f4b0d0db8a9532"}
started:
ws start
####################
测试进行: ctm00010e5c@hu17b54113cf90212902#46356799.pcm
{"recordId":"ase000e9a47@hu1957f4b1f2a1323882:e1f62f2df49445e59637b4a53b6e4525","requestId":"ase000e9a47@hu1957f4b1f2a1323882","sessionId":"cid000baf94@dx1957f4b0d0db8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597384}
{"recordId":"gty000baf95@dx1957f4b0d43b8a9532:e1f62f2df49445e59637b4a53b6e4525","requestId":"gty000baf95@dx1957f4b0d43b8a9532","sessionId":"cid000baf94@dx1957f4b0d0db8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597385}
{"recordId":"gty000baf95@dx1957f4b0d43b8a9532:e1f62f2df49445e59637b4a53b6e4525","requestId":"gty000baf95@dx1957f4b0d43b8a9532","sessionId":"cid000baf94@dx1957f4b0d0db8a9532","eof":"1","text":"京东购物关闭","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741597385}
send data finished:1741597385.294478
{"recordId":"gty000baf95@dx1957f4b0d43b8a9532:e1f62f2df49445e59637b4a53b6e4525","requestId":"gty000baf95@dx1957f4b0d43b8a9532","sessionId":"cid000baf94@dx1957f4b0d0db8a9532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"京东购物关闭","intentId":"chat","intentName":"闲聊","nlg":"我是诚实的好孩子，这个问题我也不会。","shouldEndSession":true},"nlu":{"input":"京东购物关闭","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741597385}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid9406c2a8@dx14ac1b29f6c93eef00"}
连接正常关闭
1741597385
param:b'{\n            "auth_id": "58a8a58bf9f04513a8ed8da34b7b702e",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid87b0585e@dxd59a1b29f6c93eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "580d05bf9cfa4dd595a1d36f22969e61","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb725@dx1957f4b245db8aa532"}
started:
ws start
####################
测试进行: ctm00011399@hu17b59b5998e020c902#46470195.pcm
{"recordId":"ase000f022a@hu1957f4b362205c0882:580d05bf9cfa4dd595a1d36f22969e61","requestId":"ase000f022a@hu1957f4b362205c0882","sessionId":"cid000bb725@dx1957f4b245db8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597390}
{"recordId":"gty000bb727@dx1957f4b2496b8aa532:580d05bf9cfa4dd595a1d36f22969e61","requestId":"gty000bb727@dx1957f4b2496b8aa532","sessionId":"cid000bb725@dx1957f4b245db8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597391}
{"recordId":"gty000bb727@dx1957f4b2496b8aa532:580d05bf9cfa4dd595a1d36f22969e61","requestId":"gty000bb727@dx1957f4b2496b8aa532","sessionId":"cid000bb725@dx1957f4b245db8aa532","eof":"1","text":"播放窗外的小豆豆","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597391}
send data finished:1741597391.5732315
{"recordId":"gty000bb727@dx1957f4b2496b8aa532:580d05bf9cfa4dd595a1d36f22969e61","requestId":"gty000bb727@dx1957f4b2496b8aa532","sessionId":"cid000bb725@dx1957f4b245db8aa532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"播放窗外的小豆豆","intentId":"chat","intentName":"闲聊","nlg":"虽然不想让你失望，但这个问题我真的不会。","shouldEndSession":true},"nlu":{"input":"播放窗外的小豆豆","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741597391}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid07206f21@dxa6581b29f6cf3eef00"}
连接正常关闭
1741597391
param:b'{\n            "auth_id": "7d4ea97ea6f949b5914cf6a199ad1bb2",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid640620aa@dx998a1b29f6cf3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "11f6ab2156794a858ca3119de1ed548a","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb730@dx1957f4b3cc5b8aa532"}
started:
ws start
####################
测试进行: ctm000113b3@hu17b59b5a724020c902#46470220.pcm
{"recordId":"gty000bb732@dx1957f4b3cfdb8aa532:11f6ab2156794a858ca3119de1ed548a","requestId":"gty000bb732@dx1957f4b3cfdb8aa532","sessionId":"cid000bb730@dx1957f4b3cc5b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597396}
{"recordId":"gty000bb732@dx1957f4b3cfdb8aa532:11f6ab2156794a858ca3119de1ed548a","requestId":"gty000bb732@dx1957f4b3cfdb8aa532","sessionId":"cid000bb730@dx1957f4b3cc5b8aa532","eof":"1","text":"春节小品","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741597396}
send data finished:1741597396.958764
{"recordId":"gty000bb732@dx1957f4b3cfdb8aa532:11f6ab2156794a858ca3119de1ed548a","requestId":"gty000bb732@dx1957f4b3cfdb8aa532","sessionId":"cid000bb730@dx1957f4b3cc5b8aa532","topic":"dm.output","skill":"有声节目","skillId":"IFLYTEK.audioProgram","speakUrl":"","error":{},"dm":{"input":"春节小品","intentId":"PLAY","intentName":"播放","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"春节小品","skill":"有声节目","skillId":"IFLYTEK.audioProgram","skillVersion":"187.0","semantics":{"request":{"slots":[{"name":"category","value":"小品"},{"name":"subCategory","value":"春节"}]}}},"timestamp":1741597396}
{"recordId":"ase000f14e7@hu1957f4b4fb705c2882:11f6ab2156794a858ca3119de1ed548a","requestId":"ase000f14e7@hu1957f4b4fb705c2882","sessionId":"cid000bb730@dx1957f4b3cc5b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597397}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidddab4af2@dx25241b29f6d53eef00"}
连接正常关闭
1741597397
param:b'{\n            "auth_id": "9e2fa24ecdda4d1bbe76a573445e254f",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidd8f83b19@dxe9611b29f6d53eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "13ec8ea644934a9e8aacc6faabc80f4c","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb9a6@dx1957f4b51fa7844532"}
started:
ws start
####################
测试进行: ctm000113d0@hu17b59b5b6c3020c902#46470269.pcm
{"recordId":"ase000f17ad@hu1957f4b61c505c2882:13ec8ea644934a9e8aacc6faabc80f4c","requestId":"ase000f17ad@hu1957f4b61c505c2882","sessionId":"cid000bb9a6@dx1957f4b51fa7844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597401}
{"recordId":"gty000bb9a7@dx1957f4b52327844532:13ec8ea644934a9e8aacc6faabc80f4c","requestId":"gty000bb9a7@dx1957f4b52327844532","sessionId":"cid000bb9a6@dx1957f4b51fa7844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597402}
{"recordId":"gty000bb9a7@dx1957f4b52327844532:13ec8ea644934a9e8aacc6faabc80f4c","requestId":"gty000bb9a7@dx1957f4b52327844532","sessionId":"cid000bb9a6@dx1957f4b51fa7844532","eof":"1","text":"于谦的相声","pinyin":"","topic":"asr.speech.result","age":"elder","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597402}
send data finished:1741597402.3417442
{"recordId":"gty000bb9a7@dx1957f4b52327844532:13ec8ea644934a9e8aacc6faabc80f4c","requestId":"gty000bb9a7@dx1957f4b52327844532","sessionId":"cid000bb9a6@dx1957f4b51fa7844532","topic":"dm.output","skill":"有声节目","skillId":"IFLYTEK.audioProgram","speakUrl":"","error":{},"dm":{"input":"于谦的相声","intentId":"PLAY","intentName":"播放","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"于谦的相声","skill":"有声节目","skillId":"IFLYTEK.audioProgram","skillVersion":"187.0","semantics":{"request":{"slots":[{"name":"actor","value":"于谦"},{"name":"category","value":"相声"}]}}},"timestamp":1741597402}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid386c2e28@dx3a621b29f6da3eef00"}
连接正常关闭
1741597402
param:b'{\n            "auth_id": "0c39ac5018bb45d5a22d4f55b59d73c1",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid07e792ec@dx24481b29f6da3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "8ef6fdde5d394de490d4962ce7eb6b20","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb84a@dx1957f4b6660b86a532"}
started:
ws start
####################
测试进行: ctm0001148b@hu17b6230bc850212902#46571615.pcm
{"recordId":"ase000e989d@hu1957f4b787b05bf882:8ef6fdde5d394de490d4962ce7eb6b20","requestId":"ase000e989d@hu1957f4b787b05bf882","sessionId":"cid000bb84a@dx1957f4b6660b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597407}
{"recordId":"ase000dbf65@hu1957f4b791704d3882:8ef6fdde5d394de490d4962ce7eb6b20","requestId":"ase000dbf65@hu1957f4b791704d3882","sessionId":"cid000bb84a@dx1957f4b6660b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597407}
{"recordId":"gty000bb84b@dx1957f4b6697b86a532:8ef6fdde5d394de490d4962ce7eb6b20","requestId":"gty000bb84b@dx1957f4b6697b86a532","sessionId":"cid000bb84a@dx1957f4b6660b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597408}
{"recordId":"gty000bb84b@dx1957f4b6697b86a532:8ef6fdde5d394de490d4962ce7eb6b20","requestId":"gty000bb84b@dx1957f4b6697b86a532","sessionId":"cid000bb84a@dx1957f4b6660b86a532","eof":"1","text":"我爸爸的姐姐的妈妈是谁","pinyin":"","topic":"asr.speech.result","age":"child","ageScore":"","gender":"","genderScore":"","language_class":"mandarin","timestamp":1741597408}
send data finished:1741597408.905041
{"recordId":"gty000bb84b@dx1957f4b6697b86a532:8ef6fdde5d394de490d4962ce7eb6b20","requestId":"gty000bb84b@dx1957f4b6697b86a532","sessionId":"cid000bb84a@dx1957f4b6660b86a532","topic":"dm.output","skill":"LEIQIAO.relationShip","skillId":"LEIQIAO.relationShip","speakUrl":"","error":{},"dm":{"input":"我爸爸的姐姐的妈妈是谁","intentId":"CALL_ELSE","intentName":"查询","nlg":"爸爸的姐姐的妈妈是你的奶奶","shouldEndSession":true},"nlu":{"input":"我爸爸的姐姐的妈妈是谁","skill":"LEIQIAO.relationShip","skillId":"LEIQIAO.relationShip","skillVersion":"74.0","semantics":{"request":{"entrypoint":"ent","hazard":false,"score":1,"slots":[{"begin":1,"end":3,"name":"call","normValue":"爸爸","value":"爸爸"},{"begin":4,"end":6,"name":"call","normValue":"姐姐","value":"姐姐"},{"begin":7,"end":9,"name":"call","normValue":"妈妈","value":"妈妈"}],"template":"我{call}的{call}的{call}是谁"}}},"timestamp":1741597408}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cida827e5ec@dx4e0e1b29f6e03eef00"}
连接正常关闭
1741597409
param:b'{\n            "auth_id": "4e516f7037494e92acc3c04c48aea11f",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid47acb349@dx22ac1b29f6e13eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "33434e0329644886adc5e314d61db2d4","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb9a8@dx1957f4b80317844532"}
started:
ws start
####################
测试进行: ctm00011492@hu17b6230c21d0212902#46571629.pcm
{"recordId":"ase000fdfb7@hu1957f4b937205c4882:33434e0329644886adc5e314d61db2d4","requestId":"ase000fdfb7@hu1957f4b937205c4882","sessionId":"cid000bb9a8@dx1957f4b80317844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597414}
{"recordId":"gty000bb9a9@dx1957f4b806d7844532:33434e0329644886adc5e314d61db2d4","requestId":"gty000bb9a9@dx1957f4b806d7844532","sessionId":"cid000bb9a8@dx1957f4b80317844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597414}
{"recordId":"gty000bb9a9@dx1957f4b806d7844532:33434e0329644886adc5e314d61db2d4","requestId":"gty000bb9a9@dx1957f4b806d7844532","sessionId":"cid000bb9a8@dx1957f4b80317844532","eof":"1","text":"买部手机吧","pinyin":"","topic":"asr.speech.result","age":"child","ageScore":"","gender":"","genderScore":"","language_class":"mandarin","timestamp":1741597414}
send data finished:1741597414.962531
{"recordId":"gty000bb9a9@dx1957f4b806d7844532:33434e0329644886adc5e314d61db2d4","requestId":"gty000bb9a9@dx1957f4b806d7844532","sessionId":"cid000bb9a8@dx1957f4b80317844532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"买部手机吧","intentId":"chat","intentName":"闲聊","nlg":"我还小，不知道你在说什么，我们聊点别的吧。","shouldEndSession":true},"nlu":{"input":"买部手机吧","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741597415}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cide4607974@dxc5661b29f6e73eef00"}
连接正常关闭
1741597415
param:b'{\n            "auth_id": "78c560db796b4ff09ee3ddc6dc9d7871",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid9f7bc99f@dxd8351b29f6e73eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "d3ddd579317e44d8ab988e379eba5b73","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb747@dx1957f4b9827b8aa532"}
started:
ws start
####################
测试进行: ctm000114a0@hu17b6230cc5c0212902#46571646.pcm
{"recordId":"ase000f1345@hu1957f4baaf205c0882:d3ddd579317e44d8ab988e379eba5b73","requestId":"ase000f1345@hu1957f4baaf205c0882","sessionId":"cid000bb747@dx1957f4b9827b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597420}
{"recordId":"gty000bb748@dx1957f4b9861b8aa532:d3ddd579317e44d8ab988e379eba5b73","requestId":"gty000bb748@dx1957f4b9861b8aa532","sessionId":"cid000bb747@dx1957f4b9827b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597421}
{"recordId":"gty000bb748@dx1957f4b9861b8aa532:d3ddd579317e44d8ab988e379eba5b73","requestId":"gty000bb748@dx1957f4b9861b8aa532","sessionId":"cid000bb747@dx1957f4b9827b8aa532","eof":"1","text":"一公斤大约等于多少平方米","pinyin":"","topic":"asr.speech.result","age":"child","ageScore":"","gender":"","genderScore":"","language_class":"mandarin","timestamp":1741597421}
send data finished:1741597421.653467
{"recordId":"gty000bb748@dx1957f4b9861b8aa532:d3ddd579317e44d8ab988e379eba5b73","requestId":"gty000bb748@dx1957f4b9861b8aa532","sessionId":"cid000bb747@dx1957f4b9827b8aa532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"一公斤大约等于多少平方米","intentId":"chat","intentName":"闲聊","nlg":"等我学习一下，再来回答吧！现在不如问点其他的吧。","shouldEndSession":true},"nlu":{"input":"一公斤大约等于多少平方米","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741597421}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid5b4ccef5@dx72b91b29f6ed3eef00"}
连接正常关闭
1741597421
param:b'{\n            "auth_id": "be7755f5c6d54e4d903285f80f2c0b3a",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid0a9b1fdd@dxbc641b29f6ed3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "17754c98af3642e7ac24a9da7473720f","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb755@dx1957f4bb232b8aa532"}
started:
ws start
####################
测试进行: ctm000114af@hu17b6230d0130212902#46571653.pcm
{"recordId":"ase000e797d@hu1957f4bc3c205c3882:17754c98af3642e7ac24a9da7473720f","requestId":"ase000e797d@hu1957f4bc3c205c3882","sessionId":"cid000bb755@dx1957f4bb232b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597426}
{"recordId":"gty000bb756@dx1957f4bb269b8aa532:17754c98af3642e7ac24a9da7473720f","requestId":"gty000bb756@dx1957f4bb269b8aa532","sessionId":"cid000bb755@dx1957f4bb232b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597427}
{"recordId":"gty000bb756@dx1957f4bb269b8aa532:17754c98af3642e7ac24a9da7473720f","requestId":"gty000bb756@dx1957f4bb269b8aa532","sessionId":"cid000bb755@dx1957f4bb232b8aa532","eof":"1","text":"我要你取消闹钟","pinyin":"","topic":"asr.speech.result","age":"child","ageScore":"","gender":"","genderScore":"","language_class":"mandarin","timestamp":1741597427}
send data finished:1741597427.7619393
{"recordId":"gty000bb756@dx1957f4bb269b8aa532:17754c98af3642e7ac24a9da7473720f","requestId":"gty000bb756@dx1957f4bb269b8aa532","sessionId":"cid000bb755@dx1957f4bb232b8aa532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"我要你取消闹钟","intentId":"chat","intentName":"闲聊","nlg":"这个领域我还正在研究哦，要不我们说点其他的吧！","shouldEndSession":true},"nlu":{"input":"我要你取消闹钟","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741597427}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid0b799605@dx89091b29f6f33eef00"}
连接正常关闭
1741597428
param:b'{\n            "auth_id": "3661fd7dac704900a31a76b7f1452e49",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cide90a2ef1@dx79301b29f6f43eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "59ab837fff8843c7b63f33aef7a6a281","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb85c@dx1957f4bca65b86a532"}
started:
ws start
####################
测试进行: ctm00011891@hu17b59bae0c6020c902#46471001.pcm
{"recordId":"ase000f1adb@hu1957f4bdb3205c0882:59ab837fff8843c7b63f33aef7a6a281","requestId":"ase000f1adb@hu1957f4bdb3205c0882","sessionId":"cid000bb85c@dx1957f4bca65b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597432}
{"recordId":"gty000bb85d@dx1957f4bca9ab86a532:59ab837fff8843c7b63f33aef7a6a281","requestId":"gty000bb85d@dx1957f4bca9ab86a532","sessionId":"cid000bb85c@dx1957f4bca65b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597434}
{"recordId":"gty000bb85d@dx1957f4bca9ab86a532:59ab837fff8843c7b63f33aef7a6a281","requestId":"gty000bb85d@dx1957f4bca9ab86a532","sessionId":"cid000bb85c@dx1957f4bca65b86a532","eof":"1","text":"马上给我播放儿歌小牧童","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597434}
send data finished:1741597434.6308267
{"recordId":"gty000bb85d@dx1957f4bca9ab86a532:59ab837fff8843c7b63f33aef7a6a281","requestId":"gty000bb85d@dx1957f4bca9ab86a532","sessionId":"cid000bb85c@dx1957f4bca65b86a532","topic":"dm.output","skill":"国学","skillId":"AIUI.chLiterature","speakUrl":"","error":{},"dm":{"input":"马上给我播放儿歌小牧童","intentId":"QUERY_GROUP","intentName":"综合查询","nlg":"即将为你播放《牧童》－吕洞宾和小牧童","widget":{"content":[{"album":"五年级必背古诗文－国学小课堂","language":"CN","tags":"内容分类 亲子 国学启蒙 国学小课堂","title":"《牧童》－吕洞宾和小牧童","subTitle":"","imageUrl":"http://p0.ifengimg.com/a/2019_02/b87fafbcb7a5666_size246_w640_h640.jpg","linkUrl":"https://p6.renbenzhihui.com/video19.ifeng.com/video09/2019/01/09/4966760-543-066-1422.mp3?pf=OH9GI&vid=4502820&tm=1741597434841&pid=212031&t=1741597434&k=7914FD04F6C474AD6B7C9D8437AF0DB5","id":"4502820","extra":{"source":"ifeng"}},{"album":"一年级必背古诗文－国学小课堂","language":"CN","tags":"内容分类 亲子 国学启蒙 国学小课堂","title":"《所见》－牛背上的小牧童","subTitle":"","imageUrl":"http://p0.ifengimg.com/a/2019_02/b091359975ea57c_size188_w640_h640.jpg","linkUrl":"https://p6.renbenzhihui.com/video19.ifeng.com/video09/2019/01/09/4966700-543-066-1343.mp3?pf=OH9GI&vid=4502784&tm=1741597434843&pid=212029&t=1741597434&k=43AE9F76D8E7895ABE5DB6819FC3F288","id":"4502784","extra":{"source":"ifeng"}}]},"shouldEndSession":true},"nlu":{"input":"马上给我播放儿歌小牧童","skill":"国学","skillId":"AIUI.chLiterature","skillVersion":"2.0","semantics":{"request":{"entrypoint":"ent","score":0.8378620147705078,"slots":[{"begin":9,"end":11,"name":"name","normValue":"牧童","value":"牧童"}],"template":"{play}儿童国学{name}"}}},"timestamp":1741597434}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid6784e584@dx55311b29f6fa3eef00"}
连接正常关闭
1741597434
param:b'{\n            "auth_id": "1e9848d89a6d419e8727d4d68c646c01",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidb0a04a15@dx9cea1b29f6fb3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "ab7f9aea76c6404db0814e8bb5a4cffe","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bafb8@dx1957f4be582b8a9532"}
started:
ws start
####################
测试进行: ctm0001189a@hu17b59bae594020c902#46471011.pcm
{"recordId":"ase000e8179@hu1957f4bf60c05c3882:ab7f9aea76c6404db0814e8bb5a4cffe","requestId":"ase000e8179@hu1957f4bf60c05c3882","sessionId":"cid000bafb8@dx1957f4be582b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597439}
{"recordId":"gty000bafb9@dx1957f4be5b5b8a9532:ab7f9aea76c6404db0814e8bb5a4cffe","requestId":"gty000bafb9@dx1957f4be5b5b8a9532","sessionId":"cid000bafb8@dx1957f4be582b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597441}
{"recordId":"gty000bafb9@dx1957f4be5b5b8a9532:ab7f9aea76c6404db0814e8bb5a4cffe","requestId":"gty000bafb9@dx1957f4be5b5b8a9532","sessionId":"cid000bafb8@dx1957f4be582b8a9532","eof":"1","text":"有没有小蓓蕾组合唱的儿歌","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597441}
send data finished:1741597441.6105692
{"recordId":"gty000bafb9@dx1957f4be5b5b8a9532:ab7f9aea76c6404db0814e8bb5a4cffe","requestId":"gty000bafb9@dx1957f4be5b5b8a9532","sessionId":"cid000bafb8@dx1957f4be582b8a9532","topic":"dm.output","skill":"儿歌","skillId":"2019031500001056","speakUrl":"","error":{},"dm":{"input":"有没有小蓓蕾组合唱的儿歌","intentId":"QUERY_BY_ARTIST","intentName":"根据歌手名点播儿歌","nlg":"没有找到合适内容","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"有没有小蓓蕾组合唱的儿歌","skill":"儿歌","skillId":"2019031500001056","skillVersion":"8","semantics":{"request":{"entrypoint":"ent","hazard":false,"score":1,"slots":[{"name":"歌手名","value":"小蓓蕾组合"}],"template":"有没有{artist}唱的儿歌"}}},"timestamp":1741597441}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid49a62ea6@dx14cb1b29f7013eef00"}
连接正常关闭
1741597441
param:b'{\n            "auth_id": "7bbc0c9dca324a788b5a0372569374fd",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid71aa2b39@dxe17b1b29f7013eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "a0e6e8ad73754f38a3e6ae9d8e594b5e","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bafca@dx1957f4bffcfb8a9532"}
started:
ws start
####################
测试进行: ctm000118a9@hu17b59baf074020c902#46471029.pcm
{"recordId":"ase000f2378@hu1957f4c106c05c0882:a0e6e8ad73754f38a3e6ae9d8e594b5e","requestId":"ase000f2378@hu1957f4c106c05c0882","sessionId":"cid000bafca@dx1957f4bffcfb8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597446}
{"recordId":"gty000bafcb@dx1957f4c002cb8a9532:a0e6e8ad73754f38a3e6ae9d8e594b5e","requestId":"gty000bafcb@dx1957f4c002cb8a9532","sessionId":"cid000bafca@dx1957f4bffcfb8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597447}
{"recordId":"gty000bafcb@dx1957f4c002cb8a9532:a0e6e8ad73754f38a3e6ae9d8e594b5e","requestId":"gty000bafcb@dx1957f4c002cb8a9532","sessionId":"cid000bafca@dx1957f4bffcfb8a9532","eof":"1","text":"可以帮我推荐个股票吗","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597447}
send data finished:1741597447.7876322
{"recordId":"gty000bafcb@dx1957f4c002cb8a9532:a0e6e8ad73754f38a3e6ae9d8e594b5e","requestId":"gty000bafcb@dx1957f4c002cb8a9532","sessionId":"cid000bafca@dx1957f4bffcfb8a9532","topic":"dm.output","skill":"股票","skillId":"IFLYTEK.stock","speakUrl":"","error":{},"dm":{"input":"可以帮我推荐个股票吗","intentId":"STOCK_RECOMMEND","intentName":"推荐股票","nlg":"我还不能预知未来股市，无法为您推荐股票。","shouldEndSession":true},"nlu":{"input":"可以帮我推荐个股票吗","skill":"股票","skillId":"IFLYTEK.stock","skillVersion":"375.0","semantics":{"request":{"slots":[]}}},"timestamp":1741597447}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid1a6ee3bf@dx73a61b29f7073eef00"}
连接正常关闭
1741597447
param:b'{\n            "auth_id": "eca4e157e6494822af1202a33461e600",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cida8bbede2@dx363d1b29f7083eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "f7f88ad16276465a8dd4100f01471b46","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb9d6@dx1957f4c17e77844532"}
started:
ws start
####################
测试进行: ctm000118b6@hu17b4fbfceea020c902#46308921.pcm
{"recordId":"ase000f28ae@hu1957f4c2f4805c0882:f7f88ad16276465a8dd4100f01471b46","requestId":"ase000f28ae@hu1957f4c2f4805c0882","sessionId":"cid000bb9d6@dx1957f4c17e77844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597454}
{"recordId":"gty000bb9d7@dx1957f4c181d7844532:f7f88ad16276465a8dd4100f01471b46","requestId":"gty000bb9d7@dx1957f4c181d7844532","sessionId":"cid000bb9d6@dx1957f4c17e77844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597455}
{"recordId":"gty000bb9d7@dx1957f4c181d7844532:f7f88ad16276465a8dd4100f01471b46","requestId":"gty000bb9d7@dx1957f4c181d7844532","sessionId":"cid000bb9d6@dx1957f4c17e77844532","eof":"1","text":"我想听长发公主的故事","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597455}
{"recordId":"gty000bb9d7@dx1957f4c181d7844532:f7f88ad16276465a8dd4100f01471b46","requestId":"gty000bb9d7@dx1957f4c181d7844532","sessionId":"cid000bb9d6@dx1957f4c17e77844532","topic":"dm.output","skill":"有声节目","skillId":"IFLYTEK.audioProgram","speakUrl":"","error":{},"dm":{"input":"我想听长发公主的故事","intentId":"PLAY","intentName":"播放","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"我想听长发公主的故事","skill":"有声节目","skillId":"IFLYTEK.audioProgram","skillVersion":"187.0","semantics":{"request":{"slots":[{"name":"category","value":"故事"},{"name":"name","value":"长发公主"}]}}},"timestamp":1741597455}
发送结束标识
发送断开连接标识
send data finished:1741597455.7073612
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid7a5c58f3@dx6c1b1b29f70f3eef00"}
连接正常关闭
1741597455
param:b'{\n            "auth_id": "c679bdbc379d497da133d0d287afcadd",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidb41e3d61@dx06e51b29f7103eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "8baf1881b32c4c27be93f702e26eeb23","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bafd9@dx1957f4c37b3b8a9532"}
started:
ws start
####################
测试进行: ctm000118c4@hu17b4fbfd54b020c902#46308945.pcm
{"recordId":"ase000f2dcc@hu1957f4c4f4505c0882:8baf1881b32c4c27be93f702e26eeb23","requestId":"ase000f2dcc@hu1957f4c4f4505c0882","sessionId":"cid000bafd9@dx1957f4c37b3b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597462}
{"recordId":"gty000bafda@dx1957f4c37eab8a9532:8baf1881b32c4c27be93f702e26eeb23","requestId":"gty000bafda@dx1957f4c37eab8a9532","sessionId":"cid000bafd9@dx1957f4c37b3b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597464}
{"recordId":"gty000bafda@dx1957f4c37eab8a9532:8baf1881b32c4c27be93f702e26eeb23","requestId":"gty000bafda@dx1957f4c37eab8a9532","sessionId":"cid000bafd9@dx1957f4c37b3b8a9532","eof":"1","text":"有没有调频90.8生活广播","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597464}
send data finished:1741597464.8326292
{"recordId":"gty000bafda@dx1957f4c37eab8a9532:8baf1881b32c4c27be93f702e26eeb23","requestId":"gty000bafda@dx1957f4c37eab8a9532","sessionId":"cid000bafd9@dx1957f4c37b3b8a9532","topic":"dm.output","skill":"网络电台","skillId":"2019031500001032","speakUrl":"","error":{},"dm":{"input":"有没有调频90.8生活广播","intentId":"LAUNCH","intentName":"打开","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"有没有调频90.8生活广播","skill":"网络电台","skillId":"2019031500001032","skillVersion":"44","semantics":{"request":{"slots":[{"name":"code","value":"90.8"},{"name":"nameOrig","value":"生活电台"},{"name":"waveband","value":"fm"},{"name":"category","value":"生活"}]}}},"timestamp":1741597464}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidaece6fe5@dxe1621b29f7183eef00"}
连接正常关闭
1741597464
param:b'{\n            "auth_id": "a93f60966d22478fb536bbbef4f25c06",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid4cd3c134@dx900e1b29f7193eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "f397bcc388024b1686610cd8ee448d9b","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb888@dx1957f4c5a94b86a532"}
started:
ws start
####################
测试进行: ctm000118cf@hu17b4fbfd9e3020c902#46308961.pcm
{"recordId":"ase000f3386@hu1957f4c71cd05c0882:f397bcc388024b1686610cd8ee448d9b","requestId":"ase000f3386@hu1957f4c71cd05c0882","sessionId":"cid000bb888@dx1957f4c5a94b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597471}
{"recordId":"ase000e95b3@hu1957f4c728505c3882:f397bcc388024b1686610cd8ee448d9b","requestId":"ase000e95b3@hu1957f4c728505c3882","sessionId":"cid000bb888@dx1957f4c5a94b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597471}
{"recordId":"gty000bb889@dx1957f4c5ac8b86a532:f397bcc388024b1686610cd8ee448d9b","requestId":"gty000bb889@dx1957f4c5ac8b86a532","sessionId":"cid000bb888@dx1957f4c5a94b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597472}
{"recordId":"gty000bb889@dx1957f4c5ac8b86a532:f397bcc388024b1686610cd8ee448d9b","requestId":"gty000bb889@dx1957f4c5ac8b86a532","sessionId":"cid000bb888@dx1957f4c5a94b86a532","eof":"1","text":"找一下三字经","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597472}
send data finished:1741597472.171964
{"recordId":"gty000bb889@dx1957f4c5ac8b86a532:f397bcc388024b1686610cd8ee448d9b","requestId":"gty000bb889@dx1957f4c5ac8b86a532","sessionId":"cid000bb888@dx1957f4c5a94b86a532","topic":"dm.output","skill":"国学","skillId":"AIUI.chLiterature","speakUrl":"","error":{},"dm":{"input":"找一下三字经","intentId":"QUERY_GROUP","intentName":"综合查询","nlg":"即将为你播放三字经 读史者","widget":{"content":[{"album":"嘟拉新三字经","language":"CN","tags":"嘟拉学堂,嘟拉新三字经","title":"三字经 读史者","subTitle":"","imageUrl":"","linkUrl":"https://cdn9003.iflyos.cn/audio/national/newsanzijing/854a54e025a6be728d9026ce60183d4e.mp3","id":"854a54e025a6be728d9026ce60183d4e","extra":{"source":"61ertong"}},{"album":"嘟拉三字经","language":"CN","tags":"嘟拉学堂,嘟拉三字经","title":"三字经 五子者","subTitle":"","imageUrl":"","linkUrl":"https://cdn9003.iflyos.cn/audio/national/sanzijing/ae1a0e641e412655c390702cfce8cd1f.mp3","id":"d28e48a4b4354b3112dcca42fca4c500","extra":{"source":"61ertong"}},{"album":"嘟拉三字经","language":"CN","tags":"嘟拉学堂,嘟拉三字经","title":"三字经 首孝悌","subTitle":"","imageUrl":"","linkUrl":"https://cdn9003.iflyos.cn/audio/national/sanzijing/d42f3852819565e1aea75845ec83ded2.mp3","id":"5e8362be5ed8ec0d4ae402aa00260a42","extra":{"source":"61ertong"}},{"album":"嘟拉三字经","language":"CN","tags":"嘟拉学堂,嘟拉三字经","title":"三字经 莹八岁","subTitle":"","imageUrl":"","linkUrl":"https://cdn9003.iflyos.cn/audio/national/sanzijing/32059737fe686efe698566a3eef88a4a.mp3","id":"12b679e7133ef61ea43dffeefbf7027f","extra":{"source":"61ertong"}},{"album":"嘟拉新三字经","language":"CN","tags":"嘟拉学堂,嘟拉新三字经","title":"三字经 夏有禹","subTitle":"","imageUrl":"","linkUrl":"https://cdn9003.iflyos.cn/audio/national/newsanzijing/ef9fb54ced55844b3ad65073b27ce83b.mp3","id":"ef9fb54ced55844b3ad65073b27ce83b","extra":{"source":"61ertong"}},{"album":"嘟拉三字经","language":"CN","tags":"嘟拉学堂,嘟拉三字经","title":"三字经 曰喜怒","subTitle":"","imageUrl":"","linkUrl":"https://cdn9003.iflyos.cn/audio/national/sanzijing/c5f48c0d1a5bba5201d11b4161d14685.mp3","id":"6a0081879ee0a5d1ad2cb79362adfff8","extra":{"source":"61ertong"}},{"album":"嘟拉新三字经","language":"CN","tags":"嘟拉学堂,嘟拉新三字经","title":"三字经 凡训蒙","subTitle":"","imageUrl":"","linkUrl":"https://cdn9003.iflyos.cn/audio/national/newsanzijing/ff7c5f0d7a88ead99df110afbcbafe15.mp3","id":"ff7c5f0d7a88ead99df110afbcbafe15","extra":{"source":"61ertong"}},{"album":"嘟拉新三字经","language":"CN","tags":"嘟拉学堂,嘟拉新三字经","title":"三字经 地所生","subTitle":"","imageUrl":"","linkUrl":"https://cdn9003.iflyos.cn/audio/national/newsanzijing/0b0be0629cc9a0d99503a4f169d9397d.mp3","id":"0b0be0629cc9a0d99503a4f169d9397d","extra":{"source":"61ertong"}},{"album":"嘟拉新三字经","language":"CN","tags":"嘟拉学堂,嘟拉新三字经","title":"三字经 二十传","subTitle":"","imageUrl":"","linkUrl":"https://cdn9003.iflyos.cn/audio/national/newsanzijing/90b5247cef5b6f025f84e51d5cb524a3.mp3","id":"90b5247cef5b6f025f84e51d5cb524a3","extra":{"source":"61ertong"}},{"album":"嘟拉三字经","language":"CN","tags":"嘟拉学堂,嘟拉三字经","title":"三字经 炎宋兴","subTitle":"","imageUrl":"","linkUrl":"https://cdn9003.iflyos.cn/audio/national/sanzijing/35a9930d2aadb2fbb577f4eb498cd16a.mp3","id":"35a9930d2aadb2fbb577f4eb498cd16a","extra":{"source":"61ertong"}}]},"shouldEndSession":true},"nlu":{"input":"找一下三字经","skill":"国学","skillId":"AIUI.chLiterature","skillVersion":"2.0","semantics":{"request":{"entrypoint":"ent","hazard":false,"score":1,"slots":[{"begin":3,"end":6,"name":"name","normValue":"三字经","value":"三字经"}],"template":"{search}{name}"}}},"timestamp":1741597472}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid87354de3@dx50d31b29f7203eef00"}
连接正常关闭
1741597472
param:b'{\n            "auth_id": "e67188a87ba94aef8a9a0df6168fce8b",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidb4a37ee8@dxa2a61b29f7203eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "62bc37ee9273432c8701b03fb87b6eb5","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb9f6@dx1957f4c772f7844532"}
started:
ws start
####################
测试进行: ctm000118de@hu17b4fbfdea9020c902#46308980.pcm
{"recordId":"ase000d5c0a@hu1957f4c8dde0427882:62bc37ee9273432c8701b03fb87b6eb5","requestId":"ase000d5c0a@hu1957f4c8dde0427882","sessionId":"cid000bb9f6@dx1957f4c772f7844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597478}
{"recordId":"gty000bb9f7@dx1957f4c776a7844532:62bc37ee9273432c8701b03fb87b6eb5","requestId":"gty000bb9f7@dx1957f4c776a7844532","sessionId":"cid000bb9f6@dx1957f4c772f7844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597480}
{"recordId":"gty000bb9f7@dx1957f4c776a7844532:62bc37ee9273432c8701b03fb87b6eb5","requestId":"gty000bb9f7@dx1957f4c776a7844532","sessionId":"cid000bb9f6@dx1957f4c772f7844532","eof":"1","text":"我要把人民币兑换成美元","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597480}
send data finished:1741597480.1731386
{"recordId":"gty000bb9f7@dx1957f4c776a7844532:62bc37ee9273432c8701b03fb87b6eb5","requestId":"gty000bb9f7@dx1957f4c776a7844532","sessionId":"cid000bb9f6@dx1957f4c772f7844532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"我要把人民币兑换成美元","intentId":"chat","intentName":"闲聊","nlg":"看自己需求。","shouldEndSession":true},"nlu":{"input":"我要把人民币兑换成美元","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741597480}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidfe01940a@dxa3d61b29f7283eef00"}
连接正常关闭
1741597480
param:b'{\n            "auth_id": "e2cfc6e9ca424f94b5e79c66530eafc6",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cide86f97fa@dx32471b29f7283eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "a86c8bd86c4d4967b00ead8a5c66c7ca","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb002@dx1957f4c96fdb8a9532"}
started:
ws start
####################
测试进行: ctm0001190a@hu17ba70b828b0212902#47592803.pcm
{"recordId":"ase000f0bd7@hu1957f4ca8f605c4882:a86c8bd86c4d4967b00ead8a5c66c7ca","requestId":"ase000f0bd7@hu1957f4ca8f605c4882","sessionId":"cid000bb002@dx1957f4c96fdb8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597485}
{"recordId":"gty000bb003@dx1957f4c9730b8a9532:a86c8bd86c4d4967b00ead8a5c66c7ca","requestId":"gty000bb003@dx1957f4c9730b8a9532","sessionId":"cid000bb002@dx1957f4c96fdb8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597485}
{"recordId":"gty000bb003@dx1957f4c9730b8a9532:a86c8bd86c4d4967b00ead8a5c66c7ca","requestId":"gty000bb003@dx1957f4c9730b8a9532","sessionId":"cid000bb002@dx1957f4c96fdb8a9532","eof":"1","text":"音乐关了","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597485}
send data finished:1741597485.5397887
{"recordId":"gty000bb003@dx1957f4c9730b8a9532:a86c8bd86c4d4967b00ead8a5c66c7ca","requestId":"gty000bb003@dx1957f4c9730b8a9532","sessionId":"cid000bb002@dx1957f4c96fdb8a9532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"音乐关了","intentId":"INSTRUCTION","intentName":"音乐控制","nlg":"","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"音乐关了","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"操作","value":"close"}]}}},"timestamp":1741597485}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid47702774@dxdd161b29f72d3eef00"}
连接正常关闭
1741597485
param:b'{\n            "auth_id": "c76d39052f414368b25127e13f8ee760",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid94a9d9ab@dx7d061b29f72d3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "b15e86e09bb54599ba48965d42681db3","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb7aa@dx1957f4cab5eb8aa532"}
started:
ws start
####################
测试进行: ctm0001190e@hu17ba70b83df0212902#47592809.pcm
{"recordId":"ase000ecc49@hu1957f4cc07305bf882:b15e86e09bb54599ba48965d42681db3","requestId":"ase000ecc49@hu1957f4cc07305bf882","sessionId":"cid000bb7aa@dx1957f4cab5eb8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597491}
{"recordId":"gty000bb7ab@dx1957f4caba0b8aa532:b15e86e09bb54599ba48965d42681db3","requestId":"gty000bb7ab@dx1957f4caba0b8aa532","sessionId":"cid000bb7aa@dx1957f4cab5eb8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597493}
{"recordId":"gty000bb7ab@dx1957f4caba0b8aa532:b15e86e09bb54599ba48965d42681db3","requestId":"gty000bb7ab@dx1957f4caba0b8aa532","sessionId":"cid000bb7aa@dx1957f4cab5eb8aa532","eof":"1","text":"收藏现在放的这首歌","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597493}
send data finished:1741597493.1508133
{"recordId":"gty000bb7ab@dx1957f4caba0b8aa532:b15e86e09bb54599ba48965d42681db3","requestId":"gty000bb7ab@dx1957f4caba0b8aa532","sessionId":"cid000bb7aa@dx1957f4cab5eb8aa532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"收藏现在放的这首歌","intentId":"chat","intentName":"闲聊","nlg":"等我学习一下，再来回答吧！现在不如问点其他的吧。","shouldEndSession":true},"nlu":{"input":"收藏现在放的这首歌","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741597493}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid1b01b2b0@dxbc1c1b29f7353eef00"}
连接正常关闭
1741597493
param:b'{\n            "auth_id": "78a668f6a2064d3c83d7a8d6721e79cd",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid6dc34028@dx63261b29f7353eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "99bf2943cac14f23b5db588739ad6cc1","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bba16@dx1957f4cc9a57844532"}
started:
ws start
####################
测试进行: ctm00011911@hu17ba70b88b50212902#47592812.pcm
{"recordId":"ase000df76f@hu1957f4cddf504d3882:99bf2943cac14f23b5db588739ad6cc1","requestId":"ase000df76f@hu1957f4cddf504d3882","sessionId":"cid000bba16@dx1957f4cc9a57844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597499}
{"recordId":"gty000bba17@dx1957f4cc9dc7844532:99bf2943cac14f23b5db588739ad6cc1","requestId":"gty000bba17@dx1957f4cc9dc7844532","sessionId":"cid000bba16@dx1957f4cc9a57844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597499}
{"recordId":"gty000bba17@dx1957f4cc9dc7844532:99bf2943cac14f23b5db588739ad6cc1","requestId":"gty000bba17@dx1957f4cc9dc7844532","sessionId":"cid000bba16@dx1957f4cc9a57844532","eof":"1","text":"播放冯巩的相声","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597499}
send data finished:1741597499.8582428
{"recordId":"gty000bba17@dx1957f4cc9dc7844532:99bf2943cac14f23b5db588739ad6cc1","requestId":"gty000bba17@dx1957f4cc9dc7844532","sessionId":"cid000bba16@dx1957f4cc9a57844532","topic":"dm.output","skill":"有声节目","skillId":"IFLYTEK.audioProgram","speakUrl":"","error":{},"dm":{"input":"播放冯巩的相声","intentId":"PLAY","intentName":"播放","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"播放冯巩的相声","skill":"有声节目","skillId":"IFLYTEK.audioProgram","skillVersion":"187.0","semantics":{"request":{"slots":[{"name":"actor","value":"冯巩"},{"name":"category","value":"相声"}]}}},"timestamp":1741597499}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid6d8d18b2@dx174d1b29f73b3eef00"}
连接正常关闭
1741597499
param:b'{\n            "auth_id": "2ca63e01d2fd43029131229e5134ef56",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"ciddb851ba3@dx48bf1b29f73c3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "2192c07516ab4175a2bfb0fa5a1e51df","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb015@dx1957f4ce37cb8a9532"}
started:
ws start
####################
测试进行: ctm00011914@hu17ba70b8e090212902#47592826.pcm
{"recordId":"ase000ed4ac@hu1957f4cf68f05bf882:2192c07516ab4175a2bfb0fa5a1e51df","requestId":"ase000ed4ac@hu1957f4cf68f05bf882","sessionId":"cid000bb015@dx1957f4ce37cb8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597505}
{"recordId":"gty000bb016@dx1957f4ce3afb8a9532:2192c07516ab4175a2bfb0fa5a1e51df","requestId":"gty000bb016@dx1957f4ce3afb8a9532","sessionId":"cid000bb015@dx1957f4ce37cb8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597506}
{"recordId":"gty000bb016@dx1957f4ce3afb8a9532:2192c07516ab4175a2bfb0fa5a1e51df","requestId":"gty000bb016@dx1957f4ce3afb8a9532","sessionId":"cid000bb015@dx1957f4ce37cb8a9532","eof":"1","text":"故事讲个故事吧","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597506}
send data finished:1741597506.15399
{"recordId":"gty000bb016@dx1957f4ce3afb8a9532:2192c07516ab4175a2bfb0fa5a1e51df","requestId":"gty000bb016@dx1957f4ce3afb8a9532","sessionId":"cid000bb015@dx1957f4ce37cb8a9532","topic":"dm.output","skill":"有声节目","skillId":"IFLYTEK.audioProgram","speakUrl":"","error":{},"dm":{"input":"故事讲个故事吧","intentId":"PLAY","intentName":"播放","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"故事讲个故事吧","skill":"有声节目","skillId":"IFLYTEK.audioProgram","skillVersion":"187.0","semantics":{"request":{"slots":[{"name":"category","value":"故事"}]}}},"timestamp":1741597506}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidd7b1ee0a@dxb19a1b29f7423eef00"}
连接正常关闭
1741597506
param:b'{\n            "auth_id": "dc7d7ad873034d39a4d935027170b267",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid0ded00eb@dx107e1b29f7423eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "a8332e6f44b049aebce35b2011b48f34","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb7bb@dx1957f4cfbe5b8aa532"}
started:
ws start
####################
测试进行: ctm00011919@hu17ba70b8fa30212902#47592831.pcm
{"recordId":"ase000ed7fa@hu1957f4d0deb05bf882:a8332e6f44b049aebce35b2011b48f34","requestId":"ase000ed7fa@hu1957f4d0deb05bf882","sessionId":"cid000bb7bb@dx1957f4cfbe5b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597511}
{"recordId":"gty000bb7bc@dx1957f4cfc18b8aa532:a8332e6f44b049aebce35b2011b48f34","requestId":"gty000bb7bc@dx1957f4cfc18b8aa532","sessionId":"cid000bb7bb@dx1957f4cfbe5b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597512}
{"recordId":"gty000bb7bc@dx1957f4cfc18b8aa532:a8332e6f44b049aebce35b2011b48f34","requestId":"gty000bb7bc@dx1957f4cfc18b8aa532","sessionId":"cid000bb7bb@dx1957f4cfbe5b8aa532","eof":"1","text":"请你给我讲个故事好吗","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597512}
send data finished:1741597512.7907329
{"recordId":"gty000bb7bc@dx1957f4cfc18b8aa532:a8332e6f44b049aebce35b2011b48f34","requestId":"gty000bb7bc@dx1957f4cfc18b8aa532","sessionId":"cid000bb7bb@dx1957f4cfbe5b8aa532","topic":"dm.output","skill":"有声节目","skillId":"IFLYTEK.audioProgram","speakUrl":"","error":{},"dm":{"input":"请你给我讲个故事好吗","intentId":"PLAY","intentName":"播放","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"请你给我讲个故事好吗","skill":"有声节目","skillId":"IFLYTEK.audioProgram","skillVersion":"187.0","semantics":{"request":{"slots":[{"name":"category","value":"故事"}]}}},"timestamp":1741597512}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid653f8be1@dx9b4d1b29f7483eef00"}
连接正常关闭
1741597512
param:b'{\n            "auth_id": "eddf99c2d98440f3801e4c155fa61b4d",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid08139500@dx22851b29f7493eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "2f25b151d13d4610913105be54689692","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb7c4@dx1957f4d15deb8aa532"}
started:
ws start
####################
测试进行: ctm0001191d@hu17ba70b91a70212902#47592834.pcm
{"recordId":"ase000eeb78@hu1957f4d28391323882:2f25b151d13d4610913105be54689692","requestId":"ase000eeb78@hu1957f4d28391323882","sessionId":"cid000bb7c4@dx1957f4d15deb8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597517}
{"recordId":"gty000bb7c5@dx1957f4d1618b8aa532:2f25b151d13d4610913105be54689692","requestId":"gty000bb7c5@dx1957f4d1618b8aa532","sessionId":"cid000bb7c4@dx1957f4d15deb8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597518}
{"recordId":"gty000bb7c5@dx1957f4d1618b8aa532:2f25b151d13d4610913105be54689692","requestId":"gty000bb7c5@dx1957f4d1618b8aa532","sessionId":"cid000bb7c4@dx1957f4d15deb8aa532","eof":"1","text":"我想听相声","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597518}
send data finished:1741597518.4631603
{"recordId":"gty000bb7c5@dx1957f4d1618b8aa532:2f25b151d13d4610913105be54689692","requestId":"gty000bb7c5@dx1957f4d1618b8aa532","sessionId":"cid000bb7c4@dx1957f4d15deb8aa532","topic":"dm.output","skill":"有声节目","skillId":"IFLYTEK.audioProgram","speakUrl":"","error":{},"dm":{"input":"我想听相声","intentId":"PLAY","intentName":"播放","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"我想听相声","skill":"有声节目","skillId":"IFLYTEK.audioProgram","skillVersion":"187.0","semantics":{"request":{"slots":[{"name":"category","value":"相声"}]}}},"timestamp":1741597518}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidae6a3c4d@dx706f1b29f74e3eef00"}
连接正常关闭
1741597518
param:b'{\n            "auth_id": "e64f387c1d8a4952ba3ea90ff1668cbc",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid6f4ad2a5@dxc2111b29f74e3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "c14940daf6b04d0eb3b6cea08ac4c351","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bba28@dx1957f4d2c087844532"}
started:
ws start
####################
测试进行: ctm0001191f@hu17ba70b93b50212902#47592838.pcm
{"recordId":"ase000d776b@hu1957f4d3df60427882:c14940daf6b04d0eb3b6cea08ac4c351","requestId":"ase000d776b@hu1957f4d3df60427882","sessionId":"cid000bba28@dx1957f4d2c087844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597523}
{"recordId":"gty000bba29@dx1957f4d2c3d7844532:c14940daf6b04d0eb3b6cea08ac4c351","requestId":"gty000bba29@dx1957f4d2c3d7844532","sessionId":"cid000bba28@dx1957f4d2c087844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597523}
{"recordId":"gty000bba29@dx1957f4d2c3d7844532:c14940daf6b04d0eb3b6cea08ac4c351","requestId":"gty000bba29@dx1957f4d2c3d7844532","sessionId":"cid000bba28@dx1957f4d2c087844532","eof":"1","text":"我要听相声","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597524}
send data finished:1741597524.096878
{"recordId":"gty000bba29@dx1957f4d2c3d7844532:c14940daf6b04d0eb3b6cea08ac4c351","requestId":"gty000bba29@dx1957f4d2c3d7844532","sessionId":"cid000bba28@dx1957f4d2c087844532","topic":"dm.output","skill":"有声节目","skillId":"IFLYTEK.audioProgram","speakUrl":"","error":{},"dm":{"input":"我要听相声","intentId":"PLAY","intentName":"播放","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"我要听相声","skill":"有声节目","skillId":"IFLYTEK.audioProgram","skillVersion":"187.0","semantics":{"request":{"slots":[{"name":"category","value":"相声"}]}}},"timestamp":1741597524}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid0cf70cf1@dxee001b29f7543eef00"}
连接正常关闭
1741597524
param:b'{\n            "auth_id": "2be3ef1e2a5543df88644080791dbce4",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid9b7511e8@dx33b91b29f7543eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "2bbf6146f7f6431594cc9932babe78d3","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bba31@dx1957f4d42007844532"}
started:
ws start
####################
测试进行: ctm00011923@hu17ba70b96310212902#47592841.pcm
{"recordId":"ase000f662b@hu1957f4d549405c2882:2bbf6146f7f6431594cc9932babe78d3","requestId":"ase000f662b@hu1957f4d549405c2882","sessionId":"cid000bba31@dx1957f4d42007844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597529}
{"recordId":"gty000bba32@dx1957f4d42337844532:2bbf6146f7f6431594cc9932babe78d3","requestId":"gty000bba32@dx1957f4d42337844532","sessionId":"cid000bba31@dx1957f4d42007844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597530}
{"recordId":"gty000bba32@dx1957f4d42337844532:2bbf6146f7f6431594cc9932babe78d3","requestId":"gty000bba32@dx1957f4d42337844532","sessionId":"cid000bba31@dx1957f4d42007844532","eof":"1","text":"看小说共进午餐","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597530}
send data finished:1741597530.5427072
{"recordId":"gty000bba32@dx1957f4d42337844532:2bbf6146f7f6431594cc9932babe78d3","requestId":"gty000bba32@dx1957f4d42337844532","sessionId":"cid000bba31@dx1957f4d42007844532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"看小说共进午餐","intentId":"chat","intentName":"闲聊","nlg":"机器人也不是万能的噢，这个我也不知道。","shouldEndSession":true},"nlu":{"input":"看小说共进午餐","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741597530}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid41a8c362@dxbff41b29f75a3eef00"}
连接正常关闭
1741597530
param:b'{\n            "auth_id": "db6b1a803a424bba897414cc40797224",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid5ed98bda@dxfc5d1b29f75a3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "762866309fa448988fcf023fa13cfb09","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bba39@dx1957f4d5b927844532"}
started:
ws start
####################
测试进行: ctm00011927@hu17ba70b96f40212902#47592845.pcm
{"recordId":"ase000ebcf8@hu1957f4d6def05c3882:762866309fa448988fcf023fa13cfb09","requestId":"ase000ebcf8@hu1957f4d6def05c3882","sessionId":"cid000bba39@dx1957f4d5b927844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597535}
{"recordId":"gty000bba3a@dx1957f4d5bcf7844532:762866309fa448988fcf023fa13cfb09","requestId":"gty000bba3a@dx1957f4d5bcf7844532","sessionId":"cid000bba39@dx1957f4d5b927844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597539}
{"recordId":"gty000bba3a@dx1957f4d5bcf7844532:762866309fa448988fcf023fa13cfb09","requestId":"gty000bba3a@dx1957f4d5bcf7844532","sessionId":"cid000bba39@dx1957f4d5b927844532","eof":"1","text":"请查一下郑一群的小说马云的互联网思维","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597539}
send data finished:1741597539.7620091
{"recordId":"gty000bba3a@dx1957f4d5bcf7844532:762866309fa448988fcf023fa13cfb09","requestId":"gty000bba3a@dx1957f4d5bcf7844532","sessionId":"cid000bba39@dx1957f4d5b927844532","topic":"dm.output","skill":"有声节目","skillId":"IFLYTEK.audioProgram","speakUrl":"","error":{},"dm":{"input":"请查一下郑一群的小说马云的互联网思维","intentId":"PLAY","intentName":"播放","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"请查一下郑一群的小说马云的互联网思维","skill":"有声节目","skillId":"IFLYTEK.audioProgram","skillVersion":"187.0","semantics":{"request":{"slots":[{"name":"author","value":"郑一群"},{"name":"category","value":"小说"},{"name":"name","value":"马云的互联网思维"}]}}},"timestamp":1741597539}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid0b37dfb3@dx73421b29f7633eef00"}
连接正常关闭
1741597539
param:b'{\n            "auth_id": "bed8283c840d4490ba5fae20e5895b85",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid95bc124a@dxac271b29f7643eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "dc384d7b17ce402b942314a1106b392b","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb7da@dx1957f4d7f39b8aa532"}
started:
ws start
####################
测试进行: ctm00011929@hu17ba70b99510212902#47592848.pcm
{"recordId":"ase000f60ab@hu1957f4d907005c0882:dc384d7b17ce402b942314a1106b392b","requestId":"ase000f60ab@hu1957f4d907005c0882","sessionId":"cid000bb7da@dx1957f4d7f39b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597544}
{"recordId":"gty000bb7db@dx1957f4d7f6fb8aa532:dc384d7b17ce402b942314a1106b392b","requestId":"gty000bb7db@dx1957f4d7f6fb8aa532","sessionId":"cid000bb7da@dx1957f4d7f39b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597547}
{"recordId":"gty000bb7db@dx1957f4d7f6fb8aa532:dc384d7b17ce402b942314a1106b392b","requestId":"gty000bb7db@dx1957f4d7f6fb8aa532","sessionId":"cid000bb7da@dx1957f4d7f39b8aa532","eof":"1","text":"直接查下共进午餐这本小说","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597547}
send data finished:1741597547.2200413
{"recordId":"gty000bb7db@dx1957f4d7f6fb8aa532:dc384d7b17ce402b942314a1106b392b","requestId":"gty000bb7db@dx1957f4d7f6fb8aa532","sessionId":"cid000bb7da@dx1957f4d7f39b8aa532","topic":"dm.output","skill":"有声节目","skillId":"IFLYTEK.audioProgram","speakUrl":"","error":{},"dm":{"input":"直接查下共进午餐这本小说","intentId":"PLAY","intentName":"播放","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"直接查下共进午餐这本小说","skill":"有声节目","skillId":"IFLYTEK.audioProgram","skillVersion":"187.0","semantics":{"request":{"slots":[{"name":"name","value":"共进午餐"},{"name":"category","value":"小说"}]}}},"timestamp":1741597547}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidd8531ca1@dx8c741b29f76b3eef00"}
连接正常关闭
1741597547
param:b'{\n            "auth_id": "77047e8ef3224bb6a1712218d0dab8d4",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid8a82b7a3@dx21d61b29f76b3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "6369e64cc6d84d1fa86738d0ea4600de","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb8cf@dx1957f4d9c60b86a532"}
started:
ws start
####################
测试进行: ctm0001192b@hu17ba70b99bb0212902#47592850.pcm
{"recordId":"ase000e01af@hu1957f4db0891323882:6369e64cc6d84d1fa86738d0ea4600de","requestId":"ase000e01af@hu1957f4db0891323882","sessionId":"cid000bb8cf@dx1957f4d9c60b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597552}
{"recordId":"gty000bb8d0@dx1957f4d9c97b86a532:6369e64cc6d84d1fa86738d0ea4600de","requestId":"gty000bb8d0@dx1957f4d9c97b86a532","sessionId":"cid000bb8cf@dx1957f4d9c60b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597554}
{"recordId":"gty000bb8d0@dx1957f4d9c97b86a532:6369e64cc6d84d1fa86738d0ea4600de","requestId":"gty000bb8d0@dx1957f4d9c97b86a532","sessionId":"cid000bb8cf@dx1957f4d9c60b86a532","eof":"1","text":"尽管可是怎么造句","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597554}
send data finished:1741597554.278987
{"recordId":"gty000bb8d0@dx1957f4d9c97b86a532:6369e64cc6d84d1fa86738d0ea4600de","requestId":"gty000bb8d0@dx1957f4d9c97b86a532","sessionId":"cid000bb8cf@dx1957f4d9c60b86a532","topic":"dm.output","skill":"词典","skillId":"wordsDictionary","speakUrl":"","error":{},"dm":{"input":"尽管可是怎么造句","intentId":"SENTENCE_QUERY","intentName":"查询词语如何造句","nlg":"尽管可是你可以这么用：尽管我非常努力地学习，可是成绩还是不理想。","shouldEndSession":true},"nlu":{"input":"尽管可是怎么造句","skill":"词典","skillId":"wordsDictionary","skillVersion":"196.0","semantics":{"request":{"slots":[{"name":"related","value":"尽管可是"}]}}},"timestamp":1741597554}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidee8919cd@dx0d741b29f7723eef00"}
连接正常关闭
1741597554
param:b'{\n            "auth_id": "34ec60bc3e86437e8bb268ac665a33f9",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid182f8bf9@dxe9b11b29f7723eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "4a523e2294d94ddbbc93538a1be330c7","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb7ea@dx1957f4db7ddb8aa532"}
started:
ws start
####################
测试进行: ctm00011cc9@hu17b4cb4fc0f0212902#46275220.pcm
{"recordId":"ase000d1cdb@hu1957f4dc8e004d3882:4a523e2294d94ddbbc93538a1be330c7","requestId":"ase000d1cdb@hu1957f4dc8e004d3882","sessionId":"cid000bb7ea@dx1957f4db7ddb8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597559}
{"recordId":"gty000bb7eb@dx1957f4db815b8aa532:4a523e2294d94ddbbc93538a1be330c7","requestId":"gty000bb7eb@dx1957f4db815b8aa532","sessionId":"cid000bb7ea@dx1957f4db7ddb8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597560}
{"recordId":"gty000bb7eb@dx1957f4db815b8aa532:4a523e2294d94ddbbc93538a1be330c7","requestId":"gty000bb7eb@dx1957f4db815b8aa532","sessionId":"cid000bb7ea@dx1957f4db7ddb8aa532","eof":"1","text":"看看佛山的百科","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597560}
send data finished:1741597560.2864752
{"recordId":"gty000bb7eb@dx1957f4db815b8aa532:4a523e2294d94ddbbc93538a1be330c7","requestId":"gty000bb7eb@dx1957f4db815b8aa532","sessionId":"cid000bb7ea@dx1957f4db7ddb8aa532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"看看佛山的百科","intentId":"chat","intentName":"闲聊","nlg":"佛山，广东省省辖市，中国重要的制造业基地，国家历史文化名城，珠三角地区西翼经贸中心和综合交通枢纽。","shouldEndSession":true},"nlu":{"input":"看看佛山的百科","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741597560}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid3c33cd54@dx151a1b29f7783eef00"}
连接正常关闭
1741597560
param:b'{\n            "auth_id": "87be8ebec0164452b36e671404d9f5ee",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid13a51b97@dx3e741b29f7783eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "6a106c9b80b24b0ba67d825de64730fc","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb04b@dx1957f4dcff2b8a9532"}
started:
ws start
####################
测试进行: ctm00011cd1@hu17b4cb5006c0212902#46275231.pcm
{"recordId":"ase000d20d9@hu1957f4de06604d3882:6a106c9b80b24b0ba67d825de64730fc","requestId":"ase000d20d9@hu1957f4de06604d3882","sessionId":"cid000bb04b@dx1957f4dcff2b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597565}
{"recordId":"gty000bb04c@dx1957f4dd029b8a9532:6a106c9b80b24b0ba67d825de64730fc","requestId":"gty000bb04c@dx1957f4dd029b8a9532","sessionId":"cid000bb04b@dx1957f4dcff2b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597566}
{"recordId":"gty000bb04c@dx1957f4dd029b8a9532:6a106c9b80b24b0ba67d825de64730fc","requestId":"gty000bb04c@dx1957f4dd029b8a9532","sessionId":"cid000bb04b@dx1957f4dcff2b8a9532","eof":"1","text":"39度算高烧吗","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597566}
send data finished:1741597566.4902487
{"recordId":"gty000bb04c@dx1957f4dd029b8a9532:6a106c9b80b24b0ba67d825de64730fc","requestId":"gty000bb04c@dx1957f4dd029b8a9532","sessionId":"cid000bb04b@dx1957f4dcff2b8a9532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"39度算高烧吗","intentId":"chat","intentName":"闲聊","nlg":"虽然不想让你失望，但这个问题我真的不会。","shouldEndSession":true},"nlu":{"input":"39度算高烧吗","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741597566}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidba6855a4@dx911d1b29f77e3eef00"}
连接正常关闭
1741597566
param:b'{\n            "auth_id": "0ccc1f2260cd4909b5c67aee9e60eac9",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidbcb97149@dx9acd1b29f77e3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "4bebff0b06ff4348b3bb0f13981682db","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bba59@dx1957f4de8387844532"}
started:
ws start
####################
测试进行: ctm00011ce1@hu17b4cb506bb0212902#46275245.pcm
{"recordId":"ase000f410e@hu1957f4df90b05c4882:4bebff0b06ff4348b3bb0f13981682db","requestId":"ase000f410e@hu1957f4df90b05c4882","sessionId":"cid000bba59@dx1957f4de8387844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597571}
{"recordId":"gty000bba5a@dx1957f4de8787844532:4bebff0b06ff4348b3bb0f13981682db","requestId":"gty000bba5a@dx1957f4de8787844532","sessionId":"cid000bba59@dx1957f4de8387844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597572}
{"recordId":"gty000bba5a@dx1957f4de8787844532:4bebff0b06ff4348b3bb0f13981682db","requestId":"gty000bba5a@dx1957f4de8787844532","sessionId":"cid000bba59@dx1957f4de8387844532","eof":"1","text":"蜜蜂是怎么叫的呀","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597572}
send data finished:1741597572.5471382
{"recordId":"gty000bba5a@dx1957f4de8787844532:4bebff0b06ff4348b3bb0f13981682db","requestId":"gty000bba5a@dx1957f4de8787844532","sessionId":"cid000bba59@dx1957f4de8387844532","topic":"dm.output","skill":"动物叫声","skillId":"IFLYTEK.animalCries","speakUrl":"","error":{},"dm":{"input":"蜜蜂是怎么叫的呀","intentId":"PLAY","intentName":"播放","nlg":"它是这样叫的","widget":{"content":[{"type":"蜜蜂,蜂","title":"蜜蜂","tags":"","linkUrl":"http://aiui.storage.iflyresearch.com/ctimusic/animalCries/a1babcd5478d9678e07119b140ad77bdmifeng.mp3","extra":{"source":"iflytek"}}]},"shouldEndSession":true},"nlu":{"input":"蜜蜂是怎么叫的呀","skill":"动物叫声","skillId":"IFLYTEK.animalCries","skillVersion":"7.0","semantics":{"request":{"slots":[{"name":"name","value":"蜜蜂"}]}}},"timestamp":1741597572}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidece1336c@dx598a1b29f7843eef00"}
连接正常关闭
1741597572
param:b'{\n            "auth_id": "70a1b454b9b04d2096401078f6014051",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid9d48eed5@dx59e11b29f7843eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "3821238a5e1049b581ace35f6297c422","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb8e9@dx1957f4dff81b86a532"}
started:
ws start
####################
测试进行: ctm00011eac@hu17ba9ed6f10020c902#47643167.pcm
{"recordId":"gty000bb8ea@dx1957f4dffb8b86a532:3821238a5e1049b581ace35f6297c422","requestId":"gty000bb8ea@dx1957f4dffb8b86a532","sessionId":"cid000bb8e9@dx1957f4dff81b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597577}
{"recordId":"gty000bb8ea@dx1957f4dffb8b86a532:3821238a5e1049b581ace35f6297c422","requestId":"gty000bb8ea@dx1957f4dffb8b86a532","sessionId":"cid000bb8e9@dx1957f4dff81b86a532","eof":"1","text":"播新闻","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597577}
send data finished:1741597577.8732817
{"recordId":"gty000bb8ea@dx1957f4dffb8b86a532:3821238a5e1049b581ace35f6297c422","requestId":"gty000bb8ea@dx1957f4dffb8b86a532","sessionId":"cid000bb8e9@dx1957f4dff81b86a532","topic":"dm.output","skill":"新闻","skillId":"2019031900001180","speakUrl":"","error":{},"dm":{"input":"播新闻","intentId":"PLAY","intentName":"听新闻","nlg":"新闻报道：3月10日人本生活资讯晚报：全国政协委员张凯丽：建议大力打击谣言黑产；哪吒2英配版将发行院线","widget":{"content":[{"album":"新闻","title":"3月10日人本生活资讯晚报：全国政协委员张凯丽：建议大力打击谣言黑产；哪吒2英配版将发行院线","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202503101610A6F32914097BFB52019E023671AEF9BF_64.mp3?pf=OH9GI&vid=11723586&tm=1741597578045&pid=1898262","extra":{"source":""}},{"album":"新闻","title":"减肥4年后绝大多数人恢复之前体重","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2025031015223F38235525E04BCD655F1A1FC53E5265_64.mp3?pf=OH9GI&vid=11723578&tm=1741597578045&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"人生最容易长胖的5个时刻","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202503101518265A9818823909625DF2F29B090E4595_64.mp3?pf=OH9GI&vid=11723563&tm=1741597578045&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"蹲便冲水细菌是坐便的2倍","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2025031015130EA1D3CDD6B6C8FFED07928B1EF5D96C_64.mp3?pf=OH9GI&vid=11723562&tm=1741597578045&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"代表建议加大医美行业监管力度﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202503101455893E4D847DE5CC6E99894AAF5C6287C6_64.mp3?pf=OH9GI&vid=11723554&tm=1741597578045&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"青少年需要身体能出汗的体育课﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2025031014558DC7F45429AA8B9EEFB93689A0B4901E_64.mp3?pf=OH9GI&vid=11723548&tm=1741597578045&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"防腐剂脱氢乙酸钠全面禁用是谣言﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202503101455491F9B10CADDCD42524F8ED3283E9F7C_64.mp3?pf=OH9GI&vid=11723547&tm=1741597578045&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"《纽约时报》急问为什么小米可以造电动车，苹果却不能？","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202503101437E72886650ABC5F2085FD1CC6EFAA7474_64.mp3?pf=OH9GI&vid=11723538&tm=1741597578045&pid=194144","extra":{"source":"fm"}},{"album":"新闻","title":"养老服务向全体老年人拓展﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2025031012542B3AF8EDDF8118971866627D50AA16AF_64.mp3?pf=OH9GI&vid=11723480&tm=1741597578045&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"加拿大称将继续对美征收报复性关税﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/20250310125416F7FCBFA3CCE31C40BE0A772F4D0E45_64.mp3?pf=OH9GI&vid=11723476&tm=1741597578045&pid=1897566","extra":{"source":"fm"}}]},"shouldEndSession":true},"nlu":{"input":"播新闻","skill":"新闻","skillId":"2019031900001180","skillVersion":"87","semantics":{"request":{"slots":[]}}},"timestamp":1741597578}
{"recordId":"ase000f75d2@hu1957f4e133a05c0882:3821238a5e1049b581ace35f6297c422","requestId":"ase000f75d2@hu1957f4e133a05c0882","sessionId":"cid000bb8e9@dx1957f4dff81b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597578}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid13abea7c@dx02441b29f78a3eef00"}
连接正常关闭
1741597578
param:b'{\n            "auth_id": "531fa89cd1dd4b86b951756ef32a36c6",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid932030c7@dxac4f1b29f78a3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "cfaf8daf7bd749b69e207720bcf1c15c","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bba66@dx1957f4e15947844532"}
started:
ws start
####################
测试进行: ctm00011eae@hu17ba9ed6f2f020c902#47643170.pcm
{"recordId":"ase000e056b@hu1957f4e26ff05bf882:cfaf8daf7bd749b69e207720bcf1c15c","requestId":"ase000e056b@hu1957f4e26ff05bf882","sessionId":"cid000bba66@dx1957f4e15947844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597583}
{"recordId":"gty000bba67@dx1957f4e15cd7844532:cfaf8daf7bd749b69e207720bcf1c15c","requestId":"gty000bba67@dx1957f4e15cd7844532","sessionId":"cid000bba66@dx1957f4e15947844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597583}
{"recordId":"gty000bba67@dx1957f4e15cd7844532:cfaf8daf7bd749b69e207720bcf1c15c","requestId":"gty000bba67@dx1957f4e15cd7844532","sessionId":"cid000bba66@dx1957f4e15947844532","eof":"1","text":"随便来个笑话","pinyin":"","topic":"asr.speech.result","age":"elder","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597583}
send data finished:1741597584.0273032
{"recordId":"gty000bba67@dx1957f4e15cd7844532:cfaf8daf7bd749b69e207720bcf1c15c","requestId":"gty000bba67@dx1957f4e15cd7844532","sessionId":"cid000bba66@dx1957f4e15947844532","topic":"dm.output","skill":"旧版笑话","skillId":"2019032500000002","speakUrl":"","error":{},"dm":{"input":"随便来个笑话","intentId":"QUERY","intentName":"笑话点播","nlg":"请听笑话【胖子的声音太吓人】，见不远处的草地上睡着一位胖子，蚊子妈妈便叫小蚊子试着去吸他的血，这是小蚊子第一次去吸人的血，结果小蚊子饿着肚子回来了。妈妈问他是什么原因，他回答说：那个胖子那里发出来的声音太吓人了。妈妈问道：什么声音？小蚊子回答道：我还没飞到他那里，就听到有人说：快使用双节棍，哼哼哈嘿。","widget":{"content":[{"source":"","title":"胖子的声音太吓人","text":"见不远处的草地上睡着一位胖子，蚊子妈妈便叫小蚊子试着去吸他的血，这是小蚊子第一次去吸人的血，结果小蚊子饿着肚子回来了。妈妈问他是什么原因，他回答说：那个胖子那里发出来的声音太吓人了。妈妈问道：什么声音？小蚊子回答道：我还没飞到他那里，就听到有人说：快使用双节棍，哼哼哈嘿","author":"","linkUrl":"https://wsapi.xfyun.cn/midea/audio/joke/272662162a8c4ec3217cf4adcf78ccb9/52ab1149a1389b57bd78a388d68aca55.mp3?time=1741597584057","extra":{"resType":"mp3"}},{"source":"","title":"阿弥陀佛","text":"大师，你为什么老说阿弥陀佛？施主，我觉得一个出家人如果也用呵呵来表达情绪的话，这也太俗了。阿弥陀佛。","author":"","linkUrl":"https://wsapi.xfyun.cn/midea/audio/joke/9c6814c0123c97c672ae2e41d610536f/8d20f9b410f3b1a43d9e6c6a4ed178bd.mp3?time=1741597584058","extra":{"resType":"mp3"}},{"source":"","title":"找丈夫","text":"逛书店时，我听见一位女士问店员电脑书籍部在哪儿，店员领她到书店后面一个角落。问她：你有什么特别要找的？那位女士说：我找我丈夫。","author":"","linkUrl":"https://wsapi.xfyun.cn/midea/audio/joke/37b14385f0954f988658fbd7817edc8e/aaf052cb0682aa580e899e9e89fa1030.mp3?time=1741597584058","extra":{"resType":"mp3"}},{"source":"","title":"急于结婚的男人","text":"三个女人谈到一个急于结婚的男人。17岁的少女问：那男人是不是长得很帅？25岁的大姑娘问：那男人一个月挣多少钱？35岁的老剩女急着说：那男人现在在哪？","author":"","linkUrl":"https://wsapi.xfyun.cn/midea/audio/joke/f82278438f115cbf35c93d9cca97a1b4/d4a2c02597895091ac22cd35b4300137.mp3?time=1741597584058","extra":{"resType":"mp3"}},{"source":"","title":"我只想吃两个鸡腿","text":"爸爸说：你把这篇课文背完，我带你去吃烤全鸡。儿子说：那我背两段就可以了，我只想吃两个鸡腿。","author":"","linkUrl":"https://wsapi.xfyun.cn/midea/audio/joke/db85f21bf9397a5cb4643ac99462078c/6a59a0381b0d67b5638a635865fe2832.mp3?time=1741597584058","extra":{"resType":"mp3"}},{"source":"","title":"没那么多","text":"去银行取钱，对柜台业务员说：取１５００！业务员说：没那么多。我说：那你看看别的窗口有吗？业务员无奈的说了一句：是你卡里没那么多！","author":"","linkUrl":"https://wsapi.xfyun.cn/midea/audio/joke/f8603d01ac44e73da64350b8b3b4445f/24307d812f5e850751795329310b700e.mp3?time=1741597584059","extra":{"resType":"mp3"}},{"source":"","title":"因为你长得太难看了","text":"我在幼儿园工作，园里办了一个让小孩投票评选最喜欢的老师的活动，我是唯一一个得0票的，我私下问几个小孩为什么不给我投票，一个小孩回答：因为你长得太难看了。","author":"","linkUrl":"https://wsapi.xfyun.cn/midea/audio/joke/22238e8dd12eb67ed2a44803c5f9704f/08ac3ae774191fc1c9babde2943a8948.mp3?time=1741597584059","extra":{"resType":"mp3"}},{"source":"","title":"懂事的树木","text":"一天，维佳和乔治坐在树下乘凉。维佳抬头望着树上的叶子。维佳说：冬天为什么没有茂盛的叶子？乔治说：冬天人们需要温暖的阳光，如果树上长有茂盛的叶子，不是要给人们挡去了这温暖的阳光吗？维佳说：夏天树上为什么又长有茂盛的叶子？乔治说：道理正相反。夏天人们讨厌这炽热的阳光，树上长有叶子，能给人们挡住阳光。","author":"","linkUrl":"https://wsapi.xfyun.cn/midea/audio/joke/07c1c1e79af1c1e87045f5f673c48590/062748e4398a39e54fc437f209b8d0a4.mp3?time=1741597584059","extra":{"resType":"mp3"}},{"source":"","title":"谁付电话费","text":"杰克逊生气地把杂志一扔，说：我真想了解一下，都是谁在编造这些耻笑我们苏格兰人如何吝啬的笑话的。那好说，你给这家杂志编辑部打个电话问问就行了。旁边人给他出主意说。打电话？电话费谁给我付？杰克逊生气地说。","author":"","linkUrl":"https://wsapi.xfyun.cn/midea/audio/joke/e0b2bf11dbbd13528f5c203552911cda/51e642dda0d312990bbefceac57e464f.mp3?time=1741597584060","extra":{"resType":"mp3"}},{"source":"","title":"你是多喜欢宋丹丹啊？","text":"本来抱着大哭一场的念头去观摩将爱情进行到底，结果从头笑到尾。波尔多那一场戏，徐静蕾对李亚鹏说：带我走吧。李亚鹏答：大象怎么办？（大象是徐静蕾的儿子）。结果您猜怎么着？邻座一个观众很严肃的说：装冰箱呀！","author":"","linkUrl":"https://wsapi.xfyun.cn/midea/audio/joke/ee36b8e36b86a9987d54738546a66c3d/04bbfef5f1f683d06cfc9e9d969197a2.mp3?time=1741597584060","extra":{"resType":"mp3"}}]},"shouldEndSession":true},"nlu":{"input":"随便来个笑话","skill":"旧版笑话","skillId":"2019032500000002","skillVersion":"29","semantics":{"request":{"slots":[]}}},"timestamp":1741597584}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid76e4a32d@dxad841b29f7903eef00"}
连接正常关闭
1741597584
param:b'{\n            "auth_id": "88a2b108768a44d2aa9e177915f805c0",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid76959029@dxbf591b29f7903eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "d0653b769124447eb4d9b59bebec4dd8","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bba76@dx1957f4e2c497844532"}
started:
ws start
####################
测试进行: ctm00011eb0@hu17ba9ed6f4d020c902#47643172.pcm
{"recordId":"ase000f8b99@hu1957f4e3e2605c2882:d0653b769124447eb4d9b59bebec4dd8","requestId":"ase000f8b99@hu1957f4e3e2605c2882","sessionId":"cid000bba76@dx1957f4e2c497844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597589}
{"recordId":"gty000bba77@dx1957f4e2c807844532:d0653b769124447eb4d9b59bebec4dd8","requestId":"gty000bba77@dx1957f4e2c807844532","sessionId":"cid000bba76@dx1957f4e2c497844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597590}
{"recordId":"gty000bba77@dx1957f4e2c807844532:d0653b769124447eb4d9b59bebec4dd8","requestId":"gty000bba77@dx1957f4e2c807844532","sessionId":"cid000bba76@dx1957f4e2c497844532","eof":"1","text":"北京最近有什么动态","pinyin":"","topic":"asr.speech.result","age":"elder","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597590}
send data finished:1741597590.3187222
{"recordId":"gty000bba77@dx1957f4e2c807844532:d0653b769124447eb4d9b59bebec4dd8","requestId":"gty000bba77@dx1957f4e2c807844532","sessionId":"cid000bba76@dx1957f4e2c497844532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"北京最近有什么动态","intentId":"chat","intentName":"闲聊","nlg":"惭愧惭愧，我好像并不知道有什么。","shouldEndSession":true},"nlu":{"input":"北京最近有什么动态","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741597590}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid26d103c9@dx92de1b29f7963eef00"}
连接正常关闭
1741597590
param:b'{\n            "auth_id": "7c99227c6e3e4247abe90be37b83c91b",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidfb35f887@dx9ef21b29f7963eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "5ee57639c70f459a9ffb7d0e4502b5eb","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb069@dx1957f4e4640b8a9532"}
started:
ws start
####################
测试进行: ctm00011eba@hu17ba9ed777f020c902#47643178.pcm
{"recordId":"ase000f818a@hu1957f4e58fc05c0882:5ee57639c70f459a9ffb7d0e4502b5eb","requestId":"ase000f818a@hu1957f4e58fc05c0882","sessionId":"cid000bb069@dx1957f4e4640b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597596}
{"recordId":"gty000bb06a@dx1957f4e467bb8a9532:5ee57639c70f459a9ffb7d0e4502b5eb","requestId":"gty000bb06a@dx1957f4e467bb8a9532","sessionId":"cid000bb069@dx1957f4e4640b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597596}
{"recordId":"gty000bb06a@dx1957f4e467bb8a9532:5ee57639c70f459a9ffb7d0e4502b5eb","requestId":"gty000bb06a@dx1957f4e467bb8a9532","sessionId":"cid000bb069@dx1957f4e4640b8a9532","eof":"1","text":"今天有什么新闻","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597596}
send data finished:1741597596.89761
{"recordId":"gty000bb06a@dx1957f4e467bb8a9532:5ee57639c70f459a9ffb7d0e4502b5eb","requestId":"gty000bb06a@dx1957f4e467bb8a9532","sessionId":"cid000bb069@dx1957f4e4640b8a9532","topic":"dm.output","skill":"新闻","skillId":"2019031900001180","speakUrl":"","error":{},"dm":{"input":"今天有什么新闻","intentId":"PLAY","intentName":"听新闻","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"今天有什么新闻","skill":"新闻","skillId":"2019031900001180","skillVersion":"87","semantics":{"request":{"slots":[{"name":"datetime","normValue":"{\"datetime\":\"2025-03-10\",\"suggestDatetime\":\"2025-03-10\"}","value":"今天"},{"name":"keyword","value":"有什么"}]}}},"timestamp":1741597597}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cide2e527cd@dx8d4b1b29f79d3eef00"}
连接正常关闭
1741597597
param:b'{\n            "auth_id": "2a7238416ef3486a80f55ad209a19bed",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid0938c95b@dx6ded1b29f79d3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "3d96a4e2ed944c3c8a16f6cf2b16513c","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb810@dx1957f4e5fc9b8aa532"}
started:
ws start
####################
测试进行: ctm00011ebc@hu17ba9ed77a4020c902#47643177.pcm
{"recordId":"ase000e207c@hu1957f4e71e01323882:3d96a4e2ed944c3c8a16f6cf2b16513c","requestId":"ase000e207c@hu1957f4e71e01323882","sessionId":"cid000bb810@dx1957f4e5fc9b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597602}
{"recordId":"gty000bb811@dx1957f4e6000b8aa532:3d96a4e2ed944c3c8a16f6cf2b16513c","requestId":"gty000bb811@dx1957f4e6000b8aa532","sessionId":"cid000bb810@dx1957f4e5fc9b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597602}
{"recordId":"gty000bb811@dx1957f4e6000b8aa532:3d96a4e2ed944c3c8a16f6cf2b16513c","requestId":"gty000bb811@dx1957f4e6000b8aa532","sessionId":"cid000bb810@dx1957f4e5fc9b8aa532","eof":"1","text":"关于杨幂的新闻","pinyin":"","topic":"asr.speech.result","age":"elder","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597603}
send data finished:1741597603.1185968
{"recordId":"gty000bb811@dx1957f4e6000b8aa532:3d96a4e2ed944c3c8a16f6cf2b16513c","requestId":"gty000bb811@dx1957f4e6000b8aa532","sessionId":"cid000bb810@dx1957f4e5fc9b8aa532","topic":"dm.output","skill":"新闻","skillId":"2019031900001180","speakUrl":"","error":{},"dm":{"input":"关于杨幂的新闻","intentId":"PLAY","intentName":"听新闻","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"关于杨幂的新闻","skill":"新闻","skillId":"2019031900001180","skillVersion":"87","semantics":{"request":{"slots":[{"name":"keyword","value":"关于杨幂"}]}}},"timestamp":1741597603}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid34e6f985@dxa3031b29f7a33eef00"}
连接正常关闭
1741597603
param:b'{\n            "auth_id": "04fda031fff94654a3370013940a1880",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid0acbc309@dx888d1b29f7a33eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "50b8cf70ad9049c2ab4110654d3f1a69","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bba84@dx1957f4e77d57844532"}
started:
ws start
####################
测试进行: ctm00011ebe@hu17ba9ed7823020c902#47643181.pcm
{"recordId":"gty000bba85@dx1957f4e78117844532:50b8cf70ad9049c2ab4110654d3f1a69","requestId":"gty000bba85@dx1957f4e78117844532","sessionId":"cid000bba84@dx1957f4e77d57844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597608}
{"recordId":"gty000bba85@dx1957f4e78117844532:50b8cf70ad9049c2ab4110654d3f1a69","requestId":"gty000bba85@dx1957f4e78117844532","sessionId":"cid000bba84@dx1957f4e77d57844532","eof":"1","text":"说新闻","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597608}
send data finished:1741597609.0421615
{"recordId":"gty000bba85@dx1957f4e78117844532:50b8cf70ad9049c2ab4110654d3f1a69","requestId":"gty000bba85@dx1957f4e78117844532","sessionId":"cid000bba84@dx1957f4e77d57844532","topic":"dm.output","skill":"新闻","skillId":"2019031900001180","speakUrl":"","error":{},"dm":{"input":"说新闻","intentId":"PLAY","intentName":"听新闻","nlg":"可以了解下3月10日人本生活资讯晚报：全国政协委员张凯丽：建议大力打击谣言黑产；哪吒2英配版将发行院线","widget":{"content":[{"album":"新闻","title":"3月10日人本生活资讯晚报：全国政协委员张凯丽：建议大力打击谣言黑产；哪吒2英配版将发行院线","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202503101610A6F32914097BFB52019E023671AEF9BF_64.mp3?pf=OH9GI&vid=11723586&tm=1741597609112&pid=1898262","extra":{"source":""}},{"album":"新闻","title":"减肥4年后绝大多数人恢复之前体重","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2025031015223F38235525E04BCD655F1A1FC53E5265_64.mp3?pf=OH9GI&vid=11723578&tm=1741597609112&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"人生最容易长胖的5个时刻","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202503101518265A9818823909625DF2F29B090E4595_64.mp3?pf=OH9GI&vid=11723563&tm=1741597609112&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"蹲便冲水细菌是坐便的2倍","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2025031015130EA1D3CDD6B6C8FFED07928B1EF5D96C_64.mp3?pf=OH9GI&vid=11723562&tm=1741597609112&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"代表建议加大医美行业监管力度﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202503101455893E4D847DE5CC6E99894AAF5C6287C6_64.mp3?pf=OH9GI&vid=11723554&tm=1741597609112&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"青少年需要身体能出汗的体育课﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2025031014558DC7F45429AA8B9EEFB93689A0B4901E_64.mp3?pf=OH9GI&vid=11723548&tm=1741597609112&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"防腐剂脱氢乙酸钠全面禁用是谣言﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202503101455491F9B10CADDCD42524F8ED3283E9F7C_64.mp3?pf=OH9GI&vid=11723547&tm=1741597609112&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"《纽约时报》急问为什么小米可以造电动车，苹果却不能？","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202503101437E72886650ABC5F2085FD1CC6EFAA7474_64.mp3?pf=OH9GI&vid=11723538&tm=1741597609112&pid=194144","extra":{"source":"fm"}},{"album":"新闻","title":"养老服务向全体老年人拓展﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2025031012542B3AF8EDDF8118971866627D50AA16AF_64.mp3?pf=OH9GI&vid=11723480&tm=1741597609112&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"加拿大称将继续对美征收报复性关税﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/20250310125416F7FCBFA3CCE31C40BE0A772F4D0E45_64.mp3?pf=OH9GI&vid=11723476&tm=1741597609112&pid=1897566","extra":{"source":"fm"}}]},"shouldEndSession":true},"nlu":{"input":"说新闻","skill":"新闻","skillId":"2019031900001180","skillVersion":"87","semantics":{"request":{"slots":[]}}},"timestamp":1741597609}
{"recordId":"ase000f9801@hu1957f4e8ccb05c2882:50b8cf70ad9049c2ab4110654d3f1a69","requestId":"ase000f9801@hu1957f4e8ccb05c2882","sessionId":"cid000bba84@dx1957f4e77d57844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597609}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cida6ab00eb@dx64971b29f7a93eef00"}
连接正常关闭
1741597609
param:b'{\n            "auth_id": "a77c2fdc164647ec9635d3e9b0412a29",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid25adfd94@dxcfbc1b29f7a93eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "ebe3f5c542db490f9da0be9d83018db9","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bba8a@dx1957f4e8ef97844532"}
started:
ws start
####################
测试进行: ctm00011ec6@hu17ba9ed7e40020c902#47643186.pcm
{"recordId":"ase000f9b32@hu1957f4ea0d605c2882:ebe3f5c542db490f9da0be9d83018db9","requestId":"ase000f9b32@hu1957f4ea0d605c2882","sessionId":"cid000bba8a@dx1957f4e8ef97844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597614}
{"recordId":"gty000bba8b@dx1957f4e8f577844532:ebe3f5c542db490f9da0be9d83018db9","requestId":"gty000bba8b@dx1957f4e8f577844532","sessionId":"cid000bba8a@dx1957f4e8ef97844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597614}
{"recordId":"gty000bba8b@dx1957f4e8f577844532:ebe3f5c542db490f9da0be9d83018db9","requestId":"gty000bba8b@dx1957f4e8f577844532","sessionId":"cid000bba8a@dx1957f4e8ef97844532","eof":"1","text":"中央新闻","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597614}
send data finished:1741597614.7961833
{"recordId":"gty000bba8b@dx1957f4e8f577844532:ebe3f5c542db490f9da0be9d83018db9","requestId":"gty000bba8b@dx1957f4e8f577844532","sessionId":"cid000bba8a@dx1957f4e8ef97844532","topic":"dm.output","skill":"新闻","skillId":"2019031900001180","speakUrl":"","error":{},"dm":{"input":"中央新闻","intentId":"PLAY","intentName":"听新闻","nlg":"推动党的方针政策下基层润人心——论深入扎实开展主题教育","widget":{"content":[{"album":"","title":"推动党的方针政策下基层润人心——论深入扎实开展主题教育","subTitle":"","imageUrl":"https://jcdn.xhby.net/JHD/publish/20231026/t3_(27X91X485X396)16982739656872893245709420708.png","linkUrl":"http://jnews.xhby.net/v3/interfacearticles/3b6975e1821249f780fe173c896302d4/yqo3yPrceuUdnOMx/1","extra":{"source":"交汇点"}},{"album":"","title":"新华日报评论员｜推动党的方针政策下基层润人心——论深入扎实开展主题教育","subTitle":"","imageUrl":"https://jcdn.xhby.net/JHD/publish/20231026/t3_(0X0X345X230)169827869954651089371988755534.jpg","linkUrl":"http://jnews.xhby.net/v3/interfacearticles/3b6975e1821249f780fe173c896302d4/yqo3yPrceuUdnOMx/1","extra":{"source":"新华日报"}},{"album":"","title":"王毅：推动中美关系尽快回到健康、稳定、可持续发展轨道","subTitle":"","imageUrl":"https://jcdn.xhby.net/JHD/publish/20231027/t3_(0X0X345X230)16983697345576943829841587812.jpg","linkUrl":"http://jnews.xhby.net/v3/interfacearticles/3b6975e1821249f780fe173c896302d4/9u7QOPptpEGjB3Sm/1","extra":{"source":"新华社"}},{"album":"","title":"三季度全国规模以上工业企业利润同比增长7.7%","subTitle":"","imageUrl":"https://jcdn.xhby.net/JHD/publish/20231027/t3_(17X88X500X410)169838062457640811803936638305.jpg","linkUrl":"http://jnews.xhby.net/v3/interfacearticles/3b6975e1821249f780fe173c896302d4/3wQfYKrh7iIUslCE/1","extra":{"source":"新华社"}},{"album":"","title":"新华日报评论员｜调查研究当“沉到一线”","subTitle":"","imageUrl":"https://jcdn.xhby.net/JHD/publish/20231027/t3_(58X118X456X383)169837148380938903846263687.jpg","linkUrl":"http://jnews.xhby.net/v3/interfacearticles/3b6975e1821249f780fe173c896302d4/y04TbPq9UOI5E8he/1","extra":{"source":"交汇点"}},{"album":"","title":"“算大账、算长远账”，长江经济带有了这些变化","subTitle":"","imageUrl":"https://jcdn.xhby.net/JHD/publish/20231026/t3_(31X99X487X403)16982784783576019698231085899.jpg","linkUrl":"http://jnews.xhby.net/v3/interfacearticles/3b6975e1821249f780fe173c896302d4/s0BLPCheubr3MAGP/1","extra":{"source":"人民日报"}},{"album":"","title":"北京长峰医院重大火灾事故相关责任人被严肃查处","subTitle":"","imageUrl":"https://jcdn.xhby.net/JHD/publish/20231025/t3_(117X165X368X332)16982269861186087174503590292.jpg","linkUrl":"http://jnews.xhby.net/v3/interfacearticles/3b6975e1821249f780fe173c896302d4/5mPChXfR1e0KO93r/1","extra":{"source":"新华社"}},{"album":"","title":"做实“四三二” 提升企业走访“三个度”","subTitle":"","imageUrl":"https://jcdn.xhby.net/JHD/publish/20231026/t3_(0X0X345X230)16982865457382757379767497002.jpg","linkUrl":"http://jnews.xhby.net/v3/interfacearticles/3b6975e1821249f780fe173c896302d4/tNvitFkLytfKVCfZ/1","extra":{"source":"交汇点"}},{"album":"","title":"国家数据局正式挂牌，为何成立？职责是什么？","subTitle":"","imageUrl":"https://jcdn.xhby.net/JHD/publish/20231025/t3_(54X132X453X398)16982426511676841198121076162.png","linkUrl":"http://jnews.xhby.net/v3/interfacearticles/3b6975e1821249f780fe173c896302d4/2fhACilLztREEMwU/1","extra":{"source":"新华社"}},{"album":"","title":"出征“天宫”再启航——记神舟十七号航天员","subTitle":"","imageUrl":"https://jcdn.xhby.net/JHD/publish/20231026/t3_(14X86X500X410)16982750224904287967135888616.jpeg","linkUrl":"http://jnews.xhby.net/v3/interfacearticles/3b6975e1821249f780fe173c896302d4/YYpe6qpOzcKnu4mo/1","extra":{"source":"新华社"}}]},"shouldEndSession":true},"nlu":{"input":"中央新闻","skill":"新闻","skillId":"2019031900001180","skillVersion":"87","semantics":{"request":{"slots":[{"name":"channel","value":"中央"}]}}},"timestamp":1741597614}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidc0de3091@dx7ea41b29f7ae3eef00"}
连接正常关闭
1741597615
param:b'{\n            "auth_id": "815d62ad8cdd48dcb2328cc6cb972c65",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid5c7acb1a@dx65ec1b29f7af3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "e9d0cca29f8f4bfb90cc84b0841bd1fc","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb8fe@dx1957f4ea4d1b86a532"}
started:
ws start
####################
测试进行: ctm00011ec8@hu17ba9ed7ea1020c902#47643193.pcm
{"recordId":"ase000e1c8f@hu1957f4eb70e05bf882:e9d0cca29f8f4bfb90cc84b0841bd1fc","requestId":"ase000e1c8f@hu1957f4eb70e05bf882","sessionId":"cid000bb8fe@dx1957f4ea4d1b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597620}
{"recordId":"gty000bb8ff@dx1957f4ea50cb86a532:e9d0cca29f8f4bfb90cc84b0841bd1fc","requestId":"gty000bb8ff@dx1957f4ea50cb86a532","sessionId":"cid000bb8fe@dx1957f4ea4d1b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597621}
{"recordId":"gty000bb8ff@dx1957f4ea50cb86a532:e9d0cca29f8f4bfb90cc84b0841bd1fc","requestId":"gty000bb8ff@dx1957f4ea50cb86a532","sessionId":"cid000bb8fe@dx1957f4ea4d1b86a532","eof":"1","text":"最近有什么热门的新闻吗","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597621}
send data finished:1741597622.105718
{"recordId":"gty000bb8ff@dx1957f4ea50cb86a532:e9d0cca29f8f4bfb90cc84b0841bd1fc","requestId":"gty000bb8ff@dx1957f4ea50cb86a532","sessionId":"cid000bb8fe@dx1957f4ea4d1b86a532","topic":"dm.output","skill":"新闻","skillId":"2019031900001180","speakUrl":"","error":{},"dm":{"input":"最近有什么热门的新闻吗","intentId":"PLAY","intentName":"听新闻","nlg":"为您找到热点的新闻，可以了解下俄军收复库尔斯克3/4领土，精锐力量钻入天然气管道发动奇袭画面曝光","widget":{"content":[{"album":"新闻","title":"俄军收复库尔斯克3/4领土，精锐力量钻入天然气管道发动奇袭画面曝光","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2025031015024BB31E78A26C5F7A015470E4BFB70F5C_64.mp3?pf=OH9GI&vid=11723596&tm=1741597622102&pid=1897566","extra":{"source":""}},{"album":"新闻","title":"加拿大前央行行长卡尼当选自由党新党魁","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202503101527AC0E2F221A60AF9C4C76B4E0185BD523_64.mp3?pf=OH9GI&vid=11723595&tm=1741597622102&pid=1897566","extra":{"source":""}},{"album":"新闻","title":"港交所：港股日均成交额暂已突破2000亿港元","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2025031015482B308EAD9AB8C43260D49190815CFEE6_64.mp3?pf=OH9GI&vid=11723594&tm=1741597622102&pid=1897566","extra":{"source":""}},{"album":"新闻","title":"减肥4年后绝大多数人恢复之前体重","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2025031015223F38235525E04BCD655F1A1FC53E5265_64.mp3?pf=OH9GI&vid=11723578&tm=1741597622102&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"人生最容易长胖的5个时刻","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202503101518265A9818823909625DF2F29B090E4595_64.mp3?pf=OH9GI&vid=11723563&tm=1741597622102&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"蹲便冲水细菌是坐便的2倍","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2025031015130EA1D3CDD6B6C8FFED07928B1EF5D96C_64.mp3?pf=OH9GI&vid=11723562&tm=1741597622102&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"知情人士回应大疆强制员工下班：公司“鼓励大家合理安排时间”","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2025031014556681FC5C10A0E9936668A7341354249B_64.mp3?pf=OH9GI&vid=11723559&tm=1741597622102&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"青少年需要身体能出汗的体育课﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2025031014558DC7F45429AA8B9EEFB93689A0B4901E_64.mp3?pf=OH9GI&vid=11723548&tm=1741597622103&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"防腐剂脱氢乙酸钠全面禁用是谣言﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202503101455491F9B10CADDCD42524F8ED3283E9F7C_64.mp3?pf=OH9GI&vid=11723547&tm=1741597622103&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"研究表明：AI练功不当会走火入魔﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202503101455C172ED7DD7506BB8ED76167B45626564_64.mp3?pf=OH9GI&vid=11723546&tm=1741597622103&pid=1897566","extra":{"source":"fm"}}]},"shouldEndSession":true},"nlu":{"input":"最近有什么热门的新闻吗","skill":"新闻","skillId":"2019031900001180","skillVersion":"87","semantics":{"request":{"slots":[{"name":"category","value":"热点"}]}}},"timestamp":1741597622}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid84fffef8@dx53091b29f7b63eef00"}
连接正常关闭
1741597622
param:b'{\n            "auth_id": "96dfe959ccfb4c128ad0525aa0950711",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidbb99892e@dxdba01b29f7b63eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "8cd3331f84ac40cab69f133d0b30aba0","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb81c@dx1957f4ec0e7b8aa532"}
started:
ws start
####################
测试进行: ctm00011ed7@hu17ba9ed874f020c902#47643204.pcm
{"recordId":"ase000ef6b0@hu1957f4ed29e05c3882:8cd3331f84ac40cab69f133d0b30aba0","requestId":"ase000ef6b0@hu1957f4ed29e05c3882","sessionId":"cid000bb81c@dx1957f4ec0e7b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597627}
{"recordId":"gty000bb81d@dx1957f4ec11cb8aa532:8cd3331f84ac40cab69f133d0b30aba0","requestId":"gty000bb81d@dx1957f4ec11cb8aa532","sessionId":"cid000bb81c@dx1957f4ec0e7b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597630}
{"recordId":"gty000bb81d@dx1957f4ec11cb8aa532:8cd3331f84ac40cab69f133d0b30aba0","requestId":"gty000bb81d@dx1957f4ec11cb8aa532","sessionId":"cid000bb81c@dx1957f4ec0e7b8aa532","eof":"1","text":"放一首王力宏和谭维维的缘分是一道桥","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597631}
send data finished:1741597631.0818148
{"recordId":"gty000bb81d@dx1957f4ec11cb8aa532:8cd3331f84ac40cab69f133d0b30aba0","requestId":"gty000bb81d@dx1957f4ec11cb8aa532","sessionId":"cid000bb81c@dx1957f4ec0e7b8aa532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"放一首王力宏和谭维维的缘分是一道桥","intentId":"PLAY","intentName":"点歌","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"放一首王力宏和谭维维的缘分是一道桥","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"歌曲名","value":"缘分是一道桥"},{"name":"歌手名","value":"王力宏"},{"name":"合唱歌手","value":"谭维维"}]}}},"timestamp":1741597631}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cide97e3f18@dx26ac1b29f7bf3eef00"}
连接正常关闭
1741597631
param:b'{\n            "auth_id": "8b66312dae43443e8b0fcd4b19d3cfe3",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid423766ae@dx36f11b29f7bf3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "eee1db4f235940c7a080796dd34ac72e","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb07e@dx1957f4ee3f1b8a9532"}
started:
ws start
####################
测试进行: ctm00011ee2@hu17ba9ed8da1020c902#47643207.pcm
{"recordId":"ase000f9acb@hu1957f4ef64205c0882:eee1db4f235940c7a080796dd34ac72e","requestId":"ase000f9acb@hu1957f4ef64205c0882","sessionId":"cid000bb07e@dx1957f4ee3f1b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597636}
{"recordId":"gty000bb07f@dx1957f4ee42bb8a9532:eee1db4f235940c7a080796dd34ac72e","requestId":"gty000bb07f@dx1957f4ee42bb8a9532","sessionId":"cid000bb07e@dx1957f4ee3f1b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597637}
{"recordId":"gty000bb07f@dx1957f4ee42bb8a9532:eee1db4f235940c7a080796dd34ac72e","requestId":"gty000bb07f@dx1957f4ee42bb8a9532","sessionId":"cid000bb07e@dx1957f4ee3f1b8a9532","eof":"1","text":"来一首我怀念的","pinyin":"","topic":"asr.speech.result","age":"elder","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597637}
send data finished:1741597637.231842
{"recordId":"gty000bb07f@dx1957f4ee42bb8a9532:eee1db4f235940c7a080796dd34ac72e","requestId":"gty000bb07f@dx1957f4ee42bb8a9532","sessionId":"cid000bb07e@dx1957f4ee3f1b8a9532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"来一首我怀念的","intentId":"PLAY","intentName":"点歌","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"来一首我怀念的","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"歌曲名","value":"我怀念的"}]}}},"timestamp":1741597637}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid872302e8@dx79e21b29f7c53eef00"}
连接正常关闭
1741597637
param:b'{\n            "auth_id": "8ed98af21bbb46188bf66379e84a52fd",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cideb36eb74@dx2f131b29f7c53eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "2eb9676995a94f719e87909d9709c309","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb90c@dx1957f4efbeab86a532"}
started:
ws start
####################
测试进行: ctm00011ee5@hu17ba9ed8e29020c902#47643213.pcm
{"recordId":"ase000facf0@hu1957f4f0e8b05c2882:2eb9676995a94f719e87909d9709c309","requestId":"ase000facf0@hu1957f4f0e8b05c2882","sessionId":"cid000bb90c@dx1957f4efbeab86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597642}
{"recordId":"gty000bb90d@dx1957f4efc23b86a532:2eb9676995a94f719e87909d9709c309","requestId":"gty000bb90d@dx1957f4efc23b86a532","sessionId":"cid000bb90c@dx1957f4efbeab86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597645}
{"recordId":"gty000bb90d@dx1957f4efc23b86a532:2eb9676995a94f719e87909d9709c309","requestId":"gty000bb90d@dx1957f4efc23b86a532","sessionId":"cid000bb90c@dx1957f4efbeab86a532","eof":"1","text":"可以放杨千嬅别的歌给我听吗","pinyin":"","topic":"asr.speech.result","age":"elder","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597645}
send data finished:1741597645.4674478
{"recordId":"gty000bb90d@dx1957f4efc23b86a532:2eb9676995a94f719e87909d9709c309","requestId":"gty000bb90d@dx1957f4efc23b86a532","sessionId":"cid000bb90c@dx1957f4efbeab86a532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"可以放杨千嬅别的歌给我听吗","intentId":"PLAY","intentName":"点歌","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"可以放杨千嬅别的歌给我听吗","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"歌手名","value":"杨千嬅"}]}}},"timestamp":1741597645}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid49f9fc5f@dx5e3a1b29f7cd3eef00"}
连接正常关闭
1741597645
param:b'{\n            "auth_id": "5c175cc703034d9aa0f0e176478dfb18",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cida0c54747@dxbf1c1b29f7cd3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "9ebb23a6e10548bc9f616c85937ecb43","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bbaae@dx1957f4f1c317844532"}
started:
ws start
####################
测试进行: ctm000121ed@hu17b59a7bb700212902#46466916.pcm
{"recordId":"ase000e2fb3@hu1957f4f2d0605bf882:9ebb23a6e10548bc9f616c85937ecb43","requestId":"ase000e2fb3@hu1957f4f2d0605bf882","sessionId":"cid000bbaae@dx1957f4f1c317844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597650}
{"recordId":"gty000bbaaf@dx1957f4f1c677844532:9ebb23a6e10548bc9f616c85937ecb43","requestId":"gty000bbaaf@dx1957f4f1c677844532","sessionId":"cid000bbaae@dx1957f4f1c317844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597650}
{"recordId":"gty000bbaaf@dx1957f4f1c677844532:9ebb23a6e10548bc9f616c85937ecb43","requestId":"gty000bbaaf@dx1957f4f1c677844532","sessionId":"cid000bbaae@dx1957f4f1c317844532","eof":"1","text":"我想听钢琴曲","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741597650}
send data finished:1741597651.03287
{"recordId":"gty000bbaaf@dx1957f4f1c677844532:9ebb23a6e10548bc9f616c85937ecb43","requestId":"gty000bbaaf@dx1957f4f1c677844532","sessionId":"cid000bbaae@dx1957f4f1c317844532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"我想听钢琴曲","intentId":"PLAY","intentName":"点歌","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"我想听钢琴曲","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"乐器","value":"钢琴"},{"name":"标签","value":"钢琴曲"}]}}},"timestamp":1741597651}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid2f8f38cb@dxa2f71b29f7d33eef00"}
连接正常关闭
1741597651
param:b'{\n            "auth_id": "aff00b4ce8a1481fa2e3da3fe0c9ef09",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidcf1c5ad1@dx00b21b29f7d33eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "799c140efc26494c966eb3c11c85bc27","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bbab4@dx1957f4f31e07844532"}
started:
ws start
####################
测试进行: ctm000121ff@hu17b59a7c16b0212902#46466942.pcm
{"recordId":"ase000e41eb@hu1957f4f410b1323882:799c140efc26494c966eb3c11c85bc27","requestId":"ase000e41eb@hu1957f4f410b1323882","sessionId":"cid000bbab4@dx1957f4f31e07844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597655}
{"recordId":"gty000bbab5@dx1957f4f32187844532:799c140efc26494c966eb3c11c85bc27","requestId":"gty000bbab5@dx1957f4f32187844532","sessionId":"cid000bbab4@dx1957f4f31e07844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597656}
{"recordId":"gty000bbab5@dx1957f4f32187844532:799c140efc26494c966eb3c11c85bc27","requestId":"gty000bbab5@dx1957f4f32187844532","sessionId":"cid000bbab4@dx1957f4f31e07844532","eof":"1","text":"想听韩甜甜唱的歌","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741597656}
send data finished:1741597656.8240147
{"recordId":"gty000bbab5@dx1957f4f32187844532:799c140efc26494c966eb3c11c85bc27","requestId":"gty000bbab5@dx1957f4f32187844532","sessionId":"cid000bbab4@dx1957f4f31e07844532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"想听韩甜甜唱的歌","intentId":"PLAY","intentName":"点歌","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"想听韩甜甜唱的歌","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"歌手名","value":"韩甜甜"}]}}},"timestamp":1741597656}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid139ff488@dx82e51b29f7d83eef00"}
连接正常关闭
1741597656
param:b'{\n            "auth_id": "973bac601a9643f3a4346360cbdcb489",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidf339499e@dxbcd91b29f7d93eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "3cba879c6fdc44078f31b17857c90a85","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb923@dx1957f4f4894b86a532"}
started:
ws start
####################
测试进行: ctm0001220c@hu17b59a7c53a0212902#46466953.pcm
{"recordId":"ase000e45ce@hu1957f4f59b11323882:3cba879c6fdc44078f31b17857c90a85","requestId":"ase000e45ce@hu1957f4f59b11323882","sessionId":"cid000bb923@dx1957f4f4894b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597661}
{"recordId":"gty000bb924@dx1957f4f48cdb86a532:3cba879c6fdc44078f31b17857c90a85","requestId":"gty000bb924@dx1957f4f48cdb86a532","sessionId":"cid000bb923@dx1957f4f4894b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597661}
{"recordId":"gty000bb924@dx1957f4f48cdb86a532:3cba879c6fdc44078f31b17857c90a85","requestId":"gty000bb924@dx1957f4f48cdb86a532","sessionId":"cid000bb923@dx1957f4f4894b86a532","eof":"1","text":"别再唱歌了","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741597662}
send data finished:1741597662.1118097
{"recordId":"gty000bb924@dx1957f4f48cdb86a532:3cba879c6fdc44078f31b17857c90a85","requestId":"gty000bb924@dx1957f4f48cdb86a532","sessionId":"cid000bb923@dx1957f4f4894b86a532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"别再唱歌了","intentId":"RANDOM_SEARCH","intentName":"随机放歌","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"别再唱歌了","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[]}}},"timestamp":1741597662}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidae395e7b@dxf0471b29f7de3eef00"}
连接正常关闭
1741597662
param:b'{\n            "auth_id": "869b7f0c598a4ccca5dc4579bf3508ff",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidd3beda95@dx039d1b29f7de3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "249fe65aedb74b9d9504927ecb2b1c28","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb09b@dx1957f4f5d27b8a9532"}
started:
ws start
####################
测试进行: ctm00012221@hu17b59a7cc0a0212902#46466990.pcm
{"recordId":"ase000e48b3@hu1957f4f6cbf1323882:249fe65aedb74b9d9504927ecb2b1c28","requestId":"ase000e48b3@hu1957f4f6cbf1323882","sessionId":"cid000bb09b@dx1957f4f5d27b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597666}
{"recordId":"gty000bb09c@dx1957f4f5d5eb8a9532:249fe65aedb74b9d9504927ecb2b1c28","requestId":"gty000bb09c@dx1957f4f5d5eb8a9532","sessionId":"cid000bb09b@dx1957f4f5d27b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597667}
{"recordId":"gty000bb09c@dx1957f4f5d5eb8a9532:249fe65aedb74b9d9504927ecb2b1c28","requestId":"gty000bb09c@dx1957f4f5d5eb8a9532","sessionId":"cid000bb09b@dx1957f4f5d27b8a9532","eof":"1","text":"请顺序播放","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741597667}
send data finished:1741597667.3831682
{"recordId":"gty000bb09c@dx1957f4f5d5eb8a9532:249fe65aedb74b9d9504927ecb2b1c28","requestId":"gty000bb09c@dx1957f4f5d5eb8a9532","sessionId":"cid000bb09b@dx1957f4f5d27b8a9532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"请顺序播放","intentId":"INSTRUCTION","intentName":"音乐控制","nlg":"","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"请顺序播放","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"操作","value":"order"}]}}},"timestamp":1741597667}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid8d4fac9c@dx0adc1b29f7e33eef00"}
连接正常关闭
1741597667
param:b'{\n            "auth_id": "82520fd282524c0d90dc6bf6e6b8ae15",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid92be2e14@dxb7131b29f7e33eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "7e86cab9a431401287466867a2d3cf58","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb92b@dx1957f4f71bdb86a532"}
started:
ws start
####################
测试进行: ctm0001254a@hu17b6668678b020c902#46613773.pcm
{"recordId":"ase000e4c83@hu1957f4f84d71323882:7e86cab9a431401287466867a2d3cf58","requestId":"ase000e4c83@hu1957f4f84d71323882","sessionId":"cid000bb92b@dx1957f4f71bdb86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597672}
{"recordId":"gty000bb92c@dx1957f4f71efb86a532:7e86cab9a431401287466867a2d3cf58","requestId":"gty000bb92c@dx1957f4f71efb86a532","sessionId":"cid000bb92b@dx1957f4f71bdb86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597673}
{"recordId":"gty000bb92c@dx1957f4f71efb86a532:7e86cab9a431401287466867a2d3cf58","requestId":"gty000bb92c@dx1957f4f71efb86a532","sessionId":"cid000bb92b@dx1957f4f71bdb86a532","eof":"1","text":"我要听内涵段子","pinyin":"","topic":"asr.speech.result","age":"child","ageScore":"","gender":"","genderScore":"","language_class":"mandarin","timestamp":1741597673}
send data finished:1741597673.6744006
{"recordId":"gty000bb92c@dx1957f4f71efb86a532:7e86cab9a431401287466867a2d3cf58","requestId":"gty000bb92c@dx1957f4f71efb86a532","sessionId":"cid000bb92b@dx1957f4f71bdb86a532","topic":"dm.output","skill":"旧版笑话","skillId":"2019032500000002","speakUrl":"","error":{},"dm":{"input":"我要听内涵段子","intentId":"QUERY","intentName":"笑话点播","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"我要听内涵段子","skill":"旧版笑话","skillId":"2019032500000002","skillVersion":"29","semantics":{"request":{"slots":[{"name":"keyword","value":"内涵"}]}}},"timestamp":1741597673}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidf450db62@dx79a71b29f7e93eef00"}
连接正常关闭
1741597673
param:b'{\n            "auth_id": "26df5628b0064db49ef80345d39ae6b1",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidc04ed78f@dx2da11b29f7e93eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "7f6cf81eda4b4f138f2e4f196c390cdc","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb0a8@dx1957f4f8a27b8a9532"}
started:
ws start
####################
测试进行: ctm00012551@hu17b66686d40020c902#46613780.pcm
{"recordId":"ase000e411d@hu1957f4f9cdb05bf882:7f6cf81eda4b4f138f2e4f196c390cdc","requestId":"ase000e411d@hu1957f4f9cdb05bf882","sessionId":"cid000bb0a8@dx1957f4f8a27b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597678}
{"recordId":"gty000bb0a9@dx1957f4f8a5db8a9532:7f6cf81eda4b4f138f2e4f196c390cdc","requestId":"gty000bb0a9@dx1957f4f8a5db8a9532","sessionId":"cid000bb0a8@dx1957f4f8a27b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597681}
{"recordId":"gty000bb0a9@dx1957f4f8a5db8a9532:7f6cf81eda4b4f138f2e4f196c390cdc","requestId":"gty000bb0a9@dx1957f4f8a5db8a9532","sessionId":"cid000bb0a8@dx1957f4f8a27b8a9532","eof":"1","text":"和我说一下lol赛事的新闻","pinyin":"","topic":"asr.speech.result","age":"child","ageScore":"","gender":"","genderScore":"","language_class":"mandarin","timestamp":1741597681}
send data finished:1741597681.917797
{"recordId":"gty000bb0a9@dx1957f4f8a5db8a9532:7f6cf81eda4b4f138f2e4f196c390cdc","requestId":"gty000bb0a9@dx1957f4f8a5db8a9532","sessionId":"cid000bb0a8@dx1957f4f8a27b8a9532","topic":"dm.output","skill":"新闻","skillId":"2019031900001180","speakUrl":"","error":{},"dm":{"input":"和我说一下lol赛事的新闻","intentId":"PLAY","intentName":"听新闻","nlg":"3月10日人本生活资讯晚报：全国政协委员张凯丽：建议大力打击谣言黑产；哪吒2英配版将发行院线","widget":{"content":[{"album":"新闻","title":"3月10日人本生活资讯晚报：全国政协委员张凯丽：建议大力打击谣言黑产；哪吒2英配版将发行院线","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202503101610A6F32914097BFB52019E023671AEF9BF_64.mp3?pf=OH9GI&vid=11723586&tm=1741597681989&pid=1898262","extra":{"source":""}},{"album":"新闻","title":"减肥4年后绝大多数人恢复之前体重","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2025031015223F38235525E04BCD655F1A1FC53E5265_64.mp3?pf=OH9GI&vid=11723578&tm=1741597681990&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"人生最容易长胖的5个时刻","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202503101518265A9818823909625DF2F29B090E4595_64.mp3?pf=OH9GI&vid=11723563&tm=1741597681990&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"蹲便冲水细菌是坐便的2倍","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2025031015130EA1D3CDD6B6C8FFED07928B1EF5D96C_64.mp3?pf=OH9GI&vid=11723562&tm=1741597681990&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"代表建议加大医美行业监管力度﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202503101455893E4D847DE5CC6E99894AAF5C6287C6_64.mp3?pf=OH9GI&vid=11723554&tm=1741597681990&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"青少年需要身体能出汗的体育课﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2025031014558DC7F45429AA8B9EEFB93689A0B4901E_64.mp3?pf=OH9GI&vid=11723548&tm=1741597681990&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"防腐剂脱氢乙酸钠全面禁用是谣言﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202503101455491F9B10CADDCD42524F8ED3283E9F7C_64.mp3?pf=OH9GI&vid=11723547&tm=1741597681990&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"《纽约时报》急问为什么小米可以造电动车，苹果却不能？","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202503101437E72886650ABC5F2085FD1CC6EFAA7474_64.mp3?pf=OH9GI&vid=11723538&tm=1741597681990&pid=194144","extra":{"source":"fm"}},{"album":"新闻","title":"养老服务向全体老年人拓展﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2025031012542B3AF8EDDF8118971866627D50AA16AF_64.mp3?pf=OH9GI&vid=11723480&tm=1741597681990&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"加拿大称将继续对美征收报复性关税﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/20250310125416F7FCBFA3CCE31C40BE0A772F4D0E45_64.mp3?pf=OH9GI&vid=11723476&tm=1741597681990&pid=1897566","extra":{"source":"fm"}}]},"shouldEndSession":true},"nlu":{"input":"和我说一下lol赛事的新闻","skill":"新闻","skillId":"2019031900001180","skillVersion":"87","semantics":{"request":{"slots":[]}}},"timestamp":1741597682}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid12b2850a@dx0af91b29f7f23eef00"}
连接正常关闭
1741597682
param:b'{\n            "auth_id": "acb9800b630a42d1af5f40d9a84fd50f",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid0c17c3ca@dx77a01b29f7f23eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "15d59d4d5d5d4c708094fe2328ffbfb5","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bbad5@dx1957f4faae57844532"}
started:
ws start
####################
测试进行: ctm00012566@hu17b666873fb020c902#46613793.pcm
{"recordId":"ase000f892e@hu1957f4fbd2105c4882:15d59d4d5d5d4c708094fe2328ffbfb5","requestId":"ase000f892e@hu1957f4fbd2105c4882","sessionId":"cid000bbad5@dx1957f4faae57844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597687}
{"recordId":"gty000bbad6@dx1957f4fab207844532:15d59d4d5d5d4c708094fe2328ffbfb5","requestId":"gty000bbad6@dx1957f4fab207844532","sessionId":"cid000bbad5@dx1957f4faae57844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597687}
{"recordId":"gty000bbad6@dx1957f4fab207844532:15d59d4d5d5d4c708094fe2328ffbfb5","requestId":"gty000bbad6@dx1957f4fab207844532","sessionId":"cid000bbad5@dx1957f4faae57844532","eof":"1","text":"我想听新闻","pinyin":"","topic":"asr.speech.result","age":"child","ageScore":"","gender":"","genderScore":"","language_class":"mandarin","timestamp":1741597687}
send data finished:1741597687.8827477
{"recordId":"gty000bbad6@dx1957f4fab207844532:15d59d4d5d5d4c708094fe2328ffbfb5","requestId":"gty000bbad6@dx1957f4fab207844532","sessionId":"cid000bbad5@dx1957f4faae57844532","topic":"dm.output","skill":"新闻","skillId":"2019031900001180","speakUrl":"","error":{},"dm":{"input":"我想听新闻","intentId":"PLAY","intentName":"听新闻","nlg":"可以了解下3月10日人本生活资讯晚报：全国政协委员张凯丽：建议大力打击谣言黑产；哪吒2英配版将发行院线","widget":{"content":[{"album":"新闻","title":"3月10日人本生活资讯晚报：全国政协委员张凯丽：建议大力打击谣言黑产；哪吒2英配版将发行院线","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202503101610A6F32914097BFB52019E023671AEF9BF_64.mp3?pf=OH9GI&vid=11723586&tm=1741597687980&pid=1898262","extra":{"source":""}},{"album":"新闻","title":"减肥4年后绝大多数人恢复之前体重","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2025031015223F38235525E04BCD655F1A1FC53E5265_64.mp3?pf=OH9GI&vid=11723578&tm=1741597687980&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"人生最容易长胖的5个时刻","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202503101518265A9818823909625DF2F29B090E4595_64.mp3?pf=OH9GI&vid=11723563&tm=1741597687980&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"蹲便冲水细菌是坐便的2倍","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2025031015130EA1D3CDD6B6C8FFED07928B1EF5D96C_64.mp3?pf=OH9GI&vid=11723562&tm=1741597687980&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"代表建议加大医美行业监管力度﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202503101455893E4D847DE5CC6E99894AAF5C6287C6_64.mp3?pf=OH9GI&vid=11723554&tm=1741597687980&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"青少年需要身体能出汗的体育课﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2025031014558DC7F45429AA8B9EEFB93689A0B4901E_64.mp3?pf=OH9GI&vid=11723548&tm=1741597687980&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"防腐剂脱氢乙酸钠全面禁用是谣言﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202503101455491F9B10CADDCD42524F8ED3283E9F7C_64.mp3?pf=OH9GI&vid=11723547&tm=1741597687980&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"《纽约时报》急问为什么小米可以造电动车，苹果却不能？","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202503101437E72886650ABC5F2085FD1CC6EFAA7474_64.mp3?pf=OH9GI&vid=11723538&tm=1741597687980&pid=194144","extra":{"source":"fm"}},{"album":"新闻","title":"养老服务向全体老年人拓展﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2025031012542B3AF8EDDF8118971866627D50AA16AF_64.mp3?pf=OH9GI&vid=11723480&tm=1741597687980&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"加拿大称将继续对美征收报复性关税﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/20250310125416F7FCBFA3CCE31C40BE0A772F4D0E45_64.mp3?pf=OH9GI&vid=11723476&tm=1741597687980&pid=1897566","extra":{"source":"fm"}}]},"shouldEndSession":true},"nlu":{"input":"我想听新闻","skill":"新闻","skillId":"2019031900001180","skillVersion":"87","semantics":{"request":{"slots":[]}}},"timestamp":1741597688}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cida6358e3f@dx01a21b29f7f83eef00"}
连接正常关闭
1741597688
param:b'{\n            "auth_id": "4eb1254c2c394054b43fcfa89e111d7a",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid158e9986@dx5be11b29f7f83eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "9af7b9fb19ac401f942d90a1dc2ed2d1","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb0b2@dx1957f4fc255b8a9532"}
started:
ws start
####################
测试进行: ctm00012575@hu17b666879d6020c902#46613807.pcm
{"recordId":"ase000e4a0b@hu1957f4fd33c05bf882:9af7b9fb19ac401f942d90a1dc2ed2d1","requestId":"ase000e4a0b@hu1957f4fd33c05bf882","sessionId":"cid000bb0b2@dx1957f4fc255b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597692}
{"recordId":"gty000bb0b3@dx1957f4fc296b8a9532:9af7b9fb19ac401f942d90a1dc2ed2d1","requestId":"gty000bb0b3@dx1957f4fc296b8a9532","sessionId":"cid000bb0b2@dx1957f4fc255b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597693}
{"recordId":"gty000bb0b3@dx1957f4fc296b8a9532:9af7b9fb19ac401f942d90a1dc2ed2d1","requestId":"gty000bb0b3@dx1957f4fc296b8a9532","sessionId":"cid000bb0b2@dx1957f4fc255b8a9532","eof":"1","text":"播放琵琶名曲","pinyin":"","topic":"asr.speech.result","age":"child","ageScore":"","gender":"","genderScore":"","language_class":"mandarin","timestamp":1741597693}
send data finished:1741597693.9257803
{"recordId":"gty000bb0b3@dx1957f4fc296b8a9532:9af7b9fb19ac401f942d90a1dc2ed2d1","requestId":"gty000bb0b3@dx1957f4fc296b8a9532","sessionId":"cid000bb0b2@dx1957f4fc255b8a9532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"播放琵琶名曲","intentId":"PLAY","intentName":"点歌","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"播放琵琶名曲","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"乐器","value":"琵琶"},{"name":"标签","value":"琵琶|名曲"}]}}},"timestamp":1741597693}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidcf1564b2@dxa1e31b29f7fd3eef00"}
连接正常关闭
1741597694
param:b'{\n            "auth_id": "0a28098a242148d188763d0e2c4867f5",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid29781701@dx0f071b29f7fe3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "6ead06b951d847d5811e704fb0f131aa","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb950@dx1957f4fd971b86a532"}
started:
ws start
####################
测试进行: ctm00012592@hu17b66688586020c902#46613825.pcm
{"recordId":"ase000de5cb@hu1957f4feaea0427882:6ead06b951d847d5811e704fb0f131aa","requestId":"ase000de5cb@hu1957f4feaea0427882","sessionId":"cid000bb950@dx1957f4fd971b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597698}
{"recordId":"gty000bb951@dx1957f4fd9afb86a532:6ead06b951d847d5811e704fb0f131aa","requestId":"gty000bb951@dx1957f4fd9afb86a532","sessionId":"cid000bb950@dx1957f4fd971b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597700}
{"recordId":"gty000bb951@dx1957f4fd9afb86a532:6ead06b951d847d5811e704fb0f131aa","requestId":"gty000bb951@dx1957f4fd9afb86a532","sessionId":"cid000bb950@dx1957f4fd971b86a532","eof":"1","text":"请放专辑全面沦陷","pinyin":"","topic":"asr.speech.result","age":"child","ageScore":"","gender":"","genderScore":"","language_class":"mandarin","timestamp":1741597700}
send data finished:1741597700.4764283
{"recordId":"gty000bb951@dx1957f4fd9afb86a532:6ead06b951d847d5811e704fb0f131aa","requestId":"gty000bb951@dx1957f4fd9afb86a532","sessionId":"cid000bb950@dx1957f4fd971b86a532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"请放专辑全面沦陷","intentId":"PLAY","intentName":"点歌","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"请放专辑全面沦陷","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"来源类型","value":"专辑"},{"name":"专辑名","value":"全面沦陷"},{"name":"歌曲来源","value":"全面沦陷"},{"name":"专辑名","value":"全面沦陷"}]}}},"timestamp":1741597700}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidea3508cd@dxccf91b29f8043eef00"}
连接正常关闭
1741597700
param:b'{\n            "auth_id": "140a7e9ec9e14d0ebffe92a66d8f4e50",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid9c5e83d9@dxfbe91b29f8043eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "e2ade8c7626c4a7b9c4c5b386af2b80d","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bbae2@dx1957f4ff30d7844532"}
started:
ws start
####################
测试进行: ctm00012657@hu17b62d10b340212902#46579980.pcm
{"recordId":"ase000e5342@hu1957f500bf305bf882:e2ade8c7626c4a7b9c4c5b386af2b80d","requestId":"ase000e5342@hu1957f500bf305bf882","sessionId":"cid000bbae2@dx1957f4ff30d7844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597707}
{"recordId":"gty000bbae3@dx1957f4ff34b7844532:e2ade8c7626c4a7b9c4c5b386af2b80d","requestId":"gty000bbae3@dx1957f4ff34b7844532","sessionId":"cid000bbae2@dx1957f4ff30d7844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597708}
{"recordId":"gty000bbae3@dx1957f4ff34b7844532:e2ade8c7626c4a7b9c4c5b386af2b80d","requestId":"gty000bbae3@dx1957f4ff34b7844532","sessionId":"cid000bbae2@dx1957f4ff30d7844532","eof":"1","text":"热门的娱乐节目","pinyin":"","topic":"asr.speech.result","age":"elder","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741597708}
send data finished:1741597708.2184975
{"recordId":"gty000bbae3@dx1957f4ff34b7844532:e2ade8c7626c4a7b9c4c5b386af2b80d","requestId":"gty000bbae3@dx1957f4ff34b7844532","sessionId":"cid000bbae2@dx1957f4ff30d7844532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"热门的娱乐节目","intentId":"chat","intentName":"闲聊","nlg":"这个领域我还正在研究哦，要不我们说点其他的吧！","shouldEndSession":true},"nlu":{"input":"热门的娱乐节目","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741597708}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidd6c91fd4@dxa7a71b29f80c3eef00"}
连接正常关闭
1741597708
param:b'{\n            "auth_id": "5b620374e1274e38bbc5fe2826d6a2dd",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidc5daba8a@dx68941b29f80c3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "4b751a42320e48059b7a1e845a6c0c9b","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb95d@dx1957f5011bfb86a532"}
started:
ws start
####################
测试进行: ctm00012666@hu17b62d110a00212902#46579987.pcm
{"recordId":"ase000e2cf4@hu1957f50254405c3882:4b751a42320e48059b7a1e845a6c0c9b","requestId":"ase000e2cf4@hu1957f50254405c3882","sessionId":"cid000bb95d@dx1957f5011bfb86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597713}
{"recordId":"gty000bb95e@dx1957f5011fcb86a532:4b751a42320e48059b7a1e845a6c0c9b","requestId":"gty000bb95e@dx1957f5011fcb86a532","sessionId":"cid000bb95d@dx1957f5011bfb86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597714}
{"recordId":"gty000bb95e@dx1957f5011fcb86a532:4b751a42320e48059b7a1e845a6c0c9b","requestId":"gty000bb95e@dx1957f5011fcb86a532","sessionId":"cid000bb95d@dx1957f5011bfb86a532","eof":"1","text":"赵薇的电视剧","pinyin":"","topic":"asr.speech.result","age":"elder","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741597714}
send data finished:1741597714.4889975
{"recordId":"gty000bb95e@dx1957f5011fcb86a532:4b751a42320e48059b7a1e845a6c0c9b","requestId":"gty000bb95e@dx1957f5011fcb86a532","sessionId":"cid000bb95d@dx1957f5011bfb86a532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"赵薇的电视剧","intentId":"chat","intentName":"闲聊","nlg":"这个问题有点难，你还是换一个吧。","shouldEndSession":true},"nlu":{"input":"赵薇的电视剧","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741597714}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid8f00c8cf@dxed211b29f8123eef00"}
连接正常关闭
1741597714
param:b'{\n            "auth_id": "388836c0ca2c4f0bbf66c53a491e8b21",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cida6e29031@dxf9a61b29f8123eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "e4f91f3b36f74c9aa285ab84101ff855","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bbaeb@dx1957f502a1f7844532"}
started:
ws start
####################
测试进行: ctm00012677@hu17b62d1193a0212902#46580004.pcm
{"recordId":"ase000f9ede@hu1957f50445205c4882:e4f91f3b36f74c9aa285ab84101ff855","requestId":"ase000f9ede@hu1957f50445205c4882","sessionId":"cid000bbaeb@dx1957f502a1f7844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597721}
{"recordId":"gty000bbaec@dx1957f502a567844532:e4f91f3b36f74c9aa285ab84101ff855","requestId":"gty000bbaec@dx1957f502a567844532","sessionId":"cid000bbaeb@dx1957f502a1f7844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597723}
{"recordId":"gty000bbaec@dx1957f502a567844532:e4f91f3b36f74c9aa285ab84101ff855","requestId":"gty000bbaec@dx1957f502a567844532","sessionId":"cid000bbaeb@dx1957f502a1f7844532","eof":"1","text":"每天提醒我晚上10:30睡觉","pinyin":"","topic":"asr.speech.result","age":"elder","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741597723}
send data finished:1741597724.0196037
{"recordId":"gty000bbaec@dx1957f502a567844532:e4f91f3b36f74c9aa285ab84101ff855","requestId":"gty000bbaec@dx1957f502a567844532","sessionId":"cid000bbaeb@dx1957f502a1f7844532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"每天提醒我晚上10:30睡觉","intentId":"chat","intentName":"闲聊","nlg":"这个我不太懂，我们聊聊别的吧。","shouldEndSession":true},"nlu":{"input":"每天提醒我晚上10:30睡觉","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741597724}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid319e54e4@dx1c591b29f81c3eef00"}
连接正常关闭
1741597724
param:b'{\n            "auth_id": "4e39341852bd40f68463fc5e23f2f466",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidaafa5040@dx7f3f1b29f81c3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "a70768f13baa4f338e85829ca18a58e5","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bbaf5@dx1957f504fa87844532"}
started:
ws start
####################
测试进行: ctm00012680@hu17b62d11f180212902#46580015.pcm
{"recordId":"ase000dfaae@hu1957f5069ee0427882:a70768f13baa4f338e85829ca18a58e5","requestId":"ase000dfaae@hu1957f5069ee0427882","sessionId":"cid000bbaf5@dx1957f504fa87844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597731}
{"recordId":"gty000bbaf6@dx1957f504fda7844532:a70768f13baa4f338e85829ca18a58e5","requestId":"gty000bbaf6@dx1957f504fda7844532","sessionId":"cid000bbaf5@dx1957f504fa87844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597732}
{"recordId":"gty000bbaf6@dx1957f504fda7844532:a70768f13baa4f338e85829ca18a58e5","requestId":"gty000bbaf6@dx1957f504fda7844532","sessionId":"cid000bbaf5@dx1957f504fa87844532","eof":"1","text":"北京天气冷不冷","pinyin":"","topic":"asr.speech.result","age":"elder","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741597732}
send data finished:1741597732.1558888
{"recordId":"gty000bbaf6@dx1957f504fda7844532:a70768f13baa4f338e85829ca18a58e5","requestId":"gty000bbaf6@dx1957f504fda7844532","sessionId":"cid000bbaf5@dx1957f504fa87844532","topic":"dm.output","skill":"天气","skillId":"2019042500000544","speakUrl":"","error":{},"dm":{"input":"北京天气冷不冷","intentId":"QUERY","intentName":"查询天气信息","nlg":"北京市现在15度，今天7℃ ~ 17℃，有点凉。","widget":{"webhookResp":{"result":[{"airData":144,"airQuality":"轻微污染","city":"北京市","date":"2025-03-10","dateLong":1741536000,"date_for_voice":"今天","exp":{"ct":{"expName":"穿衣指数","level":"温凉","prompt":"天气偏凉，增加衣物厚度。"},"dy":{"expName":"钓鱼指数","level":"不适宜","prompt":"雾霾天气，不适宜外出钓鱼。"},"gm":{"expName":"感冒指数","level":"易发","prompt":"感冒容易发生，少去人群密集的场所有利于降低感冒的几率。"},"jt":{"expName":"交通指数","level":"良好","prompt":"天气较好，路面干燥，交通气象条件良好，车辆可以正常行驶。"},"tr":{"expName":"旅游指数","level":"适宜","prompt":"天气较好，但丝毫不会影响您出行的心情。温度适宜又有微风相伴，适宜旅游。"},"uv":{"expName":"紫外线指数","level":"弱","prompt":"辐射较弱，请涂擦SPF12-15、PA+护肤品。"},"xc":{"expName":"洗车指数","level":"较适宜","prompt":"较适宜洗车，未来一天无雨，风力较小，擦洗一新的汽车至少能保持一天。"},"yd":{"expName":"运动指数","level":"不适宜","prompt":"空气轻度污染，不宜在户外运动。"}},"extra":"","humidity":"42%","img":"http://cdn9002.iflyos.cn/osweathericon/53.png","lastUpdateTime":"2025-03-10 17:00:08","pm25":"144","precipitation":"0","sunRise":"2025-03-10 06:34:00","sunSet":"2025-03-10 18:16:00","temp":15,"tempHigh":"17℃","tempLow":"7℃","tempRange":"7℃ ~ 17℃","tempReal":"12℃","visibility":"","warning":"","weather":"霾","weatherDescription":"有点凉。","weatherDescription3":"2℃到17℃，风不大，有点凉。","weatherDescription7":"-1℃到8℃，风不大，有点冷。","weatherType":53,"week":"周一","wind":"西南风3-4级","windLevel":0},{"airData":143,"airQuality":"轻微污染","city":"北京市","date":"2025-03-09","dateLong":1741449600,"date_for_voice":"昨天","humidity":"42%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-03-10 17:00:08","sunRise":"2025-03-09 06:35:00","sunSet":"2025-03-09 18:14:00","tempHigh":"17℃","tempLow":"7℃","tempRange":"7℃ ~ 17℃","weather":"晴转多云","weatherDescription":"有点凉。","weatherType":0,"week":"周日","wind":"西南风转南风3-4级","windLevel":1},{"airData":180,"airQuality":"轻度污染","city":"北京市","date":"2025-03-11","dateLong":1741622400,"date_for_voice":"明天","humidity":"55%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-03-10 17:00:08","sunRise":"2025-03-11 06:32:00","sunSet":"2025-03-11 18:17:00","tempHigh":"16℃","tempLow":"5℃","tempRange":"5℃ ~ 16℃","weather":"多云转浮尘","weatherDescription":"有点凉。","weatherType":1,"week":"周二","wind":"西风转西北风微风","windLevel":0},{"airData":90,"airQuality":"良","city":"北京市","date":"2025-03-12","dateLong":1741708800,"date_for_voice":"后天","humidity":"16%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-03-10 17:00:08","sunRise":"2025-03-12 06:31:00","sunSet":"2025-03-12 18:18:00","tempHigh":"16℃","tempLow":"2℃","tempRange":"2℃ ~ 16℃","weather":"晴","weatherDescription":"有点凉。","weatherType":0,"week":"周三","wind":"西北风转东南风3-4级","windLevel":1},{"airData":70,"airQuality":"良","city":"北京市","date":"2025-03-13","dateLong":1741795200,"date_for_voice":"13号","humidity":"21%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-03-10 17:00:08","sunRise":"2025-03-13 06:29:00","sunSet":"2025-03-13 18:19:00","tempHigh":"15℃","tempLow":"3℃","tempRange":"3℃ ~ 15℃","weather":"多云","weatherDescription":"有点凉。","weatherType":1,"week":"周四","wind":"西南风转东南风3-4级","windLevel":1},{"airData":90,"airQuality":"良","city":"北京市","date":"2025-03-14","dateLong":1741881600,"date_for_voice":"14号","humidity":"41%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-03-10 17:00:08","sunRise":"2025-03-14 06:27:00","sunSet":"2025-03-14 18:20:00","tempHigh":"10℃","tempLow":"3℃","tempRange":"3℃ ~ 10℃","weather":"多云转阴","weatherDescription":"有点冷。","weatherType":1,"week":"周五","wind":"东北风微风","windLevel":0},{"airData":60,"airQuality":"良","city":"北京市","date":"2025-03-15","dateLong":1741968000,"date_for_voice":"15号","humidity":"47%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-03-10 17:00:08","sunRise":"2025-03-15 06:26:00","sunSet":"2025-03-15 18:21:00","tempHigh":"8℃","tempLow":"1℃","tempRange":"1℃ ~ 8℃","weather":"阴","weatherDescription":"有点冷。","weatherType":2,"week":"周六","wind":"东北风转西北风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-16","dateLong":1742054400,"date_for_voice":"16号","humidity":"38%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-03-10 17:00:08","sunRise":"2025-03-16 06:24:00","sunSet":"2025-03-16 18:22:00","tempHigh":"8℃","tempLow":"-1℃","tempRange":"-1℃ ~ 8℃","weather":"多云","weatherDescription":"有点冷。","weatherType":1,"week":"周日","wind":"西北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-17","dateLong":1742140800,"date_for_voice":"17号","humidity":"27%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-03-10 17:00:08","sunRise":"2025-03-17 06:23:00","sunSet":"2025-03-17 18:23:00","tempHigh":"12℃","tempLow":"0℃","tempRange":"0℃ ~ 12℃","weather":"多云转晴","weatherDescription":"有点冷。","weatherType":1,"week":"下周一","wind":"西北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-18","dateLong":1742227200,"date_for_voice":"18号","humidity":"29%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-03-10 17:00:08","sunRise":"2025-03-18 06:21:00","sunSet":"2025-03-18 18:24:00","tempHigh":"9℃","tempLow":"1℃","tempRange":"1℃ ~ 9℃","weather":"晴","weatherDescription":"有点冷。","weatherType":0,"week":"下周二","wind":"西北风转西南风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-19","dateLong":1742313600,"date_for_voice":"19号","humidity":"29%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-03-10 17:00:08","sunRise":"2025-03-19 06:19:00","sunSet":"2025-03-19 18:25:00","tempHigh":"18℃","tempLow":"4℃","tempRange":"4℃ ~ 18℃","weather":"晴转阴","weatherDescription":"有点凉。","weatherType":0,"week":"下周三","wind":"西北风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-20","dateLong":1742400000,"date_for_voice":"20号","humidity":"26%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-03-10 17:00:08","sunRise":"2025-03-20 06:18:00","sunSet":"2025-03-20 18:26:00","tempHigh":"18℃","tempLow":"7℃","tempRange":"7℃ ~ 18℃","weather":"阴转小雨","weatherDescription":"有点凉。","weatherType":2,"week":"下周四","wind":"东南风转西南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-21","dateLong":1742486400,"date_for_voice":"21号","humidity":"31%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-03-10 17:00:08","sunRise":"2025-03-21 06:16:00","sunSet":"2025-03-21 18:27:00","tempHigh":"16℃","tempLow":"7℃","tempRange":"7℃ ~ 16℃","weather":"阴转小雨","weatherDescription":"有点凉。","weatherType":2,"week":"下周五","wind":"西南风转东南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-22","dateLong":1742572800,"date_for_voice":"22号","humidity":"38%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-03-10 17:00:08","sunRise":"2025-03-22 06:15:00","sunSet":"2025-03-22 18:28:00","tempHigh":"24℃","tempLow":"7℃","tempRange":"7℃ ~ 24℃","weather":"晴转多云","weatherDescription":"气候温暖。","weatherType":0,"week":"下周六","wind":"东北风转西北风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-23","dateLong":1742659200,"date_for_voice":"23号","humidity":"32%","img":"http://cdn9002.iflyos.cn/osweathericon/07.png","lastUpdateTime":"2025-03-10 17:00:08","sunRise":"2025-03-23 06:13:00","sunSet":"2025-03-23 18:29:00","tempHigh":"26℃","tempLow":"4℃","tempRange":"4℃ ~ 26℃","weather":"小雨转晴","weatherDescription":"气候温暖。","weatherType":7,"week":"下周日","wind":"东北风转西北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-24","dateLong":1742745600,"date_for_voice":"24号","humidity":"18%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-03-10 17:00:08","sunRise":"2025-03-24 06:11:00","sunSet":"2025-03-24 18:30:00","tempHigh":"14℃","tempLow":"2℃","tempRange":"2℃ ~ 14℃","weather":"晴转阴","weatherDescription":"有点凉。","weatherType":0,"week":"下下周一","wind":"西南风转西北风3-4级","windLevel":1}]},"cityName":"北京市"},"dm_intent":"custom","cityName":"北京市","shouldEndSession":true},"nlu":{"input":"北京天气冷不冷","skill":"天气","skillId":"2019042500000544","skillVersion":"56","semantics":{"request":{"intent":"QUERY","slots":[{"name":"datetime","normValue":"{\"datetime\":\"2025-03-10\",\"suggestDatetime\":\"2025-03-10\"}","value":"2025-03-10"},{"name":"location.city","normValue":"北京市","value":"北京市"},{"name":"location.cityAddr","normValue":"北京","value":"北京"},{"name":"location.type","normValue":"LOC_BASIC","value":"LOC_BASIC"},{"name":"queryType","value":"程度"},{"name":"queryValue","value":"冷"},{"name":"subfocus","value":"温度"}]}}},"timestamp":1741597732}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid71ae7281@dx80761b29f8243eef00"}
连接正常关闭
1741597732
param:b'{\n            "auth_id": "ff43f887fbe24ecb88d3a2554426da40",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid7e6a15e4@dxa3d21b29f8243eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "6c2a7e36f73a47cf9e5b744fe1ca26d4","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb969@dx1957f506edab86a532"}
started:
ws start
####################
测试进行: ctm00012f61@hu17b57ba0fdc020c902#46419822.pcm
{"recordId":"ase000fe7ed@hu1957f50802b05c2882:6c2a7e36f73a47cf9e5b744fe1ca26d4","requestId":"ase000fe7ed@hu1957f50802b05c2882","sessionId":"cid000bb969@dx1957f506edab86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597737}
{"recordId":"gty000bb96a@dx1957f506f0eb86a532:6c2a7e36f73a47cf9e5b744fe1ca26d4","requestId":"gty000bb96a@dx1957f506f0eb86a532","sessionId":"cid000bb969@dx1957f506edab86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597738}
{"recordId":"gty000bb96a@dx1957f506f0eb86a532:6c2a7e36f73a47cf9e5b744fe1ca26d4","requestId":"gty000bb96a@dx1957f506f0eb86a532","sessionId":"cid000bb969@dx1957f506edab86a532","eof":"1","text":"那是不是现在11:30","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597738}
send data finished:1741597738.8800995
{"recordId":"gty000bb96a@dx1957f506f0eb86a532:6c2a7e36f73a47cf9e5b744fe1ca26d4","requestId":"gty000bb96a@dx1957f506f0eb86a532","sessionId":"cid000bb969@dx1957f506edab86a532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"那是不是现在11:30","intentId":"chat","intentName":"闲聊","nlg":"惭愧惭愧，我好像并不知道那是什么。","shouldEndSession":true},"nlu":{"input":"那是不是现在11:30","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741597739}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid771f5dc4@dx194c1b29f82b3eef00"}
连接正常关闭
1741597739
param:b'{\n            "auth_id": "3f9f0dfae44f4829897d48edd14c5c8c",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidf252948f@dx8b741b29f82b3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "50f04149eeb54602be328b094041ce43","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb0f2@dx1957f5089b3b8a9532"}
started:
ws start
####################
测试进行: ctm00012f70@hu17b57ba16c7020c902#46419837.pcm
{"recordId":"ase000fec93@hu1957f509b3505c2882:50f04149eeb54602be328b094041ce43","requestId":"ase000fec93@hu1957f509b3505c2882","sessionId":"cid000bb0f2@dx1957f5089b3b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597744}
{"recordId":"gty000bb0f3@dx1957f5089e7b8a9532:50f04149eeb54602be328b094041ce43","requestId":"gty000bb0f3@dx1957f5089e7b8a9532","sessionId":"cid000bb0f2@dx1957f5089b3b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597744}
{"recordId":"gty000bb0f3@dx1957f5089e7b8a9532:50f04149eeb54602be328b094041ce43","requestId":"gty000bb0f3@dx1957f5089e7b8a9532","sessionId":"cid000bb0f2@dx1957f5089b3b8a9532","eof":"1","text":"今天是农历多少","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597744}
send data finished:1741597744.9546676
{"recordId":"gty000bb0f3@dx1957f5089e7b8a9532:50f04149eeb54602be328b094041ce43","requestId":"gty000bb0f3@dx1957f5089e7b8a9532","sessionId":"cid000bb0f2@dx1957f5089b3b8a9532","topic":"dm.output","skill":"时间日期","skillId":"IFLYTEK.datetimePro","speakUrl":"","error":{},"dm":{"input":"今天是农历多少","intentId":"WHATDATE","intentName":"查日期","nlg":"今天是乙巳年二月十一，星期一，2025年3月10号。","shouldEndSession":true},"nlu":{"input":"今天是农历多少","skill":"时间日期","skillId":"IFLYTEK.datetimePro","skillVersion":"474.0","semantics":{"request":{"slots":[{"name":"datetime","normValue":"{\"datetime\":\"2025-03-10\",\"suggestDatetime\":\"2025-03-10\"}","value":"今天"},{"name":"lunar","value":"农历"}]}}},"timestamp":1741597744}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidec2b404d@dx1b681b29f8303eef00"}
连接正常关闭
1741597745
param:b'{\n            "auth_id": "31006480ebaf42c587882a87c5278019",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid124ffeaf@dx69671b29f8313eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "7433a20558a84525a70f15b1b460468c","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb88c@dx1957f50a0afb8aa532"}
started:
ws start
####################
测试进行: ctm00012f7d@hu17b57ba1d26020c902#46419853.pcm
{"recordId":"ase000e442c@hu1957f50b3b305c3882:7433a20558a84525a70f15b1b460468c","requestId":"ase000e442c@hu1957f50b3b305c3882","sessionId":"cid000bb88c@dx1957f50a0afb8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597750}
{"recordId":"gty000bb88d@dx1957f50a142b8aa532:7433a20558a84525a70f15b1b460468c","requestId":"gty000bb88d@dx1957f50a142b8aa532","sessionId":"cid000bb88c@dx1957f50a0afb8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597750}
{"recordId":"gty000bb88d@dx1957f50a142b8aa532:7433a20558a84525a70f15b1b460468c","requestId":"gty000bb88d@dx1957f50a142b8aa532","sessionId":"cid000bb88c@dx1957f50a0afb8aa532","eof":"1","text":"播放来生缘","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597750}
send data finished:1741597750.7976015
{"recordId":"gty000bb88d@dx1957f50a142b8aa532:7433a20558a84525a70f15b1b460468c","requestId":"gty000bb88d@dx1957f50a142b8aa532","sessionId":"cid000bb88c@dx1957f50a0afb8aa532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"播放来生缘","intentId":"PLAY","intentName":"点歌","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"播放来生缘","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"歌曲名","value":"来生缘"}]}}},"timestamp":1741597750}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid7cb8a9af@dxbd611b29f8363eef00"}
连接正常关闭
1741597750
param:b'{\n            "auth_id": "133bdf62f39d4407a57af41a202aede8",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid561ca58e@dxe1511b29f8373eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "bd4f470b7f4c4cc9bb2ff453815b2dbf","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb894@dx1957f50b7b0b8aa532"}
started:
ws start
####################
测试进行: ctm00012f85@hu17b57ba2065020c902#46419861.pcm
{"recordId":"ase000e80a3@hu1957f50c7e11323882:bd4f470b7f4c4cc9bb2ff453815b2dbf","requestId":"ase000e80a3@hu1957f50c7e11323882","sessionId":"cid000bb894@dx1957f50b7b0b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597755}
{"recordId":"gty000bb895@dx1957f50b7e9b8aa532:bd4f470b7f4c4cc9bb2ff453815b2dbf","requestId":"gty000bb895@dx1957f50b7e9b8aa532","sessionId":"cid000bb894@dx1957f50b7b0b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597756}
{"recordId":"gty000bb895@dx1957f50b7e9b8aa532:bd4f470b7f4c4cc9bb2ff453815b2dbf","requestId":"gty000bb895@dx1957f50b7e9b8aa532","sessionId":"cid000bb894@dx1957f50b7b0b8aa532","eof":"1","text":"赌神国语","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597756}
send data finished:1741597756.2938635
{"recordId":"gty000bb895@dx1957f50b7e9b8aa532:bd4f470b7f4c4cc9bb2ff453815b2dbf","requestId":"gty000bb895@dx1957f50b7e9b8aa532","sessionId":"cid000bb894@dx1957f50b7b0b8aa532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"赌神国语","intentId":"chat","intentName":"闲聊","nlg":"那这之后呢，再给我多说点吧。","shouldEndSession":true},"nlu":{"input":"赌神国语","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741597756}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid5559b6ad@dx55df1b29f83c3eef00"}
连接正常关闭
1741597756
param:b'{\n            "auth_id": "48680a0006dc4e63a06771f020b21f9b",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid17e616e7@dxe8d01b29f83c3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "ebef1c73f0e04bbe98f7919ca14ae823","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb10d@dx1957f50cd77b8a9532"}
started:
ws start
####################
测试进行: ctm00013149@hu17b4fb834340212902#46308369.pcm
{"recordId":"ase000e7688@hu1957f50e4d305bf882:ebef1c73f0e04bbe98f7919ca14ae823","requestId":"ase000e7688@hu1957f50e4d305bf882","sessionId":"cid000bb10d@dx1957f50cd77b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597762}
{"recordId":"gty000bb10e@dx1957f50cdbeb8a9532:ebef1c73f0e04bbe98f7919ca14ae823","requestId":"gty000bb10e@dx1957f50cdbeb8a9532","sessionId":"cid000bb10d@dx1957f50cd77b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597764}
{"recordId":"gty000bb10e@dx1957f50cdbeb8a9532:ebef1c73f0e04bbe98f7919ca14ae823","requestId":"gty000bb10e@dx1957f50cdbeb8a9532","sessionId":"cid000bb10d@dx1957f50cd77b8a9532","eof":"1","text":"关于海底两万里的故事","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597764}
send data finished:1741597764.3761783
{"recordId":"gty000bb10e@dx1957f50cdbeb8a9532:ebef1c73f0e04bbe98f7919ca14ae823","requestId":"gty000bb10e@dx1957f50cdbeb8a9532","sessionId":"cid000bb10d@dx1957f50cd77b8a9532","topic":"dm.output","skill":"有声节目","skillId":"IFLYTEK.audioProgram","speakUrl":"","error":{},"dm":{"input":"关于海底两万里的故事","intentId":"PLAY","intentName":"播放","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"关于海底两万里的故事","skill":"有声节目","skillId":"IFLYTEK.audioProgram","skillVersion":"187.0","semantics":{"request":{"slots":[{"name":"keyword","value":"海底两万里"},{"name":"category","value":"故事"}]}}},"timestamp":1741597764}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cida1a45c11@dxb0221b29f8443eef00"}
连接正常关闭
1741597764
param:b'{\n            "auth_id": "ad94dcf431e04a658ae16b4a53514c63",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidae7ad877@dxcf751b29f8443eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "ef22f21a40de419da0e276cf64105e3b","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb11c@dx1957f50ecf5b8a9532"}
started:
ws start
####################
测试进行: ctm0001314f@hu17b59b593890212902#46470179.pcm
{"recordId":"ase000e4ff8@hu1957f50feb005c3882:ef22f21a40de419da0e276cf64105e3b","requestId":"ase000e4ff8@hu1957f50feb005c3882","sessionId":"cid000bb11c@dx1957f50ecf5b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597769}
{"recordId":"gty000bb11d@dx1957f50ed3fb8a9532:ef22f21a40de419da0e276cf64105e3b","requestId":"gty000bb11d@dx1957f50ed3fb8a9532","sessionId":"cid000bb11c@dx1957f50ecf5b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597769}
{"recordId":"gty000bb11d@dx1957f50ed3fb8a9532:ef22f21a40de419da0e276cf64105e3b","requestId":"gty000bb11d@dx1957f50ed3fb8a9532","sessionId":"cid000bb11c@dx1957f50ecf5b8a9532","eof":"1","text":"调到下一集","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741597770}
send data finished:1741597770.0975928
{"recordId":"gty000bb11d@dx1957f50ed3fb8a9532:ef22f21a40de419da0e276cf64105e3b","requestId":"gty000bb11d@dx1957f50ed3fb8a9532","sessionId":"cid000bb11c@dx1957f50ecf5b8a9532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"调到下一集","intentId":"chat","intentName":"闲聊","nlg":"等我学习一下，再来回答吧！现在不如问点其他的吧。","shouldEndSession":true},"nlu":{"input":"调到下一集","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741597770}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidee31e2e1@dx80251b29f84a3eef00"}
连接正常关闭
1741597770
param:b'{\n            "auth_id": "2a325aee5dea472583057349ad6291f4",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid6e7f02e2@dxa3021b29f84a3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "eecc4e6655eb49c6822a1f4095073328","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bbb36@dx1957f51039a7844532"}
started:
ws start
####################
测试进行: ctm00013177@hu17b59b5a74c0212902#46470228.pcm
{"recordId":"ase000d157f@hu1957f5114730427882:eecc4e6655eb49c6822a1f4095073328","requestId":"ase000d157f@hu1957f5114730427882","sessionId":"cid000bbb36@dx1957f51039a7844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597775}
{"recordId":"gty000bbb37@dx1957f5103d47844532:eecc4e6655eb49c6822a1f4095073328","requestId":"gty000bbb37@dx1957f5103d47844532","sessionId":"cid000bbb36@dx1957f51039a7844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597775}
{"recordId":"gty000bbb37@dx1957f5103d47844532:eecc4e6655eb49c6822a1f4095073328","requestId":"gty000bbb37@dx1957f5103d47844532","sessionId":"cid000bbb36@dx1957f51039a7844532","eof":"1","text":"放郭德纲","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741597775}
send data finished:1741597775.4941816
{"recordId":"gty000bbb37@dx1957f5103d47844532:eecc4e6655eb49c6822a1f4095073328","requestId":"gty000bbb37@dx1957f5103d47844532","sessionId":"cid000bbb36@dx1957f51039a7844532","topic":"dm.output","skill":"有声节目","skillId":"IFLYTEK.audioProgram","speakUrl":"","error":{},"dm":{"input":"放郭德纲","intentId":"PLAY","intentName":"播放","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"放郭德纲","skill":"有声节目","skillId":"IFLYTEK.audioProgram","skillVersion":"187.0","semantics":{"request":{"slots":[{"name":"actor","value":"郭德纲"}]}}},"timestamp":1741597775}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cide7674932@dx9eda1b29f84f3eef00"}
连接正常关闭
1741597775
param:b'{\n            "auth_id": "0162e27de1d344b7a9fefa91e6a9c8b6",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"ciddaa457e3@dxef421b29f84f3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "7ee3e60a23c445c786b290d764953e9c","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb12c@dx1957f511816b8a9532"}
started:
ws start
####################
测试进行: ctm0001318e@hu17b59b5b54b0212902#46470267.pcm
{"recordId":"ase000da803@hu1957f5127fd04d3882:7ee3e60a23c445c786b290d764953e9c","requestId":"ase000da803@hu1957f5127fd04d3882","sessionId":"cid000bb12c@dx1957f511816b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597780}
{"recordId":"gty000bb12d@dx1957f511849b8a9532:7ee3e60a23c445c786b290d764953e9c","requestId":"gty000bb12d@dx1957f511849b8a9532","sessionId":"cid000bb12c@dx1957f511816b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597781}
{"recordId":"gty000bb12d@dx1957f511849b8a9532:7ee3e60a23c445c786b290d764953e9c","requestId":"gty000bb12d@dx1957f511849b8a9532","sessionId":"cid000bb12c@dx1957f511816b8a9532","eof":"1","text":"我要听单田芳的罗通扫北","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741597782}
send data finished:1741597782.1219666
{"recordId":"gty000bb12d@dx1957f511849b8a9532:7ee3e60a23c445c786b290d764953e9c","requestId":"gty000bb12d@dx1957f511849b8a9532","sessionId":"cid000bb12c@dx1957f511816b8a9532","topic":"dm.output","skill":"有声节目","skillId":"IFLYTEK.audioProgram","speakUrl":"","error":{},"dm":{"input":"我要听单田芳的罗通扫北","intentId":"PLAY","intentName":"播放","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"我要听单田芳的罗通扫北","skill":"有声节目","skillId":"IFLYTEK.audioProgram","skillVersion":"187.0","semantics":{"request":{"slots":[{"name":"name","value":"罗通扫北"},{"name":"actor","value":"单田芳"}]}}},"timestamp":1741597782}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cide7904285@dxc9ff1b29f8563eef00"}
连接正常关闭
1741597782
param:b'{\n            "auth_id": "427f1b41026c4faa82ca396af65efc8d",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid4105569d@dxf79d1b29f8563eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "52d70c73c2af497e8a1ae2765f4e84c1","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bbb40@dx1957f5131fe7844532"}
started:
ws start
####################
测试进行: ctm0001319b@hu17b59b5bc840212902#46470281.pcm
{"recordId":"ase000e9419@hu1957f5142871323882:52d70c73c2af497e8a1ae2765f4e84c1","requestId":"ase000e9419@hu1957f5142871323882","sessionId":"cid000bbb40@dx1957f5131fe7844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597786}
{"recordId":"gty000bbb41@dx1957f5132397844532:52d70c73c2af497e8a1ae2765f4e84c1","requestId":"gty000bbb41@dx1957f5132397844532","sessionId":"cid000bbb40@dx1957f5131fe7844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597787}
{"recordId":"gty000bbb41@dx1957f5132397844532:52d70c73c2af497e8a1ae2765f4e84c1","requestId":"gty000bbb41@dx1957f5132397844532","sessionId":"cid000bbb40@dx1957f5131fe7844532","eof":"1","text":"尽管可是怎么造句","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597787}
send data finished:1741597787.8871622
{"recordId":"gty000bbb41@dx1957f5132397844532:52d70c73c2af497e8a1ae2765f4e84c1","requestId":"gty000bbb41@dx1957f5132397844532","sessionId":"cid000bbb40@dx1957f5131fe7844532","topic":"dm.output","skill":"词典","skillId":"wordsDictionary","speakUrl":"","error":{},"dm":{"input":"尽管可是怎么造句","intentId":"SENTENCE_QUERY","intentName":"查询词语如何造句","nlg":"尽管可是常用作：尽管我非常努力地学习，可是成绩还是不理想。","shouldEndSession":true},"nlu":{"input":"尽管可是怎么造句","skill":"词典","skillId":"wordsDictionary","skillVersion":"196.0","semantics":{"request":{"slots":[{"name":"related","value":"尽管可是"}]}}},"timestamp":1741597787}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid578fb168@dx47561b29f85b3eef00"}
连接正常关闭
1741597787
param:b'{\n            "auth_id": "daed60094dcd467e9f75ead1407dbff2",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidd0d46186@dx52c11b29f85c3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "18bb671ea1df456fa96367288973fb1d","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb8be@dx1957f514869b8aa532"}
started:
ws start
####################
测试进行: ctm00013521@hu17b4fbfcfa30212902#46308926.pcm
{"recordId":"ase000d212e@hu1957f515edb0427882:18bb671ea1df456fa96367288973fb1d","requestId":"ase000d212e@hu1957f515edb0427882","sessionId":"cid000bb8be@dx1957f514869b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597794}
{"recordId":"gty000bb8bf@dx1957f5148a7b8aa532:18bb671ea1df456fa96367288973fb1d","requestId":"gty000bb8bf@dx1957f5148a7b8aa532","sessionId":"cid000bb8be@dx1957f514869b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597795}
{"recordId":"gty000bb8bf@dx1957f5148a7b8aa532:18bb671ea1df456fa96367288973fb1d","requestId":"gty000bb8bf@dx1957f5148a7b8aa532","sessionId":"cid000bb8be@dx1957f514869b8aa532","eof":"1","text":"杭州有什么经济类广播","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597795}
send data finished:1741597795.8013194
{"recordId":"gty000bb8bf@dx1957f5148a7b8aa532:18bb671ea1df456fa96367288973fb1d","requestId":"gty000bb8bf@dx1957f5148a7b8aa532","sessionId":"cid000bb8be@dx1957f514869b8aa532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"杭州有什么经济类广播","intentId":"chat","intentName":"闲聊","nlg":"惭愧惭愧，我好像并不知道有什么。","shouldEndSession":true},"nlu":{"input":"杭州有什么经济类广播","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741597795}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidb7e5143c@dxa4291b29f8633eef00"}
连接正常关闭
1741597796
param:b'{\n            "auth_id": "6303f7bbd2bb4cedba97d36eeb4688d3",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cide8e2c5f4@dx9eec1b29f8643eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "6ececb39ff3c441f811b5a7221bcdab5","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bbb54@dx1957f5167ec7844532"}
started:
ws start
####################
测试进行: ctm00013539@hu17b4fbfdbd20212902#46308964.pcm
{"recordId":"ase000e8e83@hu1957f517ed905bf882:6ececb39ff3c441f811b5a7221bcdab5","requestId":"ase000e8e83@hu1957f517ed905bf882","sessionId":"cid000bbb54@dx1957f5167ec7844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597802}
{"recordId":"gty000bbb55@dx1957f5168237844532:6ececb39ff3c441f811b5a7221bcdab5","requestId":"gty000bbb55@dx1957f5168237844532","sessionId":"cid000bbb54@dx1957f5167ec7844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597802}
{"recordId":"gty000bbb55@dx1957f5168237844532:6ececb39ff3c441f811b5a7221bcdab5","requestId":"gty000bbb55@dx1957f5168237844532","sessionId":"cid000bbb54@dx1957f5167ec7844532","eof":"1","text":"美元的汇率","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597802}
send data finished:1741597802.7997944
{"recordId":"gty000bbb55@dx1957f5168237844532:6ececb39ff3c441f811b5a7221bcdab5","requestId":"gty000bbb55@dx1957f5168237844532","sessionId":"cid000bbb54@dx1957f5167ec7844532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"美元的汇率","intentId":"chat","intentName":"闲聊","nlg":"等我学习一下，再来回答吧！现在不如问点其他的吧。","shouldEndSession":true},"nlu":{"input":"美元的汇率","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741597802}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid6920521f@dx48d81b29f86a3eef00"}
连接正常关闭
1741597803
param:b'{\n            "auth_id": "6480ea78a7b641d1968791d28bf5c158",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid8bac3035@dx5ad41b29f86b3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "032820102442465d9e34a50f81527312","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bbb5c@dx1957f51833f7844532"}
started:
ws start
####################
测试进行: ctm00013627@hu17b59bae0c30212902#46470995.pcm
{"recordId":"ase000fd4b8@hu1957f51936905c4882:032820102442465d9e34a50f81527312","requestId":"ase000fd4b8@hu1957f51936905c4882","sessionId":"cid000bbb5c@dx1957f51833f7844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597807}
{"recordId":"gty000bbb5d@dx1957f5183737844532:032820102442465d9e34a50f81527312","requestId":"gty000bbb5d@dx1957f5183737844532","sessionId":"cid000bbb5c@dx1957f51833f7844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597808}
{"recordId":"gty000bbb5d@dx1957f5183737844532:032820102442465d9e34a50f81527312","requestId":"gty000bbb5d@dx1957f5183737844532","sessionId":"cid000bbb5c@dx1957f51833f7844532","eof":"1","text":"我想听小星星","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597808}
send data finished:1741597808.4801042
{"recordId":"gty000bbb5d@dx1957f5183737844532:032820102442465d9e34a50f81527312","requestId":"gty000bbb5d@dx1957f5183737844532","sessionId":"cid000bbb5c@dx1957f51833f7844532","topic":"dm.output","skill":"儿歌","skillId":"2019031500001056","speakUrl":"","error":{},"dm":{"input":"我想听小星星","intentId":"QUERY_BY_SONG","intentName":"根据歌曲名点播儿歌","nlg":"没有找到合适内容","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"我想听小星星","skill":"儿歌","skillId":"2019031500001056","skillVersion":"8","semantics":{"request":{"entrypoint":"ent","hazard":false,"score":1,"slots":[{"name":"歌曲名","value":"小星星"}],"template":"{want}听{song}"}}},"timestamp":1741597808}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid9646ce7a@dx223a1b29f8703eef00"}
连接正常关闭
1741597808
param:b'{\n            "auth_id": "b62c0e518b544e698d1210afa4104780",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidbe378b41@dxb6841b29f8703eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "67efc9f8580c45a79421a1954e9d32d0","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bbb62@dx1957f5198f47844532"}
started:
ws start
####################
测试进行: ctm00013631@hu17b59bae5ad0212902#46471012.pcm
{"recordId":"ase000d2d21@hu1957f51aa3a0427882:67efc9f8580c45a79421a1954e9d32d0","requestId":"ase000d2d21@hu1957f51aa3a0427882","sessionId":"cid000bbb62@dx1957f5198f47844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597813}
{"recordId":"gty000bbb63@dx1957f5199297844532:67efc9f8580c45a79421a1954e9d32d0","requestId":"gty000bbb63@dx1957f5199297844532","sessionId":"cid000bbb62@dx1957f5198f47844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597815}
{"recordId":"gty000bbb63@dx1957f5199297844532:67efc9f8580c45a79421a1954e9d32d0","requestId":"gty000bbb63@dx1957f5199297844532","sessionId":"cid000bbb62@dx1957f5198f47844532","eof":"1","text":"不好意思没有了用英语怎么说","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597815}
send data finished:1741597815.5105057
{"recordId":"gty000bbb63@dx1957f5199297844532:67efc9f8580c45a79421a1954e9d32d0","requestId":"gty000bbb63@dx1957f5199297844532","sessionId":"cid000bbb62@dx1957f5198f47844532","topic":"dm.output","skill":"translation","skillId":"translation","speakUrl":"","error":{},"dm":{"input":"不好意思没有了用英语怎么说","intentId":"TRANSLATION","intentName":"查询","nlg":"Sorry, no more","shouldEndSession":true},"nlu":{"input":"不好意思没有了用英语怎么说","skill":"translation","skillId":"translation","skillVersion":"166.0","semantics":{"request":{"slots":[{"name":"content","value":"不好意思没有了"},{"name":"source","value":"cn"},{"name":"target","value":"en"}]}}},"timestamp":1741597815}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidbd5928be@dx03461b29f8773eef00"}
连接正常关闭
1741597815
param:b'{\n            "auth_id": "1c43deec51a84108b99e91b16f30e3a9",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidc66e3086@dx7df41b29f8773eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "1e1a04c0c2884dbca88e533b93d5f0a2","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb9a1@dx1957f51b507b86a532"}
started:
ws start
####################
测试进行: ctm0001363a@hu17b59baeb6e0212902#46471018.pcm
{"recordId":"ase000dc037@hu1957f51c4fa04d3882:1e1a04c0c2884dbca88e533b93d5f0a2","requestId":"ase000dc037@hu1957f51c4fa04d3882","sessionId":"cid000bb9a1@dx1957f51b507b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597820}
{"recordId":"gty000bb9a2@dx1957f51b538b86a532:1e1a04c0c2884dbca88e533b93d5f0a2","requestId":"gty000bb9a2@dx1957f51b538b86a532","sessionId":"cid000bb9a1@dx1957f51b507b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597821}
{"recordId":"gty000bb9a2@dx1957f51b538b86a532:1e1a04c0c2884dbca88e533b93d5f0a2","requestId":"gty000bb9a2@dx1957f51b538b86a532","sessionId":"cid000bb9a1@dx1957f51b507b86a532","eof":"1","text":"用俄语翻译你很棒","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597821}
send data finished:1741597821.4275131
{"recordId":"gty000bb9a2@dx1957f51b538b86a532:1e1a04c0c2884dbca88e533b93d5f0a2","requestId":"gty000bb9a2@dx1957f51b538b86a532","sessionId":"cid000bb9a1@dx1957f51b507b86a532","topic":"dm.output","skill":"translation","skillId":"translation","speakUrl":"","error":{},"dm":{"input":"用俄语翻译你很棒","intentId":"TRANSLATION","intentName":"查询","nlg":"Ты молодец. ","shouldEndSession":true},"nlu":{"input":"用俄语翻译你很棒","skill":"translation","skillId":"translation","skillVersion":"166.0","semantics":{"request":{"slots":[{"name":"source","value":"cn"},{"name":"target","value":"ru"},{"name":"content","value":"你很棒"}]}}},"timestamp":1741597821}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidb85e138e@dx24961b29f87d3eef00"}
连接正常关闭
1741597821
param:b'{\n            "auth_id": "25a89d104173425fba84140a4458d0f5",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid210b01ff@dx3fe31b29f87d3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "da2deb763e1b4f2ba25de76def8f17c2","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bbb70@dx1957f51cc337844532"}
started:
ws start
####################
测试进行: ctm00013642@hu17b59baf07f0212902#46471027.pcm
{"recordId":"ase000e9d6d@hu1957f51de7105bf882:da2deb763e1b4f2ba25de76def8f17c2","requestId":"ase000e9d6d@hu1957f51de7105bf882","sessionId":"cid000bbb70@dx1957f51cc337844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597826}
{"recordId":"gty000bbb71@dx1957f51cd597844532:da2deb763e1b4f2ba25de76def8f17c2","requestId":"gty000bbb71@dx1957f51cd597844532","sessionId":"cid000bbb70@dx1957f51cc337844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597827}
{"recordId":"gty000bbb71@dx1957f51cd597844532:da2deb763e1b4f2ba25de76def8f17c2","requestId":"gty000bbb71@dx1957f51cd597844532","sessionId":"cid000bbb70@dx1957f51cc337844532","eof":"1","text":"360股票信息","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597827}
send data finished:1741597827.8735363
{"recordId":"gty000bbb71@dx1957f51cd597844532:da2deb763e1b4f2ba25de76def8f17c2","requestId":"gty000bbb71@dx1957f51cd597844532","sessionId":"cid000bbb70@dx1957f51cc337844532","topic":"dm.output","skill":"股票","skillId":"IFLYTEK.stock","speakUrl":"","error":{},"dm":{"input":"360股票信息","intentId":"QUERY","intentName":"查股票的当前价、走势、K线图或详情等信息","nlg":"当前时间股市已收盘，今日“三六零”收盘价为11.54元，下跌2.037%。","shouldEndSession":true},"nlu":{"input":"360股票信息","skill":"股票","skillId":"IFLYTEK.stock","skillVersion":"375.0","semantics":{"request":{"slots":[{"name":"code","value":"601360"},{"name":"market","value":"sh"},{"name":"name","value":"三六零"}]}}},"timestamp":1741597827}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid280ccb15@dx273c1b29f8833eef00"}
连接正常关闭
1741597827
param:b'{\n            "auth_id": "389f18f5fd00403086ff8f622d5337f1",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid68d34f28@dx876c1b29f8843eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "bc153d30fad84a7cb3703d081f66ef63","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb14f@dx1957f51e499b8a9532"}
started:
ws start
####################
测试进行: ctm0001364c@hu17b59baf72e0212902#46471037.pcm
{"recordId":"ase000fe3cb@hu1957f51f4b205c4882:bc153d30fad84a7cb3703d081f66ef63","requestId":"ase000fe3cb@hu1957f51f4b205c4882","sessionId":"cid000bb14f@dx1957f51e499b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597832}
{"recordId":"gty000bb150@dx1957f51e4d8b8a9532:bc153d30fad84a7cb3703d081f66ef63","requestId":"gty000bb150@dx1957f51e4d8b8a9532","sessionId":"cid000bb14f@dx1957f51e499b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597833}
{"recordId":"gty000bb150@dx1957f51e4d8b8a9532:bc153d30fad84a7cb3703d081f66ef63","requestId":"gty000bb150@dx1957f51e4d8b8a9532","sessionId":"cid000bb14f@dx1957f51e499b8a9532","eof":"1","text":"西部矿业股票","pinyin":"","topic":"asr.speech.result","age":"elder","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597833}
send data finished:1741597833.4094436
{"recordId":"gty000bb150@dx1957f51e4d8b8a9532:bc153d30fad84a7cb3703d081f66ef63","requestId":"gty000bb150@dx1957f51e4d8b8a9532","sessionId":"cid000bb14f@dx1957f51e499b8a9532","topic":"dm.output","skill":"股票","skillId":"IFLYTEK.stock","speakUrl":"","error":{},"dm":{"input":"西部矿业股票","intentId":"QUERY","intentName":"查股票的当前价、走势、K线图或详情等信息","nlg":"当前时间股市已收盘，今日“西部矿业”收盘价为17.32元，上涨2.002%。","shouldEndSession":true},"nlu":{"input":"西部矿业股票","skill":"股票","skillId":"IFLYTEK.stock","skillVersion":"375.0","semantics":{"request":{"slots":[{"name":"market","value":"sh"},{"name":"name","value":"西部矿业"},{"name":"code","value":"601168"}]}}},"timestamp":1741597833}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid962584a5@dx6c331b29f8893eef00"}
连接正常关闭
1741597833
param:b'{\n            "auth_id": "5b10599747ed4adb9428d6decdb3cba4",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid6c2a5c7d@dxdd401b29f8893eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "1d17caf44da64ce0a7aa84ea4ea2cf21","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bbb7c@dx1957f51fa707844532"}
started:
ws start
####################
测试进行: ctm00013e1e@hu17b57c93361020c902#46419937.pcm
{"recordId":"ase000eb3f1@hu1957f520bdc1323882:1d17caf44da64ce0a7aa84ea4ea2cf21","requestId":"ase000eb3f1@hu1957f520bdc1323882","sessionId":"cid000bbb7c@dx1957f51fa707844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597838}
{"recordId":"gty000bbb7d@dx1957f51faad7844532:1d17caf44da64ce0a7aa84ea4ea2cf21","requestId":"gty000bbb7d@dx1957f51faad7844532","sessionId":"cid000bbb7c@dx1957f51fa707844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597840}
{"recordId":"gty000bbb7d@dx1957f51faad7844532:1d17caf44da64ce0a7aa84ea4ea2cf21","requestId":"gty000bbb7d@dx1957f51faad7844532","sessionId":"cid000bbb7c@dx1957f51fa707844532","eof":"1","text":"现在播放最火的电视剧是什么","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597840}
send data finished:1741597840.7861676
{"recordId":"gty000bbb7d@dx1957f51faad7844532:1d17caf44da64ce0a7aa84ea4ea2cf21","requestId":"gty000bbb7d@dx1957f51faad7844532","sessionId":"cid000bbb7c@dx1957f51fa707844532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"现在播放最火的电视剧是什么","intentId":"chat","intentName":"闲聊","nlg":"惭愧惭愧，我好像并不知道是什么。","shouldEndSession":true},"nlu":{"input":"现在播放最火的电视剧是什么","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741597840}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidb7d09058@dx971a1b29f8903eef00"}
连接正常关闭
1741597841
param:b'{\n            "auth_id": "c29fe2d748874f92a0740fe999eef1bc",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid09d3ce10@dxe5741b29f8913eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "4cd3d87aa47b4a57b529bb8668f227eb","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb90a@dx1957f5217b0b8aa532"}
started:
ws start
####################
测试进行: ctm00013e37@hu17b57c93e30020c902#46419955.pcm
{"recordId":"ase000d4112@hu1957f5227850427882:4cd3d87aa47b4a57b529bb8668f227eb","requestId":"ase000d4112@hu1957f5227850427882","sessionId":"cid000bb90a@dx1957f5217b0b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597845}
{"recordId":"gty000bb90b@dx1957f5217e2b8aa532:4cd3d87aa47b4a57b529bb8668f227eb","requestId":"gty000bb90b@dx1957f5217e2b8aa532","sessionId":"cid000bb90a@dx1957f5217b0b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597846}
{"recordId":"gty000bb90b@dx1957f5217e2b8aa532:4cd3d87aa47b4a57b529bb8668f227eb","requestId":"gty000bb90b@dx1957f5217e2b8aa532","sessionId":"cid000bb90a@dx1957f5217b0b8aa532","eof":"1","text":"10分钟后提醒","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597846}
send data finished:1741597846.3599842
{"recordId":"gty000bb90b@dx1957f5217e2b8aa532:4cd3d87aa47b4a57b529bb8668f227eb","requestId":"gty000bb90b@dx1957f5217e2b8aa532","sessionId":"cid000bb90a@dx1957f5217b0b8aa532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"10分钟后提醒","intentId":"chat","intentName":"闲聊","nlg":"这个我不太懂，我们聊聊别的吧。","shouldEndSession":true},"nlu":{"input":"10分钟后提醒","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741597846}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidad6623c1@dxad031b29f8963eef00"}
连接正常关闭
1741597846
param:b'{\n            "auth_id": "c6b637ef09ea498aa372d799c430c140",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cida1d4efdb@dx632c1b29f8963eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "3b4fbdeace6847ffafc39ace9ac89151","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb915@dx1957f522d87b8aa532"}
started:
ws start
####################
测试进行: ctm00013e41@hu17b57c943df020c902#46419966.pcm
{"recordId":"ase000d44cf@hu1957f523e330427882:3b4fbdeace6847ffafc39ace9ac89151","requestId":"ase000d44cf@hu1957f523e330427882","sessionId":"cid000bb915@dx1957f522d87b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597851}
{"recordId":"gty000bb916@dx1957f522dc6b8aa532:3b4fbdeace6847ffafc39ace9ac89151","requestId":"gty000bb916@dx1957f522dc6b8aa532","sessionId":"cid000bb915@dx1957f522d87b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597852}
{"recordId":"gty000bb916@dx1957f522dc6b8aa532:3b4fbdeace6847ffafc39ace9ac89151","requestId":"gty000bb916@dx1957f522dc6b8aa532","sessionId":"cid000bb915@dx1957f522d87b8aa532","eof":"1","text":"接下来两天的天气","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597852}
send data finished:1741597852.8374527
{"recordId":"gty000bb916@dx1957f522dc6b8aa532:3b4fbdeace6847ffafc39ace9ac89151","requestId":"gty000bb916@dx1957f522dc6b8aa532","sessionId":"cid000bb915@dx1957f522d87b8aa532","topic":"dm.output","skill":"天气","skillId":"2019042500000544","speakUrl":"","error":{},"dm":{"input":"接下来两天的天气","intentId":"QUERY","intentName":"查询天气信息","nlg":"北京市接下来两天，霾晴转多云，天气温凉，风不大，整体空气质量轻微污染","widget":{"webhookResp":{"result":[{"airData":144,"airQuality":"轻微污染","city":"北京市","date":"2025-03-10","dateLong":1741536000,"date_for_voice":"今天","exp":{"ct":{"expName":"穿衣指数","level":"温凉","prompt":"天气偏凉，增加衣物厚度。"},"dy":{"expName":"钓鱼指数","level":"不适宜","prompt":"雾霾天气，不适宜外出钓鱼。"},"gm":{"expName":"感冒指数","level":"易发","prompt":"感冒容易发生，少去人群密集的场所有利于降低感冒的几率。"},"jt":{"expName":"交通指数","level":"良好","prompt":"天气较好，路面干燥，交通气象条件良好，车辆可以正常行驶。"},"tr":{"expName":"旅游指数","level":"适宜","prompt":"天气较好，但丝毫不会影响您出行的心情。温度适宜又有微风相伴，适宜旅游。"},"uv":{"expName":"紫外线指数","level":"弱","prompt":"辐射较弱，请涂擦SPF12-15、PA+护肤品。"},"xc":{"expName":"洗车指数","level":"较适宜","prompt":"较适宜洗车，未来一天无雨，风力较小，擦洗一新的汽车至少能保持一天。"},"yd":{"expName":"运动指数","level":"不适宜","prompt":"空气轻度污染，不宜在户外运动。"}},"extra":"","humidity":"42%","img":"http://cdn9002.iflyos.cn/osweathericon/53.png","lastUpdateTime":"2025-03-10 17:00:08","pm25":"144","precipitation":"0","sunRise":"2025-03-10 06:34:00","sunSet":"2025-03-10 18:16:00","temp":15,"tempHigh":"17℃","tempLow":"7℃","tempRange":"7℃ ~ 17℃","tempReal":"12℃","visibility":"","warning":"","weather":"霾","weatherDescription":"有点凉。","weatherDescription3":"2℃到17℃，风不大，有点凉。","weatherDescription7":"-1℃到8℃，风不大，有点冷。","weatherType":53,"week":"周一","wind":"西南风3-4级","windLevel":0},{"airData":143,"airQuality":"轻微污染","city":"北京市","date":"2025-03-09","dateLong":1741449600,"date_for_voice":"昨天","humidity":"42%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-03-10 17:00:08","sunRise":"2025-03-09 06:35:00","sunSet":"2025-03-09 18:14:00","tempHigh":"17℃","tempLow":"7℃","tempRange":"7℃ ~ 17℃","weather":"晴转多云","weatherDescription":"有点凉。","weatherType":0,"week":"周日","wind":"西南风转南风3-4级","windLevel":1},{"airData":180,"airQuality":"轻度污染","city":"北京市","date":"2025-03-11","dateLong":1741622400,"date_for_voice":"明天","humidity":"55%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-03-10 17:00:08","sunRise":"2025-03-11 06:32:00","sunSet":"2025-03-11 18:17:00","tempHigh":"16℃","tempLow":"5℃","tempRange":"5℃ ~ 16℃","weather":"多云转浮尘","weatherDescription":"有点凉。","weatherType":1,"week":"周二","wind":"西风转西北风微风","windLevel":0},{"airData":90,"airQuality":"良","city":"北京市","date":"2025-03-12","dateLong":1741708800,"date_for_voice":"后天","humidity":"16%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-03-10 17:00:08","sunRise":"2025-03-12 06:31:00","sunSet":"2025-03-12 18:18:00","tempHigh":"16℃","tempLow":"2℃","tempRange":"2℃ ~ 16℃","weather":"晴","weatherDescription":"有点凉。","weatherType":0,"week":"周三","wind":"西北风转东南风3-4级","windLevel":1},{"airData":70,"airQuality":"良","city":"北京市","date":"2025-03-13","dateLong":1741795200,"date_for_voice":"13号","humidity":"21%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-03-10 17:00:08","sunRise":"2025-03-13 06:29:00","sunSet":"2025-03-13 18:19:00","tempHigh":"15℃","tempLow":"3℃","tempRange":"3℃ ~ 15℃","weather":"多云","weatherDescription":"有点凉。","weatherType":1,"week":"周四","wind":"西南风转东南风3-4级","windLevel":1},{"airData":90,"airQuality":"良","city":"北京市","date":"2025-03-14","dateLong":1741881600,"date_for_voice":"14号","humidity":"41%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-03-10 17:00:08","sunRise":"2025-03-14 06:27:00","sunSet":"2025-03-14 18:20:00","tempHigh":"10℃","tempLow":"3℃","tempRange":"3℃ ~ 10℃","weather":"多云转阴","weatherDescription":"有点冷。","weatherType":1,"week":"周五","wind":"东北风微风","windLevel":0},{"airData":60,"airQuality":"良","city":"北京市","date":"2025-03-15","dateLong":1741968000,"date_for_voice":"15号","humidity":"47%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-03-10 17:00:08","sunRise":"2025-03-15 06:26:00","sunSet":"2025-03-15 18:21:00","tempHigh":"8℃","tempLow":"1℃","tempRange":"1℃ ~ 8℃","weather":"阴","weatherDescription":"有点冷。","weatherType":2,"week":"周六","wind":"东北风转西北风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-16","dateLong":1742054400,"date_for_voice":"16号","humidity":"38%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-03-10 17:00:08","sunRise":"2025-03-16 06:24:00","sunSet":"2025-03-16 18:22:00","tempHigh":"8℃","tempLow":"-1℃","tempRange":"-1℃ ~ 8℃","weather":"多云","weatherDescription":"有点冷。","weatherType":1,"week":"周日","wind":"西北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-17","dateLong":1742140800,"date_for_voice":"17号","humidity":"27%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-03-10 17:00:08","sunRise":"2025-03-17 06:23:00","sunSet":"2025-03-17 18:23:00","tempHigh":"12℃","tempLow":"0℃","tempRange":"0℃ ~ 12℃","weather":"多云转晴","weatherDescription":"有点冷。","weatherType":1,"week":"下周一","wind":"西北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-18","dateLong":1742227200,"date_for_voice":"18号","humidity":"29%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-03-10 17:00:08","sunRise":"2025-03-18 06:21:00","sunSet":"2025-03-18 18:24:00","tempHigh":"9℃","tempLow":"1℃","tempRange":"1℃ ~ 9℃","weather":"晴","weatherDescription":"有点冷。","weatherType":0,"week":"下周二","wind":"西北风转西南风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-19","dateLong":1742313600,"date_for_voice":"19号","humidity":"29%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-03-10 17:00:08","sunRise":"2025-03-19 06:19:00","sunSet":"2025-03-19 18:25:00","tempHigh":"18℃","tempLow":"4℃","tempRange":"4℃ ~ 18℃","weather":"晴转阴","weatherDescription":"有点凉。","weatherType":0,"week":"下周三","wind":"西北风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-20","dateLong":1742400000,"date_for_voice":"20号","humidity":"26%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-03-10 17:00:08","sunRise":"2025-03-20 06:18:00","sunSet":"2025-03-20 18:26:00","tempHigh":"18℃","tempLow":"7℃","tempRange":"7℃ ~ 18℃","weather":"阴转小雨","weatherDescription":"有点凉。","weatherType":2,"week":"下周四","wind":"东南风转西南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-21","dateLong":1742486400,"date_for_voice":"21号","humidity":"31%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-03-10 17:00:08","sunRise":"2025-03-21 06:16:00","sunSet":"2025-03-21 18:27:00","tempHigh":"16℃","tempLow":"7℃","tempRange":"7℃ ~ 16℃","weather":"阴转小雨","weatherDescription":"有点凉。","weatherType":2,"week":"下周五","wind":"西南风转东南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-22","dateLong":1742572800,"date_for_voice":"22号","humidity":"38%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-03-10 17:00:08","sunRise":"2025-03-22 06:15:00","sunSet":"2025-03-22 18:28:00","tempHigh":"24℃","tempLow":"7℃","tempRange":"7℃ ~ 24℃","weather":"晴转多云","weatherDescription":"气候温暖。","weatherType":0,"week":"下周六","wind":"东北风转西北风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-23","dateLong":1742659200,"date_for_voice":"23号","humidity":"32%","img":"http://cdn9002.iflyos.cn/osweathericon/07.png","lastUpdateTime":"2025-03-10 17:00:08","sunRise":"2025-03-23 06:13:00","sunSet":"2025-03-23 18:29:00","tempHigh":"26℃","tempLow":"4℃","tempRange":"4℃ ~ 26℃","weather":"小雨转晴","weatherDescription":"气候温暖。","weatherType":7,"week":"下周日","wind":"东北风转西北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-24","dateLong":1742745600,"date_for_voice":"24号","humidity":"18%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-03-10 17:00:08","sunRise":"2025-03-24 06:11:00","sunSet":"2025-03-24 18:30:00","tempHigh":"14℃","tempLow":"2℃","tempRange":"2℃ ~ 14℃","weather":"晴转阴","weatherDescription":"有点凉。","weatherType":0,"week":"下下周一","wind":"西南风转西北风3-4级","windLevel":1}]},"cityName":"北京市"},"dm_intent":"custom","cityName":"北京市","shouldEndSession":true},"nlu":{"input":"接下来两天的天气","skill":"天气","skillId":"2019042500000544","skillVersion":"56","semantics":{"request":{"intent":"QUERY","slots":[{"name":"location.city","normValue":"CURRENT_CITY","value":"CURRENT_CITY"},{"name":"location.poi","normValue":"CURRENT_POI","value":"CURRENT_POI"},{"name":"location.type","normValue":"LOC_POI","value":"LOC_POI"},{"name":"queryType","value":"内容"},{"name":"subfocus","value":"天气状态"},{"name":"datetime","normValue":"{\"datetime\":\"\",\"suggestDatetime\":\"\"}","value":"接下来两天"}]}}},"timestamp":1741597852}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid25efa29d@dxe9411b29f89c3eef00"}
连接正常关闭
1741597852
param:b'{\n            "auth_id": "2e07ce28e28e4f82937d11f0657e8240",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidf446efc7@dx0d221b29f89d3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "c2393629b784444c9b3809e628ebfff0","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb166@dx1957f524667b8a9532"}
started:
ws start
####################
测试进行: ctm000140ec@hu17b681bfd9c020c902#46638991.pcm
{"recordId":"gty000bb167@dx1957f5246a3b8a9532:c2393629b784444c9b3809e628ebfff0","requestId":"gty000bb167@dx1957f5246a3b8a9532","sessionId":"cid000bb166@dx1957f524667b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597857}
{"recordId":"gty000bb167@dx1957f5246a3b8a9532:c2393629b784444c9b3809e628ebfff0","requestId":"gty000bb167@dx1957f5246a3b8a9532","sessionId":"cid000bb166@dx1957f524667b8a9532","eof":"1","text":"听新闻","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741597857}
send data finished:1741597857.8184183
{"recordId":"gty000bb167@dx1957f5246a3b8a9532:c2393629b784444c9b3809e628ebfff0","requestId":"gty000bb167@dx1957f5246a3b8a9532","sessionId":"cid000bb166@dx1957f524667b8a9532","topic":"dm.output","skill":"新闻","skillId":"2019031900001180","speakUrl":"","error":{},"dm":{"input":"听新闻","intentId":"PLAY","intentName":"听新闻","nlg":"可以了解下3月10日人本生活资讯晚报：全国政协委员张凯丽：建议大力打击谣言黑产；哪吒2英配版将发行院线","widget":{"content":[{"album":"新闻","title":"3月10日人本生活资讯晚报：全国政协委员张凯丽：建议大力打击谣言黑产；哪吒2英配版将发行院线","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202503101610A6F32914097BFB52019E023671AEF9BF_64.mp3?pf=OH9GI&vid=11723586&tm=1741597857904&pid=1898262","extra":{"source":""}},{"album":"新闻","title":"减肥4年后绝大多数人恢复之前体重","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2025031015223F38235525E04BCD655F1A1FC53E5265_64.mp3?pf=OH9GI&vid=11723578&tm=1741597857904&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"人生最容易长胖的5个时刻","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202503101518265A9818823909625DF2F29B090E4595_64.mp3?pf=OH9GI&vid=11723563&tm=1741597857904&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"蹲便冲水细菌是坐便的2倍","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2025031015130EA1D3CDD6B6C8FFED07928B1EF5D96C_64.mp3?pf=OH9GI&vid=11723562&tm=1741597857904&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"代表建议加大医美行业监管力度﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202503101455893E4D847DE5CC6E99894AAF5C6287C6_64.mp3?pf=OH9GI&vid=11723554&tm=1741597857904&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"青少年需要身体能出汗的体育课﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2025031014558DC7F45429AA8B9EEFB93689A0B4901E_64.mp3?pf=OH9GI&vid=11723548&tm=1741597857904&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"防腐剂脱氢乙酸钠全面禁用是谣言﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202503101455491F9B10CADDCD42524F8ED3283E9F7C_64.mp3?pf=OH9GI&vid=11723547&tm=1741597857904&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"《纽约时报》急问为什么小米可以造电动车，苹果却不能？","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202503101437E72886650ABC5F2085FD1CC6EFAA7474_64.mp3?pf=OH9GI&vid=11723538&tm=1741597857904&pid=194144","extra":{"source":"fm"}},{"album":"新闻","title":"养老服务向全体老年人拓展﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2025031012542B3AF8EDDF8118971866627D50AA16AF_64.mp3?pf=OH9GI&vid=11723480&tm=1741597857904&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"加拿大称将继续对美征收报复性关税﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/20250310125416F7FCBFA3CCE31C40BE0A772F4D0E45_64.mp3?pf=OH9GI&vid=11723476&tm=1741597857904&pid=1897566","extra":{"source":"fm"}}]},"shouldEndSession":true},"nlu":{"input":"听新闻","skill":"新闻","skillId":"2019031900001180","skillVersion":"87","semantics":{"request":{"slots":[]}}},"timestamp":1741597857}
{"recordId":"ase000f23da@hu1957f5258bc05c0882:c2393629b784444c9b3809e628ebfff0","requestId":"ase000f23da@hu1957f5258bc05c0882","sessionId":"cid000bb166@dx1957f524667b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597858}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidf6248cb7@dx116a1b29f8a23eef00"}
连接正常关闭
1741597858
param:b'{\n            "auth_id": "09cdb828e8eb4d9ea0c38b758e3c5242",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidab117243@dx3cd41b29f8a23eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "31e68f2d0dc94a3ba78e03a1233f8f5b","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb923@dx1957f525aa2b8aa532"}
started:
ws start
####################
测试进行: ctm000140fa@hu17b681c0398020c902#46639006.pcm
{"recordId":"ase000e89ae@hu1957f526c0405c3882:31e68f2d0dc94a3ba78e03a1233f8f5b","requestId":"ase000e89ae@hu1957f526c0405c3882","sessionId":"cid000bb923@dx1957f525aa2b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597863}
{"recordId":"gty000bb924@dx1957f525ad8b8aa532:31e68f2d0dc94a3ba78e03a1233f8f5b","requestId":"gty000bb924@dx1957f525ad8b8aa532","sessionId":"cid000bb923@dx1957f525aa2b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597863}
{"recordId":"gty000bb924@dx1957f525ad8b8aa532:31e68f2d0dc94a3ba78e03a1233f8f5b","requestId":"gty000bb924@dx1957f525ad8b8aa532","sessionId":"cid000bb923@dx1957f525aa2b8aa532","eof":"1","text":"播放琵琶名曲","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741597863}
send data finished:1741597863.8182588
{"recordId":"gty000bb924@dx1957f525ad8b8aa532:31e68f2d0dc94a3ba78e03a1233f8f5b","requestId":"gty000bb924@dx1957f525ad8b8aa532","sessionId":"cid000bb923@dx1957f525aa2b8aa532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"播放琵琶名曲","intentId":"PLAY","intentName":"点歌","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"播放琵琶名曲","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"乐器","value":"琵琶"},{"name":"标签","value":"琵琶|名曲"}]}}},"timestamp":1741597863}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid6a8a181c@dxfc001b29f8a73eef00"}
连接正常关闭
1741597863
param:b'{\n            "auth_id": "3dfa2e815cf446628c7168207de2bcb7",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidd52c4d00@dxc7e71b29f8a83eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "407774b8094b4c9798edcf6399d0de17","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb173@dx1957f52712bb8a9532"}
started:
ws start
####################
测试进行: ctm00014108@hu17b681c0e2a020c902#46639027.pcm
{"recordId":"ase000ec7b5@hu1957f528be71323882:407774b8094b4c9798edcf6399d0de17","requestId":"ase000ec7b5@hu1957f528be71323882","sessionId":"cid000bb173@dx1957f52712bb8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597871}
{"recordId":"gty000bb174@dx1957f52715cb8a9532:407774b8094b4c9798edcf6399d0de17","requestId":"gty000bb174@dx1957f52715cb8a9532","sessionId":"cid000bb173@dx1957f52712bb8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597871}
{"recordId":"gty000bb174@dx1957f52715cb8a9532:407774b8094b4c9798edcf6399d0de17","requestId":"gty000bb174@dx1957f52715cb8a9532","sessionId":"cid000bb173@dx1957f52712bb8a9532","eof":"1","text":"来一首我怀念的","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741597871}
send data finished:1741597871.9629743
{"recordId":"gty000bb174@dx1957f52715cb8a9532:407774b8094b4c9798edcf6399d0de17","requestId":"gty000bb174@dx1957f52715cb8a9532","sessionId":"cid000bb173@dx1957f52712bb8a9532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"来一首我怀念的","intentId":"PLAY","intentName":"点歌","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"来一首我怀念的","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"歌曲名","value":"我怀念的"}]}}},"timestamp":1741597871}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid03504575@dxb8541b29f8af3eef00"}
连接正常关闭
1741597872
param:b'{\n            "auth_id": "c09eeed0ee174edbbf55a6216c64d310",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid49f30816@dxbe521b29f8b03eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "2c6386ba149e4a4bafda7c9f394bf87f","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb940@dx1957f5290e0b8aa532"}
started:
ws start
####################
测试进行: ctm00014473@hu17b5906453b020c902#46446244.pcm
{"recordId":"ase000ecc1e@hu1957f52a8261323882:2c6386ba149e4a4bafda7c9f394bf87f","requestId":"ase000ecc1e@hu1957f52a8261323882","sessionId":"cid000bb940@dx1957f5290e0b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597878}
{"recordId":"gty000bb941@dx1957f52911db8aa532:2c6386ba149e4a4bafda7c9f394bf87f","requestId":"gty000bb941@dx1957f52911db8aa532","sessionId":"cid000bb940@dx1957f5290e0b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597879}
{"recordId":"gty000bb941@dx1957f52911db8aa532:2c6386ba149e4a4bafda7c9f394bf87f","requestId":"gty000bb941@dx1957f52911db8aa532","sessionId":"cid000bb940@dx1957f5290e0b8aa532","eof":"1","text":"王传喜用英语怎么说","pinyin":"","topic":"asr.speech.result","age":"child","ageScore":"","gender":"","genderScore":"","language_class":"mandarin","timestamp":1741597880}
send data finished:1741597880.1368642
{"recordId":"gty000bb941@dx1957f52911db8aa532:2c6386ba149e4a4bafda7c9f394bf87f","requestId":"gty000bb941@dx1957f52911db8aa532","sessionId":"cid000bb940@dx1957f5290e0b8aa532","topic":"dm.output","skill":"translation","skillId":"translation","speakUrl":"","error":{},"dm":{"input":"王传喜用英语怎么说","intentId":"TRANSLATION","intentName":"查询","nlg":"Wang Chuanxi","shouldEndSession":true},"nlu":{"input":"王传喜用英语怎么说","skill":"translation","skillId":"translation","skillVersion":"166.0","semantics":{"request":{"slots":[{"name":"content","value":"王传喜"},{"name":"source","value":"cn"},{"name":"target","value":"en"}]}}},"timestamp":1741597880}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid9141817d@dx9aa41b29f8b83eef00"}
连接正常关闭
1741597880
param:b'{\n            "auth_id": "92bbb9bbf7004fa8b2fbc946faaf530d",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid1fdecb69@dxc4c51b29f8b83eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "e7ff3923072d483cbf89767bdbba177c","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb9e4@dx1957f52b172b86a532"}
started:
ws start
####################
测试进行: ctm00014485@hu17b5906563a020c902#46446259.pcm
{"recordId":"ase000f35b4@hu1957f52c9b605c0882:e7ff3923072d483cbf89767bdbba177c","requestId":"ase000f35b4@hu1957f52c9b605c0882","sessionId":"cid000bb9e4@dx1957f52b172b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597887}
{"recordId":"gty000bb9e5@dx1957f52b1b4b86a532:e7ff3923072d483cbf89767bdbba177c","requestId":"gty000bb9e5@dx1957f52b1b4b86a532","sessionId":"cid000bb9e4@dx1957f52b172b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597888}
{"recordId":"gty000bb9e5@dx1957f52b1b4b86a532:e7ff3923072d483cbf89767bdbba177c","requestId":"gty000bb9e5@dx1957f52b1b4b86a532","sessionId":"cid000bb9e4@dx1957f52b172b86a532","eof":"1","text":"我想找阿里巴巴的k线图","pinyin":"","topic":"asr.speech.result","age":"child","ageScore":"","gender":"","genderScore":"","language_class":"mandarin","timestamp":1741597888}
send data finished:1741597888.9817038
{"recordId":"gty000bb9e5@dx1957f52b1b4b86a532:e7ff3923072d483cbf89767bdbba177c","requestId":"gty000bb9e5@dx1957f52b1b4b86a532","sessionId":"cid000bb9e4@dx1957f52b172b86a532","topic":"dm.output","skill":"股票","skillId":"IFLYTEK.stock","speakUrl":"","error":{},"dm":{"input":"我想找阿里巴巴的k线图","intentId":"QUERY","intentName":"查股票的当前价、走势、K线图或详情等信息","nlg":"当前时间股市未开盘，上个交易日“阿里巴巴”收盘价为140.620美元，上涨0.48%。","shouldEndSession":true},"nlu":{"input":"我想找阿里巴巴的k线图","skill":"股票","skillId":"IFLYTEK.stock","skillVersion":"375.0","semantics":{"request":{"slots":[{"name":"name","value":"阿里巴巴"},{"name":"chartType","value":"日K线"},{"name":"code","value":"BABA"},{"name":"market","value":"us"}]}}},"timestamp":1741597888}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidb6989821@dx57681b29f8c13eef00"}
连接正常关闭
1741597889
param:b'{\n            "auth_id": "81aa9132e0084a74863c6240144b35d3",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid8e4a18eb@dxa2301b29f8c13eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "bbc9e97776064ab9aaca9dfd9e4b7f47","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb966@dx1957f52d379b8aa532"}
started:
ws start
####################
测试进行: ctm00014497@hu17b59066031020c902#46446280.pcm
{"recordId":"ase000f0aa9@hu1957f52eaba05c4882:bbc9e97776064ab9aaca9dfd9e4b7f47","requestId":"ase000f0aa9@hu1957f52eaba05c4882","sessionId":"cid000bb966@dx1957f52d379b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597895}
{"recordId":"gty000bb967@dx1957f52d3bab8aa532:bbc9e97776064ab9aaca9dfd9e4b7f47","requestId":"gty000bb967@dx1957f52d3bab8aa532","sessionId":"cid000bb966@dx1957f52d379b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597895}
{"recordId":"gty000bb967@dx1957f52d3bab8aa532:bbc9e97776064ab9aaca9dfd9e4b7f47","requestId":"gty000bb967@dx1957f52d3bab8aa532","sessionId":"cid000bb966@dx1957f52d379b8aa532","eof":"1","text":"给我讲故事","pinyin":"","topic":"asr.speech.result","age":"child","ageScore":"","gender":"","genderScore":"","language_class":"mandarin","timestamp":1741597895}
send data finished:1741597895.6347287
{"recordId":"gty000bb967@dx1957f52d3bab8aa532:bbc9e97776064ab9aaca9dfd9e4b7f47","requestId":"gty000bb967@dx1957f52d3bab8aa532","sessionId":"cid000bb966@dx1957f52d379b8aa532","topic":"dm.output","skill":"有声节目","skillId":"IFLYTEK.audioProgram","speakUrl":"","error":{},"dm":{"input":"给我讲故事","intentId":"PLAY","intentName":"播放","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"给我讲故事","skill":"有声节目","skillId":"IFLYTEK.audioProgram","skillVersion":"187.0","semantics":{"request":{"slots":[{"name":"category","value":"故事"}]}}},"timestamp":1741597895}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid197fd801@dx78601b29f8c73eef00"}
连接正常关闭
1741597895
param:b'{\n            "auth_id": "e53e1fac27194ed9a630b7cc866c3745",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid394245e6@dx17e11b29f8c73eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "7ba068f854da4a3d95f58f7dab35741e","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bbbc3@dx1957f52ed867844532"}
started:
ws start
####################
测试进行: ctm0001485e@hu17b666875600212902#46613800.pcm
{"recordId":"ase000f0d7d@hu1957f52fdc605c4882:7ba068f854da4a3d95f58f7dab35741e","requestId":"ase000f0d7d@hu1957f52fdc605c4882","sessionId":"cid000bbbc3@dx1957f52ed867844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597900}
{"recordId":"gty000bbbc4@dx1957f52edc87844532:7ba068f854da4a3d95f58f7dab35741e","requestId":"gty000bbbc4@dx1957f52edc87844532","sessionId":"cid000bbbc3@dx1957f52ed867844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597902}
{"recordId":"gty000bbbc4@dx1957f52edc87844532:7ba068f854da4a3d95f58f7dab35741e","requestId":"gty000bbbc4@dx1957f52edc87844532","sessionId":"cid000bbbc3@dx1957f52ed867844532","eof":"1","text":"我想了解娱乐圈最近的头条","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597902}
send data finished:1741597902.4984953
{"recordId":"gty000bbbc4@dx1957f52edc87844532:7ba068f854da4a3d95f58f7dab35741e","requestId":"gty000bbbc4@dx1957f52edc87844532","sessionId":"cid000bbbc3@dx1957f52ed867844532","topic":"dm.output","skill":"新闻","skillId":"2019031900001180","speakUrl":"","error":{},"dm":{"input":"我想了解娱乐圈最近的头条","intentId":"PLAY","intentName":"听新闻","nlg":"为您找到娱乐圈`头条的新闻，可以了解下3月10日人本生活资讯晚报：全国政协委员张凯丽：建议大力打击谣言黑产；哪吒2英配版将发行院线","widget":{"content":[{"album":"新闻","title":"3月10日人本生活资讯晚报：全国政协委员张凯丽：建议大力打击谣言黑产；哪吒2英配版将发行院线","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202503101610A6F32914097BFB52019E023671AEF9BF_64.mp3?pf=OH9GI&vid=11723586&tm=1741597902656&pid=1898262","extra":{"source":""}},{"album":"新闻","title":"减肥4年后绝大多数人恢复之前体重","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2025031015223F38235525E04BCD655F1A1FC53E5265_64.mp3?pf=OH9GI&vid=11723578&tm=1741597902656&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"人生最容易长胖的5个时刻","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202503101518265A9818823909625DF2F29B090E4595_64.mp3?pf=OH9GI&vid=11723563&tm=1741597902656&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"蹲便冲水细菌是坐便的2倍","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2025031015130EA1D3CDD6B6C8FFED07928B1EF5D96C_64.mp3?pf=OH9GI&vid=11723562&tm=1741597902656&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"代表建议加大医美行业监管力度﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202503101455893E4D847DE5CC6E99894AAF5C6287C6_64.mp3?pf=OH9GI&vid=11723554&tm=1741597902657&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"青少年需要身体能出汗的体育课﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2025031014558DC7F45429AA8B9EEFB93689A0B4901E_64.mp3?pf=OH9GI&vid=11723548&tm=1741597902657&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"防腐剂脱氢乙酸钠全面禁用是谣言﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202503101455491F9B10CADDCD42524F8ED3283E9F7C_64.mp3?pf=OH9GI&vid=11723547&tm=1741597902657&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"《纽约时报》急问为什么小米可以造电动车，苹果却不能？","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/202503101437E72886650ABC5F2085FD1CC6EFAA7474_64.mp3?pf=OH9GI&vid=11723538&tm=1741597902657&pid=194144","extra":{"source":"fm"}},{"album":"新闻","title":"养老服务向全体老年人拓展﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/2025031012542B3AF8EDDF8118971866627D50AA16AF_64.mp3?pf=OH9GI&vid=11723480&tm=1741597902657&pid=1897566","extra":{"source":"fm"}},{"album":"新闻","title":"加拿大称将继续对美征收报复性关税﻿","subTitle":"","imageUrl":"","linkUrl":"http://p3.renbenzhihui.com/cmpp/20250310125416F7FCBFA3CCE31C40BE0A772F4D0E45_64.mp3?pf=OH9GI&vid=11723476&tm=1741597902657&pid=1897566","extra":{"source":"fm"}}]},"shouldEndSession":true},"nlu":{"input":"我想了解娱乐圈最近的头条","skill":"新闻","skillId":"2019031900001180","skillVersion":"87","semantics":{"request":{"slots":[{"name":"category","value":"娱乐圈`头条"}]}}},"timestamp":1741597902}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid8624d7f3@dxc6811b29f8ce3eef00"}
连接正常关闭
1741597902
param:b'{\n            "auth_id": "f5c60f00b3d34a3ba7d96d1de937e2f0",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid0fb8228d@dxa83f1b29f8ce3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "3ae19c968b1f4a968e070625f99149e1","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bbbc8@dx1957f53092c7844532"}
started:
ws start
####################
测试进行: ctm0001486f@hu17b66687e640212902#46613813.pcm
{"recordId":"ase000d66df@hu1957f531a6c0427882:3ae19c968b1f4a968e070625f99149e1","requestId":"ase000d66df@hu1957f531a6c0427882","sessionId":"cid000bbbc8@dx1957f53092c7844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597907}
{"recordId":"gty000bbbc9@dx1957f53096f7844532:3ae19c968b1f4a968e070625f99149e1","requestId":"gty000bbbc9@dx1957f53096f7844532","sessionId":"cid000bbbc8@dx1957f53092c7844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597910}
{"recordId":"gty000bbbc9@dx1957f53096f7844532:3ae19c968b1f4a968e070625f99149e1","requestId":"gty000bbbc9@dx1957f53096f7844532","sessionId":"cid000bbbc8@dx1957f53092c7844532","eof":"1","text":"放一首陈奕迅粤语版的富士山下","pinyin":"","topic":"asr.speech.result","age":"child","ageScore":"","gender":"","genderScore":"","language_class":"mandarin","timestamp":1741597910}
send data finished:1741597910.5675566
{"recordId":"gty000bbbc9@dx1957f53096f7844532:3ae19c968b1f4a968e070625f99149e1","requestId":"gty000bbbc9@dx1957f53096f7844532","sessionId":"cid000bbbc8@dx1957f53092c7844532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"放一首陈奕迅粤语版的富士山下","intentId":"PLAY","intentName":"点歌","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"放一首陈奕迅粤语版的富士山下","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"歌曲名","value":"富士山下"},{"name":"歌手名","value":"陈奕迅"},{"name":"语种","value":"粤语"}]}}},"timestamp":1741597910}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid9d686dae@dxcf6f1b29f8d63eef00"}
连接正常关闭
1741597910
param:b'{\n            "auth_id": "5e627c78ee8e452cb644ebffb01f50f9",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidda70d501@dxbec11b29f8d63eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "76aaa82f7ddd415687f17ebbfe3870d0","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bb19d@dx1957f5327c3b8a9532"}
started:
ws start
####################
测试进行: ctm0001487c@hu17b666883e10212902#46613820.pcm
{"recordId":"ase000ee2a2@hu1957f53397c1323882:76aaa82f7ddd415687f17ebbfe3870d0","requestId":"ase000ee2a2@hu1957f53397c1323882","sessionId":"cid000bb19d@dx1957f5327c3b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597915}
{"recordId":"gty000bb19e@dx1957f532806b8a9532:76aaa82f7ddd415687f17ebbfe3870d0","requestId":"gty000bb19e@dx1957f532806b8a9532","sessionId":"cid000bb19d@dx1957f5327c3b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597916}
{"recordId":"gty000bb19e@dx1957f532806b8a9532:76aaa82f7ddd415687f17ebbfe3870d0","requestId":"gty000bb19e@dx1957f532806b8a9532","sessionId":"cid000bb19d@dx1957f5327c3b8a9532","eof":"1","text":"来一首我怀念的","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597916}
send data finished:1741597916.733773
{"recordId":"gty000bb19e@dx1957f532806b8a9532:76aaa82f7ddd415687f17ebbfe3870d0","requestId":"gty000bb19e@dx1957f532806b8a9532","sessionId":"cid000bb19d@dx1957f5327c3b8a9532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"来一首我怀念的","intentId":"PLAY","intentName":"点歌","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"来一首我怀念的","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"歌曲名","value":"我怀念的"}]}}},"timestamp":1741597916}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidf935905e@dx2b561b29f8dc3eef00"}
连接正常关闭
1741597916
param:b'{\n            "auth_id": "6bee7aff611f4ac4b7b6b1da7b98b22c",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidbeedb6cf@dxb05c1b29f8dd3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "b7f5dcd5e966403e8a0309fd54491194","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bba03@dx1957f534004b86a532"}
started:
ws start
####################
测试进行: ctm00014a05@hu17b5dbb782e020c902#46524760.pcm
{"recordId":"ase000f1a64@hu1957f5351c805c4882:b7f5dcd5e966403e8a0309fd54491194","requestId":"ase000f1a64@hu1957f5351c805c4882","sessionId":"cid000bba03@dx1957f534004b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597921}
{"recordId":"gty000bba04@dx1957f534048b86a532:b7f5dcd5e966403e8a0309fd54491194","requestId":"gty000bba04@dx1957f534048b86a532","sessionId":"cid000bba03@dx1957f534004b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597922}
{"recordId":"gty000bba04@dx1957f534048b86a532:b7f5dcd5e966403e8a0309fd54491194","requestId":"gty000bba04@dx1957f534048b86a532","sessionId":"cid000bba03@dx1957f534004b86a532","eof":"1","text":"突然心动的百科注释","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597922}
send data finished:1741597922.9070547
{"recordId":"gty000bba04@dx1957f534048b86a532:b7f5dcd5e966403e8a0309fd54491194","requestId":"gty000bba04@dx1957f534048b86a532","sessionId":"cid000bba03@dx1957f534004b86a532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"突然心动的百科注释","intentId":"chat","intentName":"闲聊","nlg":"《突然心动》是由刘二威执导，高曙光等主演的情感剧，讲述的一个女人在丈夫出轨后重新建立幸福家庭的故事。","shouldEndSession":true},"nlu":{"input":"突然心动的百科注释","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741597923}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidf519ce53@dx59101b29f8e33eef00"}
连接正常关闭
1741597923
param:b'{\n            "auth_id": "3088f8ad4cd44b62ac55c985ab6d52e6",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid6cf38fcd@dx43921b29f8e33eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "e9e4a13eb4f44bf0a0ec5fced93e610b","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bbbd8@dx1957f5358727844532"}
started:
ws start
####################
测试进行: ctm00014a10@hu17b5dbb7d14020c902#46524769.pcm
{"recordId":"ase000eb0fd@hu1957f536a7d05c3882:e9e4a13eb4f44bf0a0ec5fced93e610b","requestId":"ase000eb0fd@hu1957f536a7d05c3882","sessionId":"cid000bbbd8@dx1957f5358727844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597928}
{"recordId":"gty000bbbdb@dx1957f5358b47844532:e9e4a13eb4f44bf0a0ec5fced93e610b","requestId":"gty000bbbdb@dx1957f5358b47844532","sessionId":"cid000bbbd8@dx1957f5358727844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597928}
{"recordId":"gty000bbbdb@dx1957f5358b47844532:e9e4a13eb4f44bf0a0ec5fced93e610b","requestId":"gty000bbbdb@dx1957f5358b47844532","sessionId":"cid000bbbd8@dx1957f5358727844532","eof":"1","text":"新冠状病毒哪里来的","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597928}
send data finished:1741597928.8390093
{"recordId":"gty000bbbdb@dx1957f5358b47844532:e9e4a13eb4f44bf0a0ec5fced93e610b","requestId":"gty000bbbdb@dx1957f5358b47844532","sessionId":"cid000bbbd8@dx1957f5358727844532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"新冠状病毒哪里来的","intentId":"chat","intentName":"闲聊","nlg":"新冠状病毒后的症状和非典有些相似，感染者会出现急性、严重呼吸道疾病，伴有发热、咳嗽、气短及呼吸困难，严重的病例会出现肾功能衰竭和死亡。","shouldEndSession":true},"nlu":{"input":"新冠状病毒哪里来的","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741597928}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid764aac5b@dxd1b31b29f8e93eef00"}
连接正常关闭
1741597929
param:b'{\n            "auth_id": "b9215dd03e4e4ec5accb4a31dcd51f37",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid3d927e59@dx04741b29f8e93eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "46a0997fdaf64dabad3a41303d7d3903","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bbbe4@dx1957f536fb67844532"}
started:
ws start
####################
测试进行: ctm00014a1f@hu17b5dbb83ac020c902#46524783.pcm
{"recordId":"ase000d060e@hu1957f5382b804d3882:46a0997fdaf64dabad3a41303d7d3903","requestId":"ase000d060e@hu1957f5382b804d3882","sessionId":"cid000bbbe4@dx1957f536fb67844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741597934}
{"recordId":"gty000bbbe5@dx1957f536ff67844532:46a0997fdaf64dabad3a41303d7d3903","requestId":"gty000bbbe5@dx1957f536ff67844532","sessionId":"cid000bbbe4@dx1957f536fb67844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741597935}
{"recordId":"gty000bbbe5@dx1957f536ff67844532:46a0997fdaf64dabad3a41303d7d3903","requestId":"gty000bbbe5@dx1957f536ff67844532","sessionId":"cid000bbbe4@dx1957f536fb67844532","eof":"1","text":"给我来一首儿歌放梦","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741597935}
send data finished:1741597935.4461238
{"recordId":"gty000bbbe5@dx1957f536ff67844532:46a0997fdaf64dabad3a41303d7d3903","requestId":"gty000bbbe5@dx1957f536ff67844532","sessionId":"cid000bbbe4@dx1957f536fb67844532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"给我来一首儿歌放梦","intentId":"PLAY","intentName":"点歌","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"给我来一首儿歌放梦","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"歌曲名","value":"放梦"},{"name":"主题","value":"儿歌"},{"name":"标签","value":"儿歌"}]}}},"timestamp":1741597935}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid0828bb86@dx95351b29f8ef3eef00"}
连接正常关闭
1741597935
param:b'{\n            "auth_id": "8dd4c39b5e484c2b962fdcf7cf722f64",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidcf46ff52@dx4dbf1b29f8ef3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "a4e364413fa843878b5bcb91fd2aa6de","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bbbe7@dx1957f5388f97844532"}
started:
ws start
####################
测试进行: ctm00014c62@hu17b57ba10140212902#46419825.pcm
