package main

import (
	"bufio"
	"crypto/md5"
	"encoding/base64"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/websocket"
	"github.com/xuri/excelize/v2"
)

// --- Configuration (mirrors Python script's global variables) ---
const (
	// UAT Environment (adjust as needed)
	baseURL = "wss://beijing-midea.listenai.com/midea/proxy"
	// transURL  = "https://itrans.xfyun.cn/v2/its" // Not directly used
	appID  = "5e017b34"
	apiKey = "eed5bf68c7e01d85b3713d90bde3154a"
	// apiSecret = "0e3724a6013368a53ba21c34293705d4" // Not directly used

	scene        = "asr"
	dataType     = "audio" // "text" or "audio"
	eos          = 500
	enableVprRec = true

	audioPath = "./data/313sp_off_a2"
	testData  = "./data/a1&a2&a3.xlsx" // Note: & needs to be & in XML for the request, but will be & in the .go file

	textMsg = "深圳今天天气怎么样"

	encode = "raw" // "opus-wb" or "raw"

	interval     = 40 // ms
	sampleRate   = 16000
	sampleSize   = 2 // bytes
	channelCount = 1
)

var (
	sliceSize int

	startDate string
	startTime string

	logLock   sync.Mutex
	excelLock sync.Mutex
	logFile   *os.File
	threadNo  string // From command-line arguments

	iatSuccess = 0
	iatFail    = 0
)

// --- Structs ---
type WsClient struct {
	conn                *websocket.Conn
	sheetName           string
	row                 int
	cols                int
	expectedText        string
	startTimeClient     time.Time
	messageQueue        chan string
	audioName           string
	endPointMs          *int64
	endPointReachedTime int64

	realTextList      []string
	timestampVadEnd   time.Time
	timestampIsFinish time.Time
	timestampNlp      time.Time
	vprEndFlag        bool
	nlpEndFlag        bool
	sendDataWg        sync.WaitGroup
	mu                sync.Mutex
}

type AuthParams struct {
	AuthID   string `json:"auth_id"`
	DataType string `json:"data_type"`
}

type StartActionParamsData struct {
	Mid string `json:"mid"`
	Asr *struct {
		Eos              int   `json:"eos"`
		EnableVprRec     *bool `json:"enable_vpr_rec,omitempty"`
		EnableVprRecInfo *bool `json:"enable_vpr_rec_info,omitempty"`
		EnableVprCluster *bool `json:"enable_vpr_cluster,omitempty"`
		VprRec           *struct {
			GroupID string `json:"group_id,omitempty"`
		} `json:"vpr_rec,omitempty"`
		Encoding   string `json:"encoding"`
		Channels   int    `json:"channels"`
		BitDepth   int    `json:"bit_depth"`
		SampleRate int    `json:"sample_rate"`
		Pd         string `json:"pd,omitempty"`
	} `json:"asr,omitempty"`
	Nlu *struct {
		Vcn    string `json:"vcn,omitempty"`
		Pitch  int    `json:"pitch,omitempty"`
		Speed  int    `json:"speed,omitempty"`
		Volume int    `json:"volume,omitempty"`
		TtsAue string `json:"tts_aue,omitempty"`
		Text   string `json:"text,omitempty"`
	} `json:"nlu,omitempty"`
	Fullduplex *struct {
		Lat        string `json:"lat,omitempty"`
		Lng        string `json:"lng,omitempty"`
		Aue        string `json:"aue,omitempty"`
		Encoding   string `json:"encoding"`
		Eos        int    `json:"eos,omitempty"`
		Vcn        string `json:"vcn,omitempty"`
		Pitch      int    `json:"pitch,omitempty"`
		Speed      int    `json:"speed,omitempty"`
		Volume     int    `json:"volume,omitempty"`
		TtsAue     string `json:"tts_aue,omitempty"`
		SampleRate int    `json:"sample_rate"`
		Channels   int    `json:"channels"`
		BitDepth   int    `json:"bit_depth"`
	} `json:"fullduplex,omitempty"`
}

type WebSocketSendPayload struct {
	Action string                 `json:"action"`
	Scene  string                 `json:"scene,omitempty"`
	Params *StartActionParamsData `json:"params,omitempty"`
}

type WebSocketReceivePayload struct {
	Action    string           `json:"action"`
	Topic     string           `json:"topic,omitempty"`
	Text      string           `json:"text,omitempty"`
	Sentence  string           `json:"sentence,omitempty"`
	RequestID string           `json:"requestId,omitempty"`
	Data      *json.RawMessage `json:"data,omitempty"`
	Code      string           `json:"code,omitempty"` // Changed from int to string
	Message   string           `json:"message,omitempty"`
}

type IatDataPayload struct {
	Sub  string `json:"sub"`
	Text string `json:"text"`
}

type NlpDataPayload struct {
	Sub    string `json:"sub"`
	Intent struct {
		Rc     int `json:"rc"`
		Answer *struct {
			Text string `json:"text"`
		} `json:"answer"`
	} `json:"intent"`
}

type TtsDataPayload struct {
	Sub     string `json:"sub"`
	Content string `json:"content"`
}

// --- Initialization ---
func init() {
	if encode == "raw" {
		sliceSize = sampleRate * channelCount * sampleSize * interval / 1000
	} else {
		sliceSize = 124
	}

	now := time.Now()
	startDate = now.Format("20060102")
	startTime = now.Format("20060102-150405")

	logDir := "./log"
	if _, err := os.Stat(logDir); os.IsNotExist(err) {
		os.MkdirAll(logDir, 0755)
	}
}

// --- Logging ---
func customPrint(content ...interface{}) {
	logLock.Lock()
	defer logLock.Unlock()

	fmt.Println(content...)

	if logFile != nil {
		var logEntryBuilder strings.Builder
		for i, item := range content {
			if i > 0 {
				logEntryBuilder.WriteString(" ")
			}
			logEntryBuilder.WriteString(fmt.Sprint(item))
		}
		logEntryBuilder.WriteString("\n")

		if _, err := logFile.WriteString(logEntryBuilder.String()); err != nil {
			log.Printf("Failed to write to custom log file: %v. Original message: %s", err, logEntryBuilder.String())
		}
	} else {
		log.Println(content...)
	}
}

// --- File Utilities ---
func findDir(dirName string) ([]string, error) {
	var fileList []string
	err := filepath.Walk(dirName, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() {
			fileList = append(fileList, path)
		}
		return nil
	})
	return fileList, err
}

// --- Text Processing ---

// getRealText (from asr_common.py)
func getRealText(realTextList []string) string {
	if realTextList == nil || len(realTextList) == 0 {
		return ""
	}
	if len(realTextList) == 1 {
		// In Python, if real_text_list[0] is bool, it's handled.
		// Go's string slice won't directly hold bools, so this case is simpler.
		return realTextList[0]
	}
	realText := realTextList[len(realTextList)-1]
	// Python: if real_text == "" or real_text == [''] or isinstance(real_text, bool):
	// Go: bool case not applicable for string. [''] not directly applicable.
	if realText == "" {
		return getRealText(realTextList[:len(realTextList)-1])
	}
	return realText
}

var (
	// CN_NUM_D from asr_common.py
	cnNumD = map[string]int{
		"十": 10, "百": 100, "千": 1000, "万": 10000, "亿": 100000000,
	}
	// CN_NUM from asr_common.py
	cnNum = map[string]string{
		"一": "1", "二": "2", "三": "3", "四": "4", "五": "5", "六": "6", "七": "7", "八": "8", "九": "9", "零": "0",
		"廿": "2", "两": "2",
	}
)

// handle_digit (from asr_common.py)
// Helper for handleStr
func handleDigit(handleResult string, subResult rune) string {
	subResultStr := string(subResult)
	handleResultRunes := []rune(handleResult)
	subResultIndex := -1
	for i, r := range handleResultRunes {
		if r == subResult {
			subResultIndex = i
			break
		}
	}

	if subResultIndex == -1 { // Should not happen if called correctly
		return handleResult
	}

	valD, okD := cnNumD[subResultStr]
	if !okD {
		return handleResult // Not a valid unit
	}

	// Simplified logic based on Python's handle_digit, focusing on common cases
	// Python's version is complex and handles many edge cases.
	// This Go version will be a direct translation attempt.

	// Case: "一十" -> "10", "二十" -> "20"
	if subResultIndex > 0 {
		prevChar := handleResultRunes[subResultIndex-1]
		prevDigitStr := string(prevChar)
		if prevDigit, err := strconv.Atoi(prevDigitStr); err == nil { // prevChar is a digit
			// "三千" -> 3 * 1000
			// "三千五" -> (3*1000) + 5 (next part handles the +5)
			// "一十三" -> 1 * 10 + 3
			if subResultStr == "十" && subResultIndex+1 < len(handleResultRunes) {
				nextChar := handleResultRunes[subResultIndex+1]
				nextDigitStr := string(nextChar)
				if nextDigit, errNext := strconv.Atoi(nextDigitStr); errNext == nil {
					// "一十三" -> 13
					calculated := prevDigit*valD + nextDigit
					return string(handleResultRunes[:subResultIndex-1]) + strconv.Itoa(calculated) + string(handleResultRunes[subResultIndex+2:])
				}
			}
			// "三十" -> 30
			calculated := prevDigit * valD
			return string(handleResultRunes[:subResultIndex-1]) + strconv.Itoa(calculated) + string(handleResultRunes[subResultIndex+1:])
		}
	}

	// Case: "十一" -> 11, "十五" -> 15
	if subResultStr == "十" && subResultIndex == 0 && subResultIndex+1 < len(handleResultRunes) {
		nextChar := handleResultRunes[subResultIndex+1]
		nextDigitStr := string(nextChar)
		if nextDigit, err := strconv.Atoi(nextDigitStr); err == nil {
			calculated := valD + nextDigit
			return strconv.Itoa(calculated) + string(handleResultRunes[subResultIndex+2:])
		}
	}
	// Case: "十" alone or "百" alone (less common without preceding/succeeding digits in context)
	// Defaulting to just the value of the unit if it's at the beginning and not "十" followed by digit
	if subResultIndex == 0 {
		return strconv.Itoa(valD) + string(handleResultRunes[subResultIndex+1:])
	}

	// Fallback: if no specific pattern matched, just replace the unit with its numeric value (might be incorrect for complex cases)
	// This part is tricky to translate directly without a full parser.
	// The Python code seems to iteratively replace, which is hard to replicate with simple string ops.
	// For now, we'll rely on the initial cnNum replacement and specific regexes later.
	// The Python's handle_digit is called iteratively. A direct Go equivalent would need a similar loop.
	// This simplified version might not cover all cases of handle_digit.
	// Let's assume the primary cnNum to digit conversion handles most, and regexes clean up.
	return strings.Replace(handleResult, subResultStr, strconv.Itoa(valD), 1)
}

// handle_str (from asr_common.py)
func handleStr(result string) string {
	if result == "" {
		return ""
	}
	var handleResultBuilder strings.Builder
	for _, r := range result {
		char := string(r)
		if val, ok := cnNum[char]; ok {
			handleResultBuilder.WriteString(val)
		} else {
			handleResultBuilder.WriteString(char)
		}
	}
	handleResult := handleResultBuilder.String()

	// Iteratively apply handle_digit logic for units (十, 百, 千, 万, 亿)
	// This is a simplified loop compared to Python's implicit iteration by repeated calls.
	// We need to be careful about the order and how replacements affect subsequent ones.
	// Order of units matters: 万 and 亿 should be processed before smaller units within them.
	// For simplicity, let's try replacing from largest to smallest if present.
	// This is still a complex part to translate perfectly without a proper number parsing library.
	// The Python code's `for i in handle_result: if i in ['十', '百', ...]: handle_result = handle_digit(...)`
	// implies that `handle_result` is modified in place and the loop continues on the modified string.
	// This is hard to do safely in Go with simple string replacement.
	// A more robust solution would parse segments.
	// Given the constraints, we'll stick to the regexes below for more complex conversions for now,
	// as the direct `handle_digit` translation is non-trivial.
	// The initial cnNum map handles "一十二" -> "12". "二十三" -> "23".
	// "十二" -> "12" (due to cnNum: "十" is not in cnNum, so it remains "十", then "一"->"1", "二"->"2" -> "1十2")
	// The Python's `handle_digit` seems to refine this.
	// Example: "一十二" -> "12" (by cnNum).
	// Example: "十二" -> Python's `handle_str` first pass: "十2" (一 is not there, 二->2). Then `handle_digit` for '十'.
	// Python `handle_digit` for "十" in "十2": prev_digit="", sub_result="十", next_digit="2" -> 10+2 = 12.
	// Let's try to replicate the "十" special handling.
	// "一十三" -> "13"
	reShi := regexp.MustCompile(`(\d)十(\d)`) // e.g., "一十三" -> "1十3" -> "13"
	handleResult = reShi.ReplaceAllStringFunc(handleResult, func(s string) string {
		parts := reShi.FindStringSubmatch(s)
		d1, _ := strconv.Atoi(parts[1])
		d2, _ := strconv.Atoi(parts[2])
		return strconv.Itoa(d1*10 + d2)
	})
	// "十三" -> "13"
	reShiPrefix := regexp.MustCompile(`十(\d)`) // e.g., "十三" -> "十3" -> "13"
	handleResult = reShiPrefix.ReplaceAllStringFunc(handleResult, func(s string) string {
		parts := reShiPrefix.FindStringSubmatch(s)
		d, _ := strconv.Atoi(parts[1])
		return strconv.Itoa(10 + d)
	})
	// "三十" -> "30"
	reShiSuffix := regexp.MustCompile(`(\d)十`) // e.g., "三十" -> "3十" -> "30"
	handleResult = reShiSuffix.ReplaceAllStringFunc(handleResult, func(s string) string {
		parts := reShiSuffix.FindStringSubmatch(s)
		d, _ := strconv.Atoi(parts[1])
		return strconv.Itoa(d * 10)
	})

	// Handling "百分之几"
	rePercent := regexp.MustCompile(`100分之(\d+)`)
	handleResult = rePercent.ReplaceAllString(handleResult, "$1%")

	// Handling "几分之几"
	// Python: m = re.search('(\d+)分之', handle_result, re.IGNORECASE) ...
	// This implies the denominator can be multi-digit.
	reFraction := regexp.MustCompile(`(\d+)分之(\d+)`)
	handleResult = reFraction.ReplaceAllString(handleResult, "$2/$1")

	// Time and specific replacements
	// pattern1 = re.compile('\d点*\d') - this check is implicit in Go's ReplaceAll
	handleResult = strings.ReplaceAll(handleResult, "点半", ":30")
	handleResult = strings.ReplaceAll(handleResult, "点钟", ":00")
	handleResult = strings.ReplaceAll(handleResult, "点", ".") //点 must be last for time

	// pattern2 = re.compile('\d除*\d')
	handleResult = strings.ReplaceAll(handleResult, "除以", "÷")
	handleResult = strings.ReplaceAll(handleResult, "除", "÷") // Assuming "除" alone also means divide

	// pattern3 = re.compile('\d*负\d')
	handleResult = strings.ReplaceAll(handleResult, "减负", "--") // Specific case
	handleResult = strings.ReplaceAll(handleResult, "负", "-")   // General case

	// pattern4 = re.compile('\d减\d')
	handleResult = strings.ReplaceAll(handleResult, "减", "-")

	// pattern5 = re.compile('\d加\d')
	// pattern6 = re.compile('\d再+\d') - "再+" is a bit unusual, assume "再加"
	handleResult = strings.ReplaceAll(handleResult, "再加", "+") // Python has "再+", Go needs specific string
	handleResult = strings.ReplaceAll(handleResult, "加", "+")

	// pattern7 = re.compile('\d等于')
	handleResult = strings.ReplaceAll(handleResult, "等于", "=")

	// pattern8 = re.compile('\d乘*\d')
	handleResult = strings.ReplaceAll(handleResult, "乘以", "*")
	handleResult = strings.ReplaceAll(handleResult, "乘", "*")

	// Python: handle_result = re.findall('[\u4e00-\u9fa5a-zA-Z0-9]+', handle_result, re.S)
	// This Python line seems to be trying to keep only Chinese, alphanumeric characters.
	// The + means one or more. re.S means . matches newline.
	// This is effectively removing any character NOT in that set if they are isolated.
	// Or, if the string is "abc def", it becomes ["abc", "def"], then "".join makes "abcdef".
	// Let's replicate the "remove unwanted characters" idea.
	// Unwanted characters would be those not Han, not ASCII letter, not ASCII digit.
	// This is simpler than findall and join.
	var resultBuilder strings.Builder
	for _, r := range handleResult {
		if (r >= '\u4e00' && r <= '\u9fa5') || (r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') || (r >= '0' && r <= '9') ||
			// Keep symbols that were results of replacements
			r == '%' || r == '/' || r == ':' || r == '.' || r == '÷' || r == '-' || r == '+' || r == '=' || r == '*' {
			resultBuilder.WriteRune(r)
		}
	}
	handleResult = resultBuilder.String()
	// The Python version's findall + join might be more aggressive in removing spaces between valid chunks.
	// E.g. "你好 world 123" -> "你好world123"
	// The Go loop above would keep "你好 world 123" if space was in the allowed set.
	// If space is NOT in allowed set (as it is now), it becomes "你好world123". This matches Python.

	return handleResult
}

// --- WebSocket and Excel Processing Logic ---

func processTask(taskWg *sync.WaitGroup, sheetName string, audioName string, excelRow int, excelColsOriginal int, expectedText string, endPointMsVal *int64, newWorkbook *excelize.File, midValue string) {
	if taskWg != nil {
		defer taskWg.Done()
	}

	customPrint(fmt.Sprintf("Processing task: Sheet=%s, Audio=%s, Row=%d, Expected=%s", sheetName, audioName, excelRow, expectedText))

	curTimeUnix := time.Now().Unix()
	// Python's uuid.uuid4().hex generates a 32-char UUID without hyphens.
	// Go's uuid.New().String() generates a 36-char UUID with hyphens.
	// The server error indicates a max length of 32 for UID.
	authIDFull := uuid.New().String()
	authID := strings.ReplaceAll(authIDFull, "-", "") // Remove hyphens to get 32 chars

	authPayload := AuthParams{
		AuthID:   authID,
		DataType: dataType,
	}
	paramBytes, err := json.Marshal(authPayload)
	if err != nil {
		customPrint(fmt.Sprintf("Error marshalling auth params for %s: %v", audioName, err))
		return
	}
	paramBase64 := base64.StdEncoding.EncodeToString(paramBytes)

	checkSumPre := apiKey + strconv.FormatInt(curTimeUnix, 10) + paramBase64
	checksum := fmt.Sprintf("%x", md5.Sum([]byte(checkSumPre)))

	wsURL := fmt.Sprintf("%s?appid=%s&checksum=%s&curtime=%d&param=%s&signtype=md5",
		baseURL, appID, checksum, curTimeUnix, paramBase64)

	customPrint("Connecting to:", wsURL)

	dialer := websocket.Dialer{
		HandshakeTimeout: 45 * time.Second,
	}
	// Python script includes an Origin header: headers=[("Origin", "http://127.0.0.1:1024")]
	requestHeader := map[string][]string{
		"Origin": {"http://127.0.0.1:1024"},
	}
	conn, _, err := dialer.Dial(wsURL, requestHeader)
	if err != nil {
		customPrint(fmt.Sprintf("Error dialing WebSocket for %s: %v", audioName, err))
		return
	}
	defer conn.Close()

	client := &WsClient{
		conn:            conn,
		sheetName:       sheetName,
		row:             excelRow,
		cols:            excelColsOriginal,
		expectedText:    expectedText,
		startTimeClient: time.Time{}, // Will be set on "connected"
		messageQueue:    make(chan string, 1),
		audioName:       audioName,
		endPointMs:      endPointMsVal,
	}

	done := make(chan struct{})
	go func() {
		defer close(done)
		for {
			messageType, message, err := client.conn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseNormalClosure, websocket.CloseNoStatusReceived) {
					customPrint(fmt.Sprintf("WebSocket read error for %s: %v", client.audioName, err))
				} else if err == websocket.ErrCloseSent {
					customPrint(fmt.Sprintf("WebSocket closed by client for %s", client.audioName))
				} else if e, ok := err.(*websocket.CloseError); ok && e.Code == websocket.CloseNormalClosure {
					customPrint(fmt.Sprintf("WebSocket normally closed for %s", client.audioName))
				} else {
					customPrint(fmt.Sprintf("WebSocket read error for %s (code %T): %v", client.audioName, err, err))
				}
				return
			}
			if messageType == websocket.TextMessage {
				client.receivedMessage(message, newWorkbook)
			} else if messageType == websocket.BinaryMessage {
				customPrint(fmt.Sprintf("Received binary message for %s, ignoring.", client.audioName))
			}
		}
	}()

	client.createSession(newWorkbook)

	<-done
	customPrint(fmt.Sprintf("Finished processing for audio: %s", client.audioName))
}

func (client *WsClient) receivedMessage(message []byte, newWorkbook *excelize.File) {
	client.mu.Lock()
	defer client.mu.Unlock()

	customPrint(fmt.Sprintf("Received for %s: %s", client.audioName, string(message)))

	var msg WebSocketReceivePayload
	if err := json.Unmarshal(message, &msg); err != nil {
		customPrint(fmt.Sprintf("Error unmarshalling message for %s: %v. Raw: %s", client.audioName, err, string(message)))
		return
	}

	testResult := "FALSE"

	if msg.Action != "" {
		switch msg.Action {
		case "connected":
			// Example raw: {"action":"connected","cid":"...","code":"0","data":"","desc":"success"}
			customPrint(fmt.Sprintf("Connected action received for %s. Code: %s, Desc: (captured in Message if tagged for 'desc')", client.audioName, msg.Code))
			client.startTimeClient = time.Now() // This is the client's timestamp when "connected" is received.
			excelLock.Lock()
			// Python: new_worksheet.write(self.row, self.cols + 6, self.start_time)
			// self.start_time in Python WsapiClient is the timestamp passed from process_task (cur_time),
			// which is the time *before* WebSocket connection is established.
			// The Python script's `self.start_time` in `WsapiClient.opened` is actually `cur_time` from `process_task`.
			// And in `received_message` for "connected", it updates `self.start_time = time.time()`.
			// So, the Go equivalent of `self.cols + 6` should indeed be `client.startTimeClient`.
			cellStartTime, _ := excelize.CoordinatesToCellName(client.cols+6+1, client.row)
			newWorkbook.SetCellValue(client.sheetName, cellStartTime, client.startTimeClient.UnixNano()/int64(time.Millisecond))
			excelLock.Unlock()
			// Python calls create_session() after this in received_message.
			// In Go, createSession is called from processTask *before* the read loop starts.
		case "started":
			customPrint(fmt.Sprintf("Session started for %s: %s", client.audioName, time.Now().String()))
			// Python: if scene == "tts": self.send(text_msg.encode("utf-8"), True)
			// Python: else: if data_type == "text": self.send(text_msg.encode("utf-8"), True)
			// Python: if data_type == "audio": self.t1.start()
			// Go:
			if scene == "tts" {
				// client.send(textMsg, true)
			} else {
				if dataType == "text" {
					// client.send(textMsg, true)
				} else if dataType == "audio" {
					customPrint(fmt.Sprintf("Starting audio data send for %s", client.audioName))
					client.sendDataWg.Add(1)
					go client.sendAudioData(newWorkbook)
				}
			}
		case "error":
			// msg.Code is now a string. If numeric representation is needed for logic, convert it.
			customPrint(fmt.Sprintf("Error from server for %s: Code %s, Message %s", client.audioName, msg.Code, msg.Message))
			select {
			case client.messageQueue <- "asr.vad.end":
			default:
			}
			client.sendEndTag()
			client.sendCloseSession()
		case "result":
			if msg.Data != nil {
				var dataMap map[string]interface{}
				if err := json.Unmarshal(*msg.Data, &dataMap); err == nil {
					if sub, ok := dataMap["sub"].(string); ok {
						switch sub {
						case "iat":
							var iatPayload IatDataPayload
							if err := json.Unmarshal(*msg.Data, &iatPayload); err == nil {
								customPrint(fmt.Sprintf("IAT result for %s: %s", client.audioName, iatPayload.Text))
							}
						case "nlp":
							var nlpPayload NlpDataPayload
							if err := json.Unmarshal(*msg.Data, &nlpPayload); err == nil {
								if nlpPayload.Intent.Rc == 0 && nlpPayload.Intent.Answer != nil {
									customPrint(fmt.Sprintf("NLP result for %s: %s", client.audioName, nlpPayload.Intent.Answer.Text))
								} else {
									customPrint(fmt.Sprintf("NLP result for %s: No answer or error (rc=%d)", client.audioName, nlpPayload.Intent.Rc))
								}
							}
						case "tts":
							customPrint(fmt.Sprintf("TTS result for %s (content received)", client.audioName))
						}
					}
				}
			}
		case "finish":
			customPrint(fmt.Sprintf("Received 'finish' action for %s.", client.audioName))
		default:
			customPrint(fmt.Sprintf("Received unhandled action '%s' for %s", msg.Action, client.audioName))
		}
	} else if msg.Topic != "" {
		switch msg.Topic {
		case "asr.speech.result":
			client.timestampIsFinish = time.Now() // Time when "asr.speech.result" is received
			// Python: self.real_text_list.append(iat_text)
			client.realTextList = append(client.realTextList, msg.Text) // msg.Text corresponds to s['text']

			// Python: self.queue.put("asr.vad.end")
			// This signals send_by_slice to stop sending audio.
			select {
			case client.messageQueue <- "asr.vad.end":
			default: // Avoid blocking
			}

			finalText := getRealText(client.realTextList)
			handledFinalText := handleStr(finalText)
			handledExpectedText := handleStr(client.expectedText)

			if strings.ToLower(handledExpectedText) == strings.ToLower(handledFinalText) ||
				strings.ToLower(client.expectedText) == strings.ToLower(finalText) ||
				strings.ToLower(client.expectedText) == strings.ToLower(handledFinalText) ||
				strings.ToLower(handledExpectedText) == strings.ToLower(finalText) {
				testResult = "TRUE"
				atomicIncrementIatSuccess()
			} else {
				atomicIncrementIatFail()
			}

			excelLock.Lock()
			// Python: new_worksheet.write(self.row, self.cols + 2, requestId)
			cellRequestID, _ := excelize.CoordinatesToCellName(client.cols+2+1, client.row)
			newWorkbook.SetCellValue(client.sheetName, cellRequestID, msg.RequestID)

			// Python: new_worksheet.write(self.row, self.cols + 9, self.timestamp_isfinish)
			cellTsFinish, _ := excelize.CoordinatesToCellName(client.cols+9+1, client.row)
			newWorkbook.SetCellValue(client.sheetName, cellTsFinish, client.timestampIsFinish.UnixNano()/int64(time.Millisecond))

			// Python: new_worksheet.write(self.row, self.cols + 10, int(round((self.timestamp_isfinish - self.timestamp_end) * 1000)))
			// self.timestamp_end is set on "asr.vad.end"
			if !client.timestampVadEnd.IsZero() {
				durationVadToFinish := client.timestampIsFinish.Sub(client.timestampVadEnd).Milliseconds()
				cellDuration, _ := excelize.CoordinatesToCellName(client.cols+10+1, client.row)
				newWorkbook.SetCellValue(client.sheetName, cellDuration, durationVadToFinish)
			}

			if client.expectedText != "" {
				// Python: new_worksheet.write(self.row, self.cols, real_text)
				cellResultText, _ := excelize.CoordinatesToCellName(client.cols+0+1, client.row) // Python's self.cols is 0-indexed for the first new column
				newWorkbook.SetCellValue(client.sheetName, cellResultText, finalText)
				// Python: new_worksheet.write(self.row, self.cols + 1, test_result)
				cellTestResult, _ := excelize.CoordinatesToCellName(client.cols+1+1, client.row)
				newWorkbook.SetCellValue(client.sheetName, cellTestResult, testResult)
			}
			excelLock.Unlock()

		case "asr.vad.end":
			client.timestampVadEnd = time.Now() // Time when "asr.vad.end" is received
			excelLock.Lock()
			// Python: new_worksheet.write(self.row, self.cols + 7, self.timestamp_end)
			cellTsVad, _ := excelize.CoordinatesToCellName(client.cols+7+1, client.row)
			newWorkbook.SetCellValue(client.sheetName, cellTsVad, client.timestampVadEnd.UnixNano()/int64(time.Millisecond))

			// Python: new_worksheet.write(self.row, self.cols + 8, int(round((self.timestamp_end - self.start_time) * 1000)))
			// self.start_time is the timestamp from "connected" action. In Go, client.startTimeClient.
			if !client.startTimeClient.IsZero() {
				durationStartToVad := client.timestampVadEnd.Sub(client.startTimeClient).Milliseconds()
				cellDuration, _ := excelize.CoordinatesToCellName(client.cols+8+1, client.row)
				newWorkbook.SetCellValue(client.sheetName, cellDuration, durationStartToVad)
			}

			// Python: if self.end_point_ms is not None:
			//             if not self.end_point == 0:
			//                 new_worksheet.write(self.row, self.cols + 12, int(self.timestamp_end - self.end_point))
			//             else:
			//                 new_worksheet.write(self.row, self.cols + 12, int(self.timestamp_end - self.start_time - self.end_point_ms))
			// self.end_point is time.time()*1000 when calculated endpoint is passed during send. In Go, client.endPointReachedTime (ms)
			// self.start_time is the timestamp from "connected" action.
			if client.endPointMs != nil {
				var durationEpToVad int64
				if client.endPointReachedTime != 0 { // client.endPointReachedTime is in milliseconds
					durationEpToVad = client.timestampVadEnd.UnixNano()/int64(time.Millisecond) - client.endPointReachedTime
				} else {
					// This case means VAD arrived before the client calculated the endpoint had been passed.
					// Python: int(self.timestamp_end - self.start_time - self.end_point_ms)
					// This is (vad_time_ms - connected_time_ms) - configured_endpoint_offset_ms
					if !client.startTimeClient.IsZero() {
						durationEpToVad = client.timestampVadEnd.Sub(client.startTimeClient).Milliseconds() - *client.endPointMs
					}
				}
				cellDurationEP, _ := excelize.CoordinatesToCellName(client.cols+12+1, client.row)
				newWorkbook.SetCellValue(client.sheetName, cellDurationEP, durationEpToVad)
			}
			excelLock.Unlock()

		case "dm.output":
			client.timestampNlp = time.Now() // Time when "dm.output" is received
			excelLock.Lock()
			// Python: new_worksheet.write(self.row, self.cols + 11, int(round((self.timestamp_nlp - self.timestamp_isfinish) * 1000)))
			if !client.timestampIsFinish.IsZero() { // timestampIsFinish is from "asr.speech.result" or "asr.speech.sentence"
				durationAsrToNlp := client.timestampNlp.Sub(client.timestampIsFinish).Milliseconds()
				cellDuration, _ := excelize.CoordinatesToCellName(client.cols+11+1, client.row)
				newWorkbook.SetCellValue(client.sheetName, cellDuration, durationAsrToNlp)
			}
			excelLock.Unlock()

			client.nlpEndFlag = true
			if enableVprRec {
				if client.vprEndFlag { // vprEndFlag is from "asrplus.voiceprint.result"
					client.sendEndTag()
					client.sendCloseSession()
				}
			} else {
				client.sendEndTag()
				client.sendCloseSession()
			}

		case "asrplus.voiceprint.result":
			client.vprEndFlag = true
			if enableVprRec {
				if client.nlpEndFlag { // nlpEndFlag is from "dm.output"
					client.sendEndTag()
					client.sendCloseSession()
				}
			}
		case "asr.speech.sentence":
			client.timestampIsFinish = time.Now() // Time when "asr.speech.sentence" is received
			// Python: self.queue.put("asr.vad.end")
			select {
			case client.messageQueue <- "asr.vad.end":
			default:
			}

			// Python: self.send_end_tag()
			// Python: self.send_close_session()
			// These are called immediately in Python for this topic.
			client.sendEndTag()
			client.sendCloseSession()

			// Python: self.real_text_list.append(iat_text) // iat_text = s['sentence']
			client.realTextList = append(client.realTextList, msg.Sentence) // msg.Sentence corresponds to s['sentence']
			finalText := getRealText(client.realTextList)
			handledFinalText := handleStr(finalText)
			handledExpectedText := handleStr(client.expectedText)

			if strings.ToLower(handledExpectedText) == strings.ToLower(handledFinalText) ||
				strings.ToLower(client.expectedText) == strings.ToLower(finalText) ||
				strings.ToLower(client.expectedText) == strings.ToLower(handledFinalText) ||
				strings.ToLower(handledExpectedText) == strings.ToLower(finalText) {
				testResult = "TRUE"
				atomicIncrementIatSuccess()
			} else {
				atomicIncrementIatFail()
			}

			excelLock.Lock()
			// Python: new_worksheet.write(self.row, self.cols + 2, requestId)
			cellRequestID, _ := excelize.CoordinatesToCellName(client.cols+2+1, client.row)
			newWorkbook.SetCellValue(client.sheetName, cellRequestID, msg.RequestID)

			// Python: new_worksheet.write(self.row, self.cols + 9, self.timestamp_isfinish)
			cellTsFinish, _ := excelize.CoordinatesToCellName(client.cols+9+1, client.row)
			newWorkbook.SetCellValue(client.sheetName, cellTsFinish, client.timestampIsFinish.UnixNano()/int64(time.Millisecond))

			// Python: new_worksheet.write(self.row, self.cols + 10, int(round((self.timestamp_isfinish - self.timestamp_end) * 1000)))
			// self.timestamp_end is from "asr.vad.end"
			if !client.timestampVadEnd.IsZero() {
				durationVadToFinish := client.timestampIsFinish.Sub(client.timestampVadEnd).Milliseconds()
				cellDuration, _ := excelize.CoordinatesToCellName(client.cols+10+1, client.row)
				newWorkbook.SetCellValue(client.sheetName, cellDuration, durationVadToFinish)
			}

			if client.expectedText != "" {
				// Python: new_worksheet.write(self.row, self.cols, real_text)
				cellResultText, _ := excelize.CoordinatesToCellName(client.cols+0+1, client.row)
				newWorkbook.SetCellValue(client.sheetName, cellResultText, finalText)
				// Python: new_worksheet.write(self.row, self.cols + 1, test_result)
				cellTestResult, _ := excelize.CoordinatesToCellName(client.cols+1+1, client.row)
				newWorkbook.SetCellValue(client.sheetName, cellTestResult, testResult)
			}
			excelLock.Unlock()
		default:
			customPrint(fmt.Sprintf("Received unhandled topic '%s' for %s", msg.Topic, client.audioName))
		}
	}
}

func (client *WsClient) createSession(newWorkbook *excelize.File) {
	clientMID := uuid.New().String()
	if encode == "opus-wb" {
		clientMID = "opus-" + clientMID // Python: mid = f"opus-{mid}"
	}

	// Base params for the "start" action
	paramsData := &StartActionParamsData{Mid: clientMID}

	// Python:
	// if scene == 'tts':
	//     param = param_tts.encode(encoding="utf-8")
	// else:
	//     if data_type == 'text':
	//         param = param_nluandtts.encode(encoding="utf-8")
	//     if data_type == 'audio':
	//         if scene == 'fullduplex':
	//             param = param_fullduplex.encode(encoding="utf-8")
	//         else:
	//             param = param_iat.encode(encoding="utf-8")

	if scene == "tts" {
		// Python: param_tts = "" (empty in the provided script)
		// Assuming it would be structured if used. For now, it's empty.
		// If it were like param_nluandtts:
		// paramsData.Nlu = &struct { ... } { Text: textMsg, Vcn: "x2_xiaojuan", ... }
		// For now, no specific params for TTS based on Python script.
	} else if dataType == "text" {
		// Python: param_nluandtts = "{\"action\":\"start\",\"scene\":\"" + scene + "\", \"params\": {\"mid\": \"" + f"opus-{mid}" + "\",\"nlu\":{\"text\": \"我想听故事\",\"vcn\": \"x2_xiaojuan\",\"pitch\": 60,\"speed\": 60,\"volume\": 50,\"tts_aue\": \"raw\"}}}"
		// Note: Python's param_nluandtts hardcodes mid with "opus-", Go's clientMID already handles this.
		paramsData.Nlu = &struct {
			Vcn    string `json:"vcn,omitempty"`
			Pitch  int    `json:"pitch,omitempty"`
			Speed  int    `json:"speed,omitempty"`
			Volume int    `json:"volume,omitempty"`
			TtsAue string `json:"tts_aue,omitempty"`
			Text   string `json:"text,omitempty"`
		}{Text: "我想听故事", Vcn: "x2_xiaojuan", Pitch: 60, Speed: 60, Volume: 50, TtsAue: "raw"}
	} else if dataType == "audio" {
		if scene == "fullduplex" {
			// Python: param_fullduplex = "{\"action\":\"start\",\"scene\":\"" + scene + "\", \"params\": {\"mid\": \"" + mid + "\",\"fullduplex\":{\"lat\": \"31.83\",\"lng\": \"117.14\",\"aue\": \"raw\",\"encoding\": \"" + encode + "\",\"eos\":300,\"vcn\": \"x2_xiaojuan\",\"pitch\": 60,\"speed\": 60,\"volume\": 50,\"tts_aue\": \"raw\",\"sample_rate\": " + str(SAMPLE_RATE) + ", \"channels\":" + str(CHANNEL_COUNT) + ",\"bit_depth\": " + str(SAMPLE_SIZE * 8) + "}}}"
			paramsData.Fullduplex = &struct {
				Lat        string `json:"lat,omitempty"`
				Lng        string `json:"lng,omitempty"`
				Aue        string `json:"aue,omitempty"`
				Encoding   string `json:"encoding"`
				Eos        int    `json:"eos,omitempty"`
				Vcn        string `json:"vcn,omitempty"`
				Pitch      int    `json:"pitch,omitempty"`
				Speed      int    `json:"speed,omitempty"`
				Volume     int    `json:"volume,omitempty"`
				TtsAue     string `json:"tts_aue,omitempty"`
				SampleRate int    `json:"sample_rate"`
				Channels   int    `json:"channels"`
				BitDepth   int    `json:"bit_depth"`
			}{
				Lat: "31.83", Lng: "117.14", Aue: "raw", Encoding: encode, Eos: 300,
				Vcn: "x2_xiaojuan", Pitch: 60, Speed: 60, Volume: 50, TtsAue: "raw",
				SampleRate: sampleRate, Channels: channelCount, BitDepth: sampleSize * 8,
			}
		} else { // Default ASR scene (not fullduplex)
			// Python: param_iat = "{\"action\":\"start\",\"scene\":\"" + scene + "\", \"params\": {\"mid\": \"" + mid + "\",\"asr\":{\"eos\":" + str(eos) + "," + vpr_rec_string + f"\"encoding\": \"{encode}\",\"channels\": " + str(CHANNEL_COUNT) + ",\"bit_depth\": " + str(SAMPLE_SIZE * 8) + ",\"sample_rate\": " + str(SAMPLE_RATE) + ",\"pd\":\"midea|midea-dragon\"},\"nlu\":{\"vcn\": \"x2_xiaojuan\",\"pitch\": 60,\"speed\": 60,\"volume\": 50,\"tts_aue\": \"raw\"}}}"
			asrDetails := struct {
				Eos              int   `json:"eos"`
				EnableVprRec     *bool `json:"enable_vpr_rec,omitempty"`
				EnableVprRecInfo *bool `json:"enable_vpr_rec_info,omitempty"`
				EnableVprCluster *bool `json:"enable_vpr_cluster,omitempty"`
				VprRec           *struct {
					GroupID string `json:"group_id,omitempty"`
				} `json:"vpr_rec,omitempty"`
				Encoding   string `json:"encoding"`
				Channels   int    `json:"channels"`
				BitDepth   int    `json:"bit_depth"`
				SampleRate int    `json:"sample_rate"`
				Pd         string `json:"pd,omitempty"`
			}{
				Eos: eos, Encoding: encode, Channels: channelCount,
				BitDepth: sampleSize * 8, SampleRate: sampleRate, Pd: "midea|midea-dragon",
			}
			if enableVprRec {
				vprTrue := true
				asrDetails.EnableVprRec = &vprTrue
				asrDetails.EnableVprRecInfo = &vprTrue
				asrDetails.EnableVprCluster = &vprTrue
				asrDetails.VprRec = &struct {
					GroupID string `json:"group_id,omitempty"`
				}{GroupID: "abc_123"}
			} else {
				vprFalse := false // Explicitly set to false if enableVprRec is false
				asrDetails.EnableVprRec = &vprFalse
				asrDetails.EnableVprRecInfo = &vprFalse
				// EnableVprCluster is not explicitly set to false in Python if enableVprRec is false,
				// but it's safer to be explicit or rely on omitempty if the server handles absence as false.
				// For parity, let's match Python: only set to true if enableVprRec is true.
			}
			paramsData.Asr = &asrDetails

			paramsData.Nlu = &struct { // NLU params are also part of IAT in Python
				Vcn    string `json:"vcn,omitempty"`
				Pitch  int    `json:"pitch,omitempty"`
				Speed  int    `json:"speed,omitempty"`
				Volume int    `json:"volume,omitempty"`
				TtsAue string `json:"tts_aue,omitempty"`
				Text   string `json:"text,omitempty"` // Text field for NLU is not in Python's IAT NLU part
			}{Vcn: "x2_xiaojuan", Pitch: 60, Speed: 60, Volume: 50, TtsAue: "raw"}
		}
	}

	excelLock.Lock()
	// Python: new_worksheet.write(self.row, self.cols + 3, f"{mid}")
	cellMidExcel, _ := excelize.CoordinatesToCellName(client.cols+3+1, client.row)
	newWorkbook.SetCellValue(client.sheetName, cellMidExcel, clientMID) // Use clientMID which is in paramsData.Mid
	excelLock.Unlock()

	payload := WebSocketSendPayload{
		Action: "start",
		Scene:  scene, // scene is a global const
		Params: paramsData,
	}
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		customPrint(fmt.Sprintf("Error marshalling createSession payload for %s: %v", client.audioName, err))
		return
	}
	customPrint(fmt.Sprintf("Sending createSession for %s: %s", client.audioName, string(payloadBytes)))

	if err := client.conn.WriteMessage(websocket.TextMessage, payloadBytes); err != nil {
		customPrint(fmt.Sprintf("Error sending createSession for %s: %v", client.audioName, err))
	}
}

func (client *WsClient) sendAudioData(newWorkbook *excelize.File) {
	defer client.sendDataWg.Done()
	customPrint("Searching for audio file:", client.audioName, "in path:", audioPath)

	// Python: audio_file_list = find_dir(audio_path)
	// Python: all_audio_file = ';'.join(audio_file_list)
	// Python: audio_real_path = all_audio_file[:all_audio_file.find(self.audio_name)]
	// Python: with open(audio_real_path[audio_real_path.rfind(";") + 1:all_audio_file.find(self.audio_name)] + self.audio_name, 'rb') as f:
	// This Python logic for finding the exact path is a bit convoluted.
	// The Go `findDir` returns a list of full paths. We just need to find the one ending with `client.audioName`.
	audioFiles, err := findDir(audioPath)
	if err != nil {
		customPrint(fmt.Sprintf("Error finding audio directory %s: %v", audioPath, err))
		return
	}

	var audioFilePath string
	for _, fPath := range audioFiles {
		// Ensure it's an exact match for the filename part, not just a suffix within a longer name.
		if filepath.Base(fPath) == client.audioName {
			audioFilePath = fPath
			break
		}
	}
	// Fallback if exact base match fails, try suffix (closer to Python's original find behavior)
	if audioFilePath == "" {
		for _, fPath := range audioFiles {
			if strings.HasSuffix(fPath, client.audioName) {
				audioFilePath = fPath
				break
			}
		}
	}

	if audioFilePath == "" {
		customPrint(fmt.Sprintf("Audio file %s not found in %s or its subdirectories.", client.audioName, audioPath))
		return
	}
	customPrint(fmt.Sprintf("Found audio file for %s: %s", client.audioName, audioFilePath))

	audioData, err := os.ReadFile(audioFilePath)
	if err != nil {
		customPrint(fmt.Sprintf("Error reading audio file %s: %v", audioFilePath, err))
		return
	}

	sentDataSize := 0
	vadFlag := false
	// Python: end_point_flag = False (used to set conn.end_point only once)
	// Go: client.endPointReachedTime being 0 acts as this flag.

	sliceNum := (len(audioData) + sliceSize - 1) / sliceSize

	for i := 0; i < sliceNum; i++ {
		timeStart := time.Now()
		var sliceData []byte
		if (i+1)*sliceSize < len(audioData) {
			sliceData = audioData[i*sliceSize : (i+1)*sliceSize]
		} else {
			sliceData = audioData[i*sliceSize:]
		}

		// customPrint(fmt.Sprintf("Sending audio slice %d/%d for %s, size: %d bytes", i+1, sliceNum, client.audioName, len(sliceData)))
		if err := client.conn.WriteMessage(websocket.BinaryMessage, sliceData); err != nil {
			customPrint(fmt.Sprintf("Error sending audio slice for %s: %v", client.audioName, err))
			return
		}
		// customPrint(fmt.Sprintf("Successfully sent audio slice %d/%d for %s", i+1, sliceNum, client.audioName))
		sentDataSize += len(sliceData) // Update after successful send

		// Python: if conn.end_point_ms is not None:
		// Python:     if sent_data_size / (32000 / 1000) > conn.end_point_ms and not end_point_flag:
		// Python:         conn.end_point = time.time() * 1000
		// Python:         end_point_flag = True
		client.mu.Lock() // Protect access to endPointMs and endPointReachedTime
		if client.endPointMs != nil && client.endPointReachedTime == 0 {
			// 32000 bytes/sec = 32 bytes/ms
			if int64(sentDataSize)/32 > *client.endPointMs {
				client.endPointReachedTime = time.Now().UnixNano() / int64(time.Millisecond)
			}
		}
		client.mu.Unlock()

		select {
		case msg := <-client.messageQueue:
			if msg == "asr.vad.end" {
				vadFlag = true
				customPrint(fmt.Sprintf("VAD end signal received for %s during audio send, stopping.", client.audioName))
				// break // This break is for the select, outer loop needs to check vadFlag
			}
		default:
		}
		if vadFlag {
			break // Break the for loop
		}

		timeElapsed := time.Since(timeStart)
		// Python: time.sleep(INTERVAL / 1000 - 0.001 - time_elapsed / 1000)
		// Ensure sleep is not negative. The -0.001 is to ensure it sends slightly before interval.
		requiredSleep := time.Duration(interval)*time.Millisecond - time.Millisecond // Subtract 1ms for the -0.001 factor
		if timeElapsed < requiredSleep {
			time.Sleep(requiredSleep - timeElapsed)
		}
	}

	// Python: if not scene == "fullduplex": if not vad_flag: ... send silence
	if scene != "fullduplex" && !vadFlag {
		customPrint(fmt.Sprintf("Sending silence for %s as VAD not ended.", client.audioName))
		var silenceFilePath string
		if encode == "raw" {
			silenceFilePath = "data/silence.pcm" // Make sure these files exist
		} else {
			silenceFilePath = "data/silence.opus"
		}

		silenceFile, err := os.Open(silenceFilePath)
		if err != nil {
			customPrint(fmt.Sprintf("Error opening silence file %s: %v", silenceFilePath, err))
		} else {
			defer silenceFile.Close()
			reader := bufio.NewReader(silenceFile)
			secCount := 1 // Python: sec_count = 1
			// Python: while True: ... if sec_count >= 10: break
			for { // Loop for sending silence, mimicking Python's while True with sec_count break
				timeStart := time.Now()
				sliceData := make([]byte, sliceSize)
				n, readErr := reader.Read(sliceData) // Use readErr to check EOF

				if n > 0 {
					currentSlice := sliceData[:n]
					customPrint(fmt.Sprintf("Sending silence slice for %s, size: %d bytes", client.audioName, len(currentSlice)))
					if err := client.conn.WriteMessage(websocket.BinaryMessage, currentSlice); err != nil {
						customPrint(fmt.Sprintf("Error sending silence slice for %s: %v", client.audioName, err))
						break // Break from silence sending loop on error
					}
					customPrint(fmt.Sprintf("Successfully sent silence slice for %s", client.audioName))
					sentDataSize += n
				}

				// Check VAD and endpoint after sending, similar to main audio loop
				client.mu.Lock()
				if client.endPointMs != nil && client.endPointReachedTime == 0 {
					if int64(sentDataSize)/32 > *client.endPointMs {
						client.endPointReachedTime = time.Now().UnixNano() / int64(time.Millisecond)
					}
				}
				client.mu.Unlock()

				select {
				case msg := <-client.messageQueue:
					if msg == "asr.vad.end" {
						vadFlag = true
						customPrint(fmt.Sprintf("VAD end signal received for %s during silence send, stopping.", client.audioName))
						// break // for select
					}
				default:
				}
				if vadFlag {
					break // Break from silence sending loop
				}

				if readErr != nil || n == 0 { // If EOF or error during read
					if secCount >= 10 {
						customPrint(fmt.Sprintf("Max silence duration (10s) reached for %s.", client.audioName))
						break // Break from silence sending loop
					}
					_, seekErr := silenceFile.Seek(0, 0) // Rewind
					if seekErr != nil {
						customPrint(fmt.Sprintf("Error seeking silence file for %s: %v", client.audioName, seekErr))
						break
					}
					reader.Reset(silenceFile)
					secCount++
					customPrint(fmt.Sprintf("Silence sec_count for %s: %d", client.audioName, secCount))
					// Continue to next iteration of silence loop to read from rewound file
				}

				timeElapsed := time.Since(timeStart)
				requiredSleep := time.Duration(interval)*time.Millisecond - time.Millisecond
				if timeElapsed < requiredSleep {
					time.Sleep(requiredSleep - timeElapsed)
				}
			} // End of silence sending loop
		}
	}

	// Python: with excel_lock: new_worksheet.write(row, cols + 4, vad_data)
	// Python: new_worksheet.write(row, cols + 5, vad_data / (32000 / 1000))
	// vad_data in Python is sent_data_size.
	excelLock.Lock()
	cellVadDataSize, _ := excelize.CoordinatesToCellName(client.cols+4+1, client.row)
	newWorkbook.SetCellValue(client.sheetName, cellVadDataSize, sentDataSize)
	cellVadDataMs, _ := excelize.CoordinatesToCellName(client.cols+5+1, client.row)
	if sentDataSize > 0 { // Avoid division by zero if no data sent
		newWorkbook.SetCellValue(client.sheetName, cellVadDataMs, float64(sentDataSize)/32.0) // 32000 bytes/sec = 32 bytes/ms
	} else {
		newWorkbook.SetCellValue(client.sheetName, cellVadDataMs, 0)
	}
	excelLock.Unlock()

	customPrint(fmt.Sprintf("Finished sending data for %s at %s", client.audioName, time.Now().String()))
}

func (client *WsClient) sendEndTag() {
	payload := map[string]string{"action": "end"}
	payloadBytes, _ := json.Marshal(payload)
	customPrint(fmt.Sprintf("Sending end tag for %s: %s", client.audioName, string(payloadBytes)))
	if err := client.conn.WriteMessage(websocket.TextMessage, payloadBytes); err != nil {
		customPrint(fmt.Sprintf("Error sending end tag for %s: %v", client.audioName, err))
	}
}

func (client *WsClient) sendCloseSession() {
	payload := map[string]string{"action": "close"}
	payloadBytes, _ := json.Marshal(payload)
	customPrint(fmt.Sprintf("Sending close session for %s: %s", client.audioName, string(payloadBytes)))
	if err := client.conn.WriteMessage(websocket.TextMessage, payloadBytes); err != nil {
		customPrint(fmt.Sprintf("Error sending close session for %s: %v", client.audioName, err))
	}
}

var (
	iatSuccessMutex sync.Mutex
	iatFailMutex    sync.Mutex
)

func atomicIncrementIatSuccess() {
	iatSuccessMutex.Lock()
	iatSuccess++
	iatSuccessMutex.Unlock()
}
func atomicIncrementIatFail() {
	iatFailMutex.Lock()
	iatFail++
	iatFailMutex.Unlock()
}

func main() {
	threadNoFlag := flag.String("thread-no", "None", "Thread no")
	flag.Parse()
	threadNo = *threadNoFlag

	logDir := "./log"
	var logFileName string
	if threadNo != "None" {
		logFileName = fmt.Sprintf("%s/%s_sum_%s.log", logDir, startTime, threadNo)
	} else {
		logFileName = fmt.Sprintf("%s/%s.log", logDir, startTime)
	}
	var err error
	logFile, err = os.OpenFile(logFileName, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		log.Fatalf("Failed to open log file: %v", err)
	}
	defer func() {
		if logFile != nil {
			logFile.Close()
		}
	}()

	customPrint("Starting Midea test script in Go...")
	customPrint("Thread No (from arg):", threadNo)

	// Corresponds to parameters from Python's main function signature and example call:
	// main(config_file, extract_sheet=None, extract_sheet_num=0, is_parallel=False)
	// Example call: main(test_data, ['Sheet1'], 0, False)

	// targetSheetNames: if empty, process all sheets from the input file.
	// Defaulting to a specific sheet based on previous context in the Go script development.
	// If you want to process all sheets, leave it as: var targetSheetNames []string
	// If you want to match Python's example call ['Sheet1'], use: targetSheetNames = []string{"Sheet1"}
	targetSheetNames := []string{"313sp_off_a2"}

	// extractSheetNum: 0 means process all data rows in a sheet. >0 means process that many data rows.
	extractSheetNum := 0 // Corresponds to Python's extract_sheet_num = 0 (process all rows)
	// extractSheetNum := 1 // Previous Go hardcoding for 1 data row

	isParallel := false // Corresponds to Python's is_parallel = False

	// Python: old_workbook = xlrd.open_workbook(config_file, formatting_info=True)
	// Python: new_workbook = copy(old_workbook)
	// Go: We read from `testData` and write to a new `newWorkbook`.
	// Excelize doesn't have a direct "copy" like xlutils. We'll create a new file
	// and copy relevant data.

	originalExcelFile, err := excelize.OpenFile(testData)
	if err != nil {
		customPrint("Failed to open original Excel file:", testData, err)
		return
	}
	defer originalExcelFile.Close()

	baseName := strings.TrimSuffix(testData, filepath.Ext(testData))
	ext := filepath.Ext(testData)
	var newExcelName string
	if threadNo != "None" {
		newExcelName = fmt.Sprintf("%s_sum_%s_%s%s", baseName, startTime, threadNo, ext)
	} else {
		// Python: new_excel_name = f"{name}_结果汇总_{start_date}{extension}"
		newExcelName = fmt.Sprintf("%s_结果汇总_%s%s", baseName, startDate, ext)
	}

	newWorkbook := excelize.NewFile() // This will be our output file.

	// Python: worksheet_names = extract_sheet if extract_sheet else old_workbook.sheet_names()
	// Go: Using hardcoded targetSheetNames for now, similar to the Python script's example call.
	// If extract_sheet is empty in Python, it processes all sheets.
	// For this translation, let's assume targetSheetNames holds the sheets to process.
	// If targetSheetNames is empty, we could process all sheets from originalExcelFile.
	sheetsToProcess := []string{}
	if len(targetSheetNames) > 0 {
		sheetsToProcess = targetSheetNames
	} else {
		sheetsToProcess = originalExcelFile.GetSheetList()
	}

	// Python: extract_sheet_num = 0 (in example call main(test_data, ['Sheet1'], 0, False))
	// This means if extract_sheet_num + 1 (which is 1) is < old_worksheet.nrows, use it.
	// Effectively, if extract_sheet_num is 0, it means process all rows (rows = old_worksheet.nrows).
	// If extract_sheet_num is N > 0, it means process N data rows (i.e., N+1 total rows including header).
	// Let's make extractSheetNum in Go mean "number of data rows to process". If 0, process all.
	// The Python script's example `extract_sheet_num=0` means `rows = old_worksheet.nrows`.
	// Let's adjust `extractSheetNum` in Go to mean "max data rows to process", 0 means all.
	// Current Go `extractSheetNum = 1` means 1 data row.
	// Python: rows = min(old_worksheet.nrows, extract_sheet_num + 1 if 1 < extract_sheet_num + 1 < old_worksheet.nrows else old_worksheet.nrows)
	// If extract_sheet_num = 0 (from Python example), then extract_sheet_num + 1 = 1.
	// The condition `1 < 1 < old_worksheet.nrows` is false. So `rows = old_worksheet.nrows`.
	// So, if Go's `extractSheetNum` is to match Python's `0` for "all rows", it should be set to 0.
	// If Python's `extract_sheet_num` was, say, 5 (meaning 5 data rows), then `extract_sheet_num + 1 = 6`.
	// `min(total_rows, 6 if 1 < 6 < total_rows else total_rows)`.
	// This logic is a bit complex. Let's simplify: if `extractSheetNum` (Go) is > 0, it's the limit of data rows. If 0, all data rows.
	// The current Go code has `extractSheetNum = 1`, meaning it processes 1 data row.

	// isParallel is now defined in the block at the beginning of main,
	// along with targetSheetNames and extractSheetNum.
	var mainProcessingWg sync.WaitGroup

	for _, sheetName := range sheetsToProcess {
		if !contains(originalExcelFile.GetSheetList(), sheetName) {
			customPrint(fmt.Sprintf("Target sheet %s not found in %s. Skipping.", sheetName, testData))
			continue
		}
		customPrint("Processing sheet:", sheetName)

		// Reset counters for each sheet
		iatSuccessMutex.Lock()
		iatSuccess = 0
		iatSuccessMutex.Unlock()
		iatFailMutex.Lock()
		iatFail = 0
		iatFailMutex.Unlock()

		originalRows, err := originalExcelFile.GetRows(sheetName)
		if err != nil {
			customPrint(fmt.Sprintf("Failed to get rows from original sheet %s: %v", sheetName, err))
			continue
		}

		if len(originalRows) == 0 {
			customPrint(fmt.Sprintf("Original sheet %s is empty.", sheetName))
			continue
		}

		// Create the corresponding sheet in the new workbook
		_, err = newWorkbook.NewSheet(sheetName)
		if err != nil {
			customPrint(fmt.Sprintf("Failed to create new sheet %s: %v", sheetName, err))
			continue
		}

		// Copy header row from original to new
		headerRowOriginal := originalRows[0]
		colsCountOriginal := len(headerRowOriginal)
		audioIndex := -1
		expectResultIndex := -1
		endPointIndex := -1

		for i, colNameCell := range headerRowOriginal {
			cellName, _ := excelize.CoordinatesToCellName(i+1, 1) // 1-based indexing for excelize
			newWorkbook.SetCellValue(sheetName, cellName, colNameCell)

			// Python: audio_index = col_name.index('音频')
			if colNameCell == "音频" {
				audioIndex = i
			}
			// Python: expect_result_index = col_name.index('文本') if '文本' in col_name else None
			if colNameCell == "文本" {
				expectResultIndex = i
			}
			// Python: end_point_index = col_name.index('后端点') if '后端点' in col_name else None
			if colNameCell == "后端点" {
				endPointIndex = i
			}
		}

		if audioIndex == -1 {
			customPrint(fmt.Sprintf("Column '音频' not found in sheet %s. Skipping sheet.", sheetName))
			continue
		}

		// Write new headers
		// Python: new_worksheet.write(0, cols, f'识别结果-{start_date}') // cols is original_cols_count
		// So, the first new column is at index `colsCountOriginal`.
		newHeadersStartColIndex := colsCountOriginal // 0-indexed for map keys, but +1 for excelize
		headersToAdd := map[int]string{              // key is offset from newHeadersStartColIndex
			0: fmt.Sprintf("识别结果-%s", startDate),
			// col + 1 is for "测试结果" if expect_result_index is not None
			2:  fmt.Sprintf("asr结果的requestId-%s", startDate),
			3:  fmt.Sprintf("mid-%s", startDate),
			4:  fmt.Sprintf("接收vad时已发送数据大小-%s", startDate),
			5:  fmt.Sprintf("接收vad时的ms数-%s", startDate),
			6:  fmt.Sprintf("第一帧的时间-%s", startDate),
			7:  fmt.Sprintf("接收vad的时间-%s", startDate),
			8:  fmt.Sprintf("第一帧到接收vad的时间-%s", startDate),
			9:  fmt.Sprintf("接收finish为true的时间-%s", startDate),
			10: fmt.Sprintf("接收vad到finish为true的时间-%s", startDate),
			11: fmt.Sprintf("接收asr到nlp的时间-%s", startDate),
			// col + 12 for "从端点2到VAD时间" if end_point_index is not None
		}
		if expectResultIndex != -1 {
			headersToAdd[1] = fmt.Sprintf("测试结果-%s", startDate)
		}
		if endPointIndex != -1 {
			headersToAdd[12] = fmt.Sprintf("从端点2到VAD时间-%s", startDate)
		}

		for offset, headerText := range headersToAdd {
			cellName, _ := excelize.CoordinatesToCellName(newHeadersStartColIndex+offset+1, 1) // +1 for 1-based excelize
			newWorkbook.SetCellValue(sheetName, cellName, headerText)
		}

		// Determine number of data rows to process
		numDataRowsInSheet := len(originalRows) - 1
		numDataRowsToProcess := numDataRowsInSheet
		if extractSheetNum > 0 && extractSheetNum < numDataRowsInSheet { // extractSheetNum from const
			numDataRowsToProcess = extractSheetNum
		}

		for rIdx := 0; rIdx < numDataRowsToProcess; rIdx++ {
			dataRowIndexOriginal := rIdx + 1 // 0-indexed for originalRows slice, but data starts at originalRows[1]
			excelRowNumber := rIdx + 2       // 1-based for Excel (header is 1, first data is 2)

			if dataRowIndexOriginal >= len(originalRows) {
				break // Should not happen if numDataRowsToProcess is correct
			}
			rowData := originalRows[dataRowIndexOriginal]

			// Copy original row data to new workbook
			for cIdx, cellValue := range rowData {
				cellName, _ := excelize.CoordinatesToCellName(cIdx+1, excelRowNumber)
				newWorkbook.SetCellValue(sheetName, cellName, cellValue)
			}

			// Python: if old_worksheet.row_values(row)[audio_index] != ''
			if len(rowData) <= audioIndex || rowData[audioIndex] == "" {
				customPrint(fmt.Sprintf("Skipping Excel row %d in sheet %s: audio name is empty or row too short.", excelRowNumber, sheetName))
				continue
			}
			audioFile := rowData[audioIndex]

			var expectedTxt string
			if expectResultIndex != -1 && len(rowData) > expectResultIndex {
				expectedTxt = rowData[expectResultIndex]
			}

			var epMsVal *int64
			if endPointIndex != -1 && len(rowData) > endPointIndex && rowData[endPointIndex] != "" {
				valStr := strings.TrimSpace(rowData[endPointIndex])
				// Handle potential float strings like "300.0"
				valFloat, errFloat := strconv.ParseFloat(valStr, 64)
				if errFloat == nil {
					valInt := int64(valFloat)
					epMsVal = &valInt
				} else {
					// Try direct int parsing if float fails (e.g. "300")
					val, errInt := strconv.ParseInt(valStr, 10, 64)
					if errInt == nil {
						epMsVal = &val
					} else {
						customPrint(fmt.Sprintf("Warning: Could not parse '后端点' value '%s' for audio '%s' in sheet %s, Excel row %d. Errors: floatParse=%v, intParse=%v", valStr, audioFile, sheetName, excelRowNumber, errFloat, errInt))
					}
				}
			}

			customPrint(fmt.Sprintf("Sheet %s: Processing data (Excel row %d), Audio: %s", sheetName, excelRowNumber, audioFile))
			if isParallel && sheetName != "云端识别耗时" { // Python: sheet_name != '云端识别耗时'
				mainProcessingWg.Add(1)
				// Pass excelRowNumber (1-based) and colsCountOriginal (for calculating new column offsets)
				go processTask(&mainProcessingWg, sheetName, audioFile, excelRowNumber, colsCountOriginal, expectedTxt, epMsVal, newWorkbook, "")
			} else {
				processTask(nil, sheetName, audioFile, excelRowNumber, colsCountOriginal, expectedTxt, epMsVal, newWorkbook, "")
			}
		}

		if isParallel {
			mainProcessingWg.Wait()
		}

		// Calculate and write success rate for the current sheet
		// Python: if expect_result_index: success_rate = round(iat_success / (iat_success + iat_fail) * 100, 2)
		// Python: new_worksheet.write(rows, cols, '识别成功率：') // rows is num data rows + 1 (header)
		// Python: new_worksheet.write(rows, cols + 1, f'{success_rate}%')
		currentIatSuccess := 0
		currentIatFail := 0
		iatSuccessMutex.Lock()
		currentIatSuccess = iatSuccess
		iatSuccessMutex.Unlock()
		iatFailMutex.Lock()
		currentIatFail = iatFail
		iatFailMutex.Unlock()

		if expectResultIndex != -1 && (currentIatSuccess+currentIatFail > 0) {
			successRate := float64(currentIatSuccess) / float64(currentIatSuccess+currentIatFail) * 100.0
			// Write to the row after the last data row processed
			successRateExcelRow := numDataRowsToProcess + 1 + 1 // +1 for header, +1 for next row

			// Label in the first new column (index colsCountOriginal)
			cellLabel, _ := excelize.CoordinatesToCellName(newHeadersStartColIndex+1, successRateExcelRow)
			newWorkbook.SetCellValue(sheetName, cellLabel, "识别成功率：")

			// Value in the second new column (index colsCountOriginal + 1)
			cellValue, _ := excelize.CoordinatesToCellName(newHeadersStartColIndex+1+1, successRateExcelRow)
			newWorkbook.SetCellValue(sheetName, cellValue, fmt.Sprintf("%.2f%%", successRate))
		}
	}

	// Delete "Sheet1" if it was created by default by NewFile() and not part of sheetsToProcess
	// and not an original sheet that was meant to be processed.
	defaultSheetName := "Sheet1" // Default name excelize.NewFile() might create
	if !contains(sheetsToProcess, defaultSheetName) {
		// Check if "Sheet1" was an intended processing target or an original sheet.
		// If it's in originalExcelFile.GetSheetList() AND in sheetsToProcess, it's handled.
		// If it's NOT in sheetsToProcess, it means we didn't explicitly want to process it.
		// If NewFile() created it and we didn't use it, delete it.
		idx, _ := newWorkbook.GetSheetIndex(defaultSheetName)
		if idx != -1 { // If "Sheet1" exists in the new workbook
			isSheet1Processed := false
			for _, processedName := range sheetsToProcess {
				if processedName == defaultSheetName {
					isSheet1Processed = true
					break
				}
			}
			if !isSheet1Processed {
				newWorkbook.DeleteSheet(defaultSheetName)
			}
		}
	}

	// Set active sheet to the first processed sheet if any
	if len(sheetsToProcess) > 0 {
		firstProcessedSheet := sheetsToProcess[0]
		idx, err := newWorkbook.GetSheetIndex(firstProcessedSheet)
		if err == nil && idx != -1 {
			newWorkbook.SetActiveSheet(idx)
		}
	}

	if err := newWorkbook.SaveAs(newExcelName); err != nil {
		customPrint("Failed to save new Excel file:", newExcelName, err)
	} else {
		customPrint("New Excel file saved as:", newExcelName)
	}

	customPrint("#####运行完成,点击查看结果按钮查看结果!##########")
}

func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
