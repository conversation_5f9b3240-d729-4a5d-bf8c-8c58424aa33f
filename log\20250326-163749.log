1742978270
param:b'{\n            "auth_id": "cdf211f7553644e88b858bf820e894e0",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid12910f9c@dx21131b3e985e3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "56226e578e624521929035be45526b20","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bbf26@dx195d199c798b8a9532"}
started:
ws start
####################
测试进行: ctm0001030e@hu17b4cb4fb36020c902#46275217.pcm
{"recordId":"ase000f91a4@hu195d199d63105c2882:56226e578e624521929035be45526b20","requestId":"ase000f91a4@hu195d199d63105c2882","sessionId":"cid000bbf26@dx195d199c798b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1742978275}
{"recordId":"gty000bbf27@dx195d199c7f5b8a9532:56226e578e624521929035be45526b20","requestId":"gty000bbf27@dx195d199c7f5b8a9532","sessionId":"cid000bbf26@dx195d199c798b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1742978275}
{"recordId":"gty000bbf27@dx195d199c7f5b8a9532:56226e578e624521929035be45526b20","requestId":"gty000bbf27@dx195d199c7f5b8a9532","sessionId":"cid000bbf26@dx195d199c798b8a9532","eof":"1","text":"电视怎么调台","pinyin":"","topic":"asr.speech.result","age":"elder","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1742978275}
send data finished:1742978276.1792395
{"recordId":"gty000bbf27@dx195d199c7f5b8a9532:56226e578e624521929035be45526b20","requestId":"gty000bbf27@dx195d199c7f5b8a9532","sessionId":"cid000bbf26@dx195d199c798b8a9532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"电视怎么调台","intentId":"chat","intentName":"闲聊","nlg":"我是诚实的好孩子，这个问题我也不会。","shouldEndSession":true},"nlu":{"input":"电视怎么调台","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1742978276}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidd78706d0@dxe7e91b3e98643eef00"}
连接正常关闭
1742978276
param:b'{\n            "auth_id": "6777c2610b104deeb717e7b26ebe92c2",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidfa92c6c0@dx5fc21b3e98643eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "bfe88ea891ce4a3fa615e82d742b396c","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bbf28@dx195d199dcb3b8a9532"}
started:
ws start
####################
测试进行: ctm00010320@hu17b4cb503aa020c902#46275237.pcm
{"recordId":"gty000bbf29@dx195d199dcfbb8a9532:bfe88ea891ce4a3fa615e82d742b396c","requestId":"gty000bbf29@dx195d199dcfbb8a9532","sessionId":"cid000bbf28@dx195d199dcb3b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1742978280}
{"recordId":"gty000bbf29@dx195d199dcfbb8a9532:bfe88ea891ce4a3fa615e82d742b396c","requestId":"gty000bbf29@dx195d199dcfbb8a9532","sessionId":"cid000bbf28@dx195d199dcb3b8a9532","eof":"1","text":"杨幂是谁","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1742978280}
send data finished:1742978281.158626
{"recordId":"gty000bbf29@dx195d199dcfbb8a9532:bfe88ea891ce4a3fa615e82d742b396c","requestId":"gty000bbf29@dx195d199dcfbb8a9532","sessionId":"cid000bbf28@dx195d199dcb3b8a9532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"杨幂是谁","intentId":"chat","intentName":"闲聊","nlg":"杨幂，1986年9月12日出生于北京市，演员、歌手、制片人，代表作有《神雕侠侣》、《亲爱的翻译官》、《三生三世十里桃花》等。","shouldEndSession":true},"nlu":{"input":"杨幂是谁","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1742978281}
{"recordId":"ase000e127b@hu195d199ee4905bf882:bfe88ea891ce4a3fa615e82d742b396c","requestId":"ase000e127b@hu195d199ee4905bf882","sessionId":"cid000bbf28@dx195d199dcb3b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1742978281}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid646597c5@dx366e1b3e98693eef00"}
连接正常关闭
1742978281
param:b'{\n            "auth_id": "b6ecc67412214413a320d939e69b382f",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid84fec525@dxed761b3e98693eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "b7dc571a0c7342ffb09b670b6aa24f3c","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bd48b@dx195d199f0117844532"}
started:
ws start
####################
测试进行: ctm00010329@hu17b4cb507a7020c902#46275249.pcm
{"recordId":"ase000e23de@hu195d199fffe1323882:b7dc571a0c7342ffb09b670b6aa24f3c","requestId":"ase000e23de@hu195d199fffe1323882","sessionId":"cid000bd48b@dx195d199f0117844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1742978285}
{"recordId":"gty000bd48c@dx195d199f04d7844532:b7dc571a0c7342ffb09b670b6aa24f3c","requestId":"gty000bd48c@dx195d199f04d7844532","sessionId":"cid000bd48b@dx195d199f0117844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1742978287}
{"recordId":"gty000bd48c@dx195d199f04d7844532:b7dc571a0c7342ffb09b670b6aa24f3c","requestId":"gty000bd48c@dx195d199f04d7844532","sessionId":"cid000bd48b@dx195d199f0117844532","eof":"1","text":"你可不可以学一下鸡的叫声","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1742978287}
send data finished:1742978288.1309962
{"recordId":"gty000bd48c@dx195d199f04d7844532:b7dc571a0c7342ffb09b670b6aa24f3c","requestId":"gty000bd48c@dx195d199f04d7844532","sessionId":"cid000bd48b@dx195d199f0117844532","topic":"dm.output","skill":"动物叫声","skillId":"IFLYTEK.animalCries","speakUrl":"","error":{},"dm":{"input":"你可不可以学一下鸡的叫声","intentId":"PLAY","intentName":"播放","nlg":"让我们听听小鸡是怎么叫的","widget":{"content":[{"type":"小鸡","title":"小鸡","tags":"","linkUrl":"http://aiui.storage.iflyresearch.com/ctimusic/animalCries/xiaoji.mp3","extra":{"source":"iflytek"}},{"type":"鸡","title":"公鸡","tags":"","linkUrl":"http://aiui.storage.iflyresearch.com/ctimusic/animalCries/gongji.mp3","extra":{"source":"iflytek"}},{"type":"鸡","title":"母鸡","tags":"","linkUrl":"http://aiui.storage.iflyresearch.com/ctimusic/animalCries/muji.mp3","extra":{"source":"iflytek"}}]},"shouldEndSession":true},"nlu":{"input":"你可不可以学一下鸡的叫声","skill":"动物叫声","skillId":"IFLYTEK.animalCries","skillVersion":"7.0","semantics":{"request":{"slots":[{"name":"category","value":"鸡"}]}}},"timestamp":1742978287}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidcf374e5a@dx79ff1b3e986f3eef00"}
连接正常关闭
1742978288
param:b'{\n            "auth_id": "20fa931831164bf8a49d17e4de0f4b60",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid7d98bc3e@dx44641b3e98703eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "562c7d37474e4c569909b9b69ef94bae","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bccf8@dx195d19a0accb86a532"}
started:
ws start
####################
测试进行: ctm00010424@hu17b59a7c006020c902#46466943.pcm
{"recordId":"ase000eedc6@hu195d19a1ab805c3882:562c7d37474e4c569909b9b69ef94bae","requestId":"ase000eedc6@hu195d19a1ab805c3882","sessionId":"cid000bccf8@dx195d19a0accb86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1742978292}
{"recordId":"gty000bccf9@dx195d19a0b0ab86a532:562c7d37474e4c569909b9b69ef94bae","requestId":"gty000bccf9@dx195d19a0b0ab86a532","sessionId":"cid000bccf8@dx195d19a0accb86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1742978293}
{"recordId":"gty000bccf9@dx195d19a0b0ab86a532:562c7d37474e4c569909b9b69ef94bae","requestId":"gty000bccf9@dx195d19a0b0ab86a532","sessionId":"cid000bccf8@dx195d19a0accb86a532","eof":"1","text":"我要听易烊千玺的歌","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1742978293}
send data finished:1742978293.902677
{"recordId":"gty000bccf9@dx195d19a0b0ab86a532:562c7d37474e4c569909b9b69ef94bae","requestId":"gty000bccf9@dx195d19a0b0ab86a532","sessionId":"cid000bccf8@dx195d19a0accb86a532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"我要听易烊千玺的歌","intentId":"PLAY","intentName":"点歌","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"我要听易烊千玺的歌","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"歌手名","value":"易烊千玺"}]}}},"timestamp":1742978293}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid5d6f2a3a@dxafcb1b3e98753eef00"}
连接正常关闭
1742978294
param:b'{\n            "auth_id": "5c6c01fd7cc44356ae8c38a9b33e82a8",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidc31d41a9@dx7bfa1b3e98753eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "610fc98122534683be65b969eeabfafe","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bce0a@dx195d19a2156b8aa532"}
started:
ws start
####################
测试进行: ctm00010435@hu17b59a7c588020c902#46466960.pcm
{"recordId":"ase000db39b@hu195d19a2f570427882:610fc98122534683be65b969eeabfafe","requestId":"ase000db39b@hu195d19a2f570427882","sessionId":"cid000bce0a@dx195d19a2156b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1742978297}
{"recordId":"gty000bce0b@dx195d19a218fb8aa532:610fc98122534683be65b969eeabfafe","requestId":"gty000bce0b@dx195d19a218fb8aa532","sessionId":"cid000bce0a@dx195d19a2156b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1742978298}
{"recordId":"gty000bce0b@dx195d19a218fb8aa532:610fc98122534683be65b969eeabfafe","requestId":"gty000bce0b@dx195d19a218fb8aa532","sessionId":"cid000bce0a@dx195d19a2156b8aa532","eof":"1","text":"播放前一首","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1742978298}
send data finished:1742978298.6770854
{"recordId":"gty000bce0b@dx195d19a218fb8aa532:610fc98122534683be65b969eeabfafe","requestId":"gty000bce0b@dx195d19a218fb8aa532","sessionId":"cid000bce0a@dx195d19a2156b8aa532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"播放前一首","intentId":"INSTRUCTION","intentName":"音乐控制","nlg":"","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"播放前一首","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"操作","value":"past"}]}}},"timestamp":1742978298}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cide14567be@dx97941b3e987a3eef00"}
连接正常关闭
#####运行完成,点击查看结果按钮查看结果!##########
