1741329623
param:b'{\n            "auth_id": "6c2d572cf41749f2a866dac963c68192",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid33a37ec2@dxd2f81b25e0d83eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "d6530a1b91fd4f35bea9d4e8fe72baee","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3ce4@dx1956f556c837844532"}
started:
ws start
####################
测试进行: ctm0001030e@hu17b4cb4fb36020c902#46275217.pcm
{"recordId":"ase000d2639@hu1956f557bb70427882:d6530a1b91fd4f35bea9d4e8fe72baee","requestId":"ase000d2639@hu1956f557bb70427882","sessionId":"cid000b3ce4@dx1956f556c837844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741329628}
{"recordId":"gty000b3ce5@dx1956f556d3a7844532:d6530a1b91fd4f35bea9d4e8fe72baee","requestId":"gty000b3ce5@dx1956f556d3a7844532","sessionId":"cid000b3ce4@dx1956f556c837844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741329628}
{"recordId":"gty000b3ce5@dx1956f556d3a7844532:d6530a1b91fd4f35bea9d4e8fe72baee","requestId":"gty000b3ce5@dx1956f556d3a7844532","sessionId":"cid000b3ce4@dx1956f556c837844532","eof":"1","text":"电视怎么调","pinyin":"","topic":"asr.speech.result","age":"elder","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741329628}
send data finished:1741329628.9028404
{"recordId":"gty000b3ce5@dx1956f556d3a7844532:d6530a1b91fd4f35bea9d4e8fe72baee","requestId":"gty000b3ce5@dx1956f556d3a7844532","sessionId":"cid000b3ce4@dx1956f556c837844532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"电视怎么调","intentId":"chat","intentName":"闲聊","nlg":"这个问题有点难，你还是换一个吧。","shouldEndSession":true},"nlu":{"input":"电视怎么调","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741329629}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid0f67b3f2@dx7a881b25e0dd3eef00"}
连接正常关闭
1741329629
param:b'{\n            "auth_id": "af7e3bccc6ff4651ba7a9a7314ea9ab9",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidfbf3e367@dx48bf1b25e0dd3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "abaaf97e40b84f428ff90bc75da987d6","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3ce9@dx1956f5583827844532"}
started:
ws start
####################
测试进行: ctm00010320@hu17b4cb503aa020c902#46275237.pcm
{"recordId":"gty000b3cea@dx1956f5584437844532:abaaf97e40b84f428ff90bc75da987d6","requestId":"gty000b3cea@dx1956f5584437844532","sessionId":"cid000b3ce9@dx1956f5583827844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741329634}
{"recordId":"gty000b3cea@dx1956f5584437844532:abaaf97e40b84f428ff90bc75da987d6","requestId":"gty000b3cea@dx1956f5584437844532","sessionId":"cid000b3ce9@dx1956f5583827844532","eof":"1","text":"杨幂是谁","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741329634}
send data finished:1741329634.374786
{"recordId":"gty000b3cea@dx1956f5584437844532:abaaf97e40b84f428ff90bc75da987d6","requestId":"gty000b3cea@dx1956f5584437844532","sessionId":"cid000b3ce9@dx1956f5583827844532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"杨幂是谁","intentId":"chat","intentName":"闲聊","nlg":"杨幂，1986年9月12日出生于北京市，演员、歌手、制片人，代表作有《神雕侠侣》、《亲爱的翻译官》、《三生三世十里桃花》等。","shouldEndSession":true},"nlu":{"input":"杨幂是谁","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741329634}
{"recordId":"ase000e9183@hu1956f55951605bf882:abaaf97e40b84f428ff90bc75da987d6","requestId":"ase000e9183@hu1956f55951605bf882","sessionId":"cid000b3ce9@dx1956f5583827844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741329634}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid71039884@dx49e01b25e0e23eef00"}
连接正常关闭
1741329634
param:b'{\n            "auth_id": "3cef89ec42b64a47b9575ac3ffcc54ac",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid34de3b1b@dx22151b25e0e23eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "8444ca627abe4ec1824ca495599d0960","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3ced@dx1956f5597307844532"}
started:
ws start
####################
测试进行: ctm00010329@hu17b4cb507a7020c902#46275249.pcm
{"recordId":"ase000fd70a@hu1956f55a77705c4882:8444ca627abe4ec1824ca495599d0960","requestId":"ase000fd70a@hu1956f55a77705c4882","sessionId":"cid000b3ced@dx1956f5597307844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741329639}
{"recordId":"gty000b3cee@dx1956f5597d17844532:8444ca627abe4ec1824ca495599d0960","requestId":"gty000b3cee@dx1956f5597d17844532","sessionId":"cid000b3ced@dx1956f5597307844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741329641}
{"recordId":"gty000b3cee@dx1956f5597d17844532:8444ca627abe4ec1824ca495599d0960","requestId":"gty000b3cee@dx1956f5597d17844532","sessionId":"cid000b3ced@dx1956f5597307844532","eof":"1","text":"你可不可以学一下鸡的叫声","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741329641}
send data finished:1741329641.366541
{"recordId":"gty000b3cee@dx1956f5597d17844532:8444ca627abe4ec1824ca495599d0960","requestId":"gty000b3cee@dx1956f5597d17844532","sessionId":"cid000b3ced@dx1956f5597307844532","topic":"dm.output","skill":"动物叫声","skillId":"IFLYTEK.animalCries","speakUrl":"","error":{},"dm":{"input":"你可不可以学一下鸡的叫声","intentId":"PLAY","intentName":"播放","nlg":"公鸡是这样叫的","widget":{"content":[{"type":"鸡","title":"公鸡","tags":"","linkUrl":"http://aiui.storage.iflyresearch.com/ctimusic/animalCries/gongji.mp3","extra":{"source":"iflytek"}},{"type":"鸡","title":"母鸡","tags":"","linkUrl":"http://aiui.storage.iflyresearch.com/ctimusic/animalCries/muji.mp3","extra":{"source":"iflytek"}},{"type":"小鸡","title":"小鸡","tags":"","linkUrl":"http://aiui.storage.iflyresearch.com/ctimusic/animalCries/xiaoji.mp3","extra":{"source":"iflytek"}}]},"shouldEndSession":true},"nlu":{"input":"你可不可以学一下鸡的叫声","skill":"动物叫声","skillId":"IFLYTEK.animalCries","skillVersion":"7.0","semantics":{"request":{"slots":[{"name":"category","value":"鸡"}]}}},"timestamp":1741329641}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"ciddcaf7d06@dxec991b25e0e93eef00"}
连接正常关闭
1741329641
param:b'{\n            "auth_id": "5a3aaa0b56f7476bb12fa4bac7afdc3f",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid31b8d1d4@dxd3ea1b25e0e93eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "d385f5103f8247b6996587ef016d0f05","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3bf7@dx1956f55b116b8aa532"}
started:
ws start
####################
测试进行: ctm00010424@hu17b59a7c006020c902#46466943.pcm
{"recordId":"ase000dbed8@hu1956f55c06b04d3882:d385f5103f8247b6996587ef016d0f05","requestId":"ase000dbed8@hu1956f55c06b04d3882","sessionId":"cid000b3bf7@dx1956f55b116b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741329645}
{"recordId":"gty000b3bf8@dx1956f55b1bdb8aa532:d385f5103f8247b6996587ef016d0f05","requestId":"gty000b3bf8@dx1956f55b1bdb8aa532","sessionId":"cid000b3bf7@dx1956f55b116b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741329646}
{"recordId":"gty000b3bf8@dx1956f55b1bdb8aa532:d385f5103f8247b6996587ef016d0f05","requestId":"gty000b3bf8@dx1956f55b1bdb8aa532","sessionId":"cid000b3bf7@dx1956f55b116b8aa532","eof":"1","text":"我要听易烊千玺的歌","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741329646}
send data finished:1741329646.928282
{"recordId":"gty000b3bf8@dx1956f55b1bdb8aa532:d385f5103f8247b6996587ef016d0f05","requestId":"gty000b3bf8@dx1956f55b1bdb8aa532","sessionId":"cid000b3bf7@dx1956f55b116b8aa532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"我要听易烊千玺的歌","intentId":"PLAY","intentName":"点歌","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"我要听易烊千玺的歌","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"歌手名","value":"易烊千玺"}]}}},"timestamp":1741329646}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid8a1d0872@dx60da1b25e0ee3eef00"}
连接正常关闭
1741329647
param:b'{\n            "auth_id": "3c4ef39fa8554c81b54513d683c2d4cd",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid4634a925@dx5cfa1b25e0ef3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "fb331df2d7a344dca2063425575fa79c","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3cf6@dx1956f55c6df7844532"}
started:
ws start
####################
测试进行: ctm00010435@hu17b59a7c588020c902#46466960.pcm
{"recordId":"ase000e712c@hu1956f55d53c05c3882:fb331df2d7a344dca2063425575fa79c","requestId":"ase000e712c@hu1956f55d53c05c3882","sessionId":"cid000b3cf6@dx1956f55c6df7844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741329651}
{"recordId":"gty000b3cf7@dx1956f55c7857844532:fb331df2d7a344dca2063425575fa79c","requestId":"gty000b3cf7@dx1956f55c7857844532","sessionId":"cid000b3cf6@dx1956f55c6df7844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741329651}
{"recordId":"gty000b3cf7@dx1956f55c7857844532:fb331df2d7a344dca2063425575fa79c","requestId":"gty000b3cf7@dx1956f55c7857844532","sessionId":"cid000b3cf6@dx1956f55c6df7844532","eof":"1","text":"播放前一首","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741329651}
send data finished:1741329651.515791
{"recordId":"gty000b3cf7@dx1956f55c7857844532:fb331df2d7a344dca2063425575fa79c","requestId":"gty000b3cf7@dx1956f55c7857844532","sessionId":"cid000b3cf6@dx1956f55c6df7844532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"播放前一首","intentId":"INSTRUCTION","intentName":"音乐控制","nlg":"","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"播放前一首","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"操作","value":"past"}]}}},"timestamp":1741329651}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid69edcf07@dxffb21b25e0f33eef00"}
连接正常关闭
1741329651
param:b'{\n            "auth_id": "a09ab75f79a24395934b495c686c34f3",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidd4388e72@dxba931b25e0f33eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "bf4b72eb34144868ad75ddc6511cd74c","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3926@dx1956f55d8c0b8a9532"}
started:
ws start
####################
测试进行: ctm00010443@hu17b59a7c8d1020c902#46466977.pcm
{"recordId":"ase000dc482@hu1956f55e7b804d3882:bf4b72eb34144868ad75ddc6511cd74c","requestId":"ase000dc482@hu1956f55e7b804d3882","sessionId":"cid000b3926@dx1956f55d8c0b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741329655}
{"recordId":"gty000b3927@dx1956f55d964b8a9532:bf4b72eb34144868ad75ddc6511cd74c","requestId":"gty000b3927@dx1956f55d964b8a9532","sessionId":"cid000b3926@dx1956f55d8c0b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741329656}
{"recordId":"gty000b3927@dx1956f55d964b8a9532:bf4b72eb34144868ad75ddc6511cd74c","requestId":"gty000b3927@dx1956f55d964b8a9532","sessionId":"cid000b3926@dx1956f55d8c0b8a9532","eof":"1","text":"快进一分钟6秒","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741329656}
send data finished:1741329656.4480808
{"recordId":"gty000b3927@dx1956f55d964b8a9532:bf4b72eb34144868ad75ddc6511cd74c","requestId":"gty000b3927@dx1956f55d964b8a9532","sessionId":"cid000b3926@dx1956f55d8c0b8a9532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"快进一分钟6秒","intentId":"INSTRUCTION","intentName":"音乐控制","nlg":"","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"快进一分钟6秒","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"操作","value":"speed"},{"name":"分钟","value":"1"},{"name":"秒","value":"6"}]}}},"timestamp":1741329656}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid9709369d@dx6f4c1b25e0f83eef00"}
连接正常关闭
1741329656
param:b'{\n            "auth_id": "2b79a4be131d4d6f8fa956c9b27422d9",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid3c237d00@dxfc561b25e0f83eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "d5d481b168d24673be1765e93ca5be65","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b392a@dx1956f55ebfcb8a9532"}
started:
ws start
####################
测试进行: ctm00010558@hu17b62d10b3d020c902#46579979.pcm
{"recordId":"ase000ea11a@hu1956f55fef605bf882:d5d481b168d24673be1765e93ca5be65","requestId":"ase000ea11a@hu1956f55fef605bf882","sessionId":"cid000b392a@dx1956f55ebfcb8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741329661}
{"recordId":"gty000b392b@dx1956f55eca3b8a9532:d5d481b168d24673be1765e93ca5be65","requestId":"gty000b392b@dx1956f55eca3b8a9532","sessionId":"cid000b392a@dx1956f55ebfcb8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741329663}
{"recordId":"gty000b392b@dx1956f55eca3b8a9532:d5d481b168d24673be1765e93ca5be65","requestId":"gty000b392b@dx1956f55eca3b8a9532","sessionId":"cid000b392a@dx1956f55ebfcb8a9532","eof":"1","text":"我想看电视剧","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741329663}
send data finished:1741329663.8225467
{"recordId":"gty000b392b@dx1956f55eca3b8a9532:d5d481b168d24673be1765e93ca5be65","requestId":"gty000b392b@dx1956f55eca3b8a9532","sessionId":"cid000b392a@dx1956f55ebfcb8a9532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"我想看电视剧","intentId":"chat","intentName":"闲聊","nlg":"看亦可，不看亦可。","shouldEndSession":true},"nlu":{"input":"我想看电视剧","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741329663}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidcffb27a6@dx23c11b25e1003eef00"}
连接正常关闭
1741329664
param:b'{\n            "auth_id": "366440630e2344bf810470ff110bc2ef",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid22adf348@dxeb3c1b25e1003eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "453f6cbe2ede43b19b91ab6d565cfb12","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3b14@dx1956f56096cb86a532"}
started:
ws start
####################
测试进行: ctm00010562@hu17b62d11176020c902#46579992.pcm
{"recordId":"gty000b3b15@dx1956f560a18b86a532:453f6cbe2ede43b19b91ab6d565cfb12","requestId":"gty000b3b15@dx1956f560a18b86a532","sessionId":"cid000b3b14@dx1956f56096cb86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741329671}
{"recordId":"gty000b3b15@dx1956f560a18b86a532:453f6cbe2ede43b19b91ab6d565cfb12","requestId":"gty000b3b15@dx1956f560a18b86a532","sessionId":"cid000b3b14@dx1956f56096cb86a532","eof":"1","text":"东京是城市吗","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741329671}
send data finished:1741329671.5671625
{"recordId":"ase000f28ec@hu1956f56256805c2882:453f6cbe2ede43b19b91ab6d565cfb12","requestId":"ase000f28ec@hu1956f56256805c2882","sessionId":"cid000b3b14@dx1956f56096cb86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741329671}
{"recordId":"gty000b3b15@dx1956f560a18b86a532:453f6cbe2ede43b19b91ab6d565cfb12","requestId":"gty000b3b15@dx1956f560a18b86a532","sessionId":"cid000b3b14@dx1956f56096cb86a532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"东京是城市吗","intentId":"chat","intentName":"闲聊","nlg":"东京是日本首都。","shouldEndSession":true},"nlu":{"input":"东京是城市吗","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741329671}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid8988d494@dxd4c71b25e1073eef00"}
连接正常关闭
1741329671
param:b'{\n            "auth_id": "5c0388f7fd8b4e71a7d109d42f7da1a1",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"ciddcff580e@dx6a9e1b25e1073eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "24e9df7c39d14e7c966e204ab66bdbb2","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3b1f@dx1956f5627a9b86a532"}
started:
ws start
####################
测试进行: ctm00010573@hu17b62d11b05020c902#46580006.pcm
{"recordId":"ase000dd0ed@hu1956f563bcd04d3882:24e9df7c39d14e7c966e204ab66bdbb2","requestId":"ase000dd0ed@hu1956f563bcd04d3882","sessionId":"cid000b3b1f@dx1956f5627a9b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741329677}
{"recordId":"gty000b3b20@dx1956f562847b86a532:24e9df7c39d14e7c966e204ab66bdbb2","requestId":"gty000b3b20@dx1956f562847b86a532","sessionId":"cid000b3b1f@dx1956f5627a9b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741329678}
{"recordId":"gty000b3b20@dx1956f562847b86a532:24e9df7c39d14e7c966e204ab66bdbb2","requestId":"gty000b3b20@dx1956f562847b86a532","sessionId":"cid000b3b1f@dx1956f5627a9b86a532","eof":"1","text":"设置今天晚上6:00的闹钟","pinyin":"","topic":"asr.speech.result","age":"elder","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741329678}
send data finished:1741329678.8202271
{"recordId":"gty000b3b20@dx1956f562847b86a532:24e9df7c39d14e7c966e204ab66bdbb2","requestId":"gty000b3b20@dx1956f562847b86a532","sessionId":"cid000b3b1f@dx1956f5627a9b86a532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"设置今天晚上6:00的闹钟","intentId":"chat","intentName":"闲聊","nlg":"这个我不太懂，我们聊聊别的吧。","shouldEndSession":true},"nlu":{"input":"设置今天晚上6:00的闹钟","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741329678}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidd65e6bb4@dx60921b25e10f3eef00"}
连接正常关闭
1741329679
param:b'{\n            "auth_id": "4832085d22b04694b7ee510b634d61a8",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid69fdd454@dxde531b25e10f3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "b35aaeab98a745a692f58d0a842db04f","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3b29@dx1956f56440eb86a532"}
started:
ws start
####################
测试进行: ctm00010586@hu17b62d120df020c902#46580018.pcm
{"recordId":"ase000f3081@hu1956f56590e05c2882:b35aaeab98a745a692f58d0a842db04f","requestId":"ase000f3081@hu1956f56590e05c2882","sessionId":"cid000b3b29@dx1956f56440eb86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741329684}
{"recordId":"gty000b3b2a@dx1956f5644b4b86a532:b35aaeab98a745a692f58d0a842db04f","requestId":"gty000b3b2a@dx1956f5644b4b86a532","sessionId":"cid000b3b29@dx1956f56440eb86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741329685}
{"recordId":"gty000b3b2a@dx1956f5644b4b86a532:b35aaeab98a745a692f58d0a842db04f","requestId":"gty000b3b2a@dx1956f5644b4b86a532","sessionId":"cid000b3b29@dx1956f56440eb86a532","eof":"1","text":"今天太阳什么时候出来","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741329685}
send data finished:1741329685.8685462
{"recordId":"gty000b3b2a@dx1956f5644b4b86a532:b35aaeab98a745a692f58d0a842db04f","requestId":"gty000b3b2a@dx1956f5644b4b86a532","sessionId":"cid000b3b29@dx1956f56440eb86a532","topic":"dm.output","skill":"天气","skillId":"2019042500000544","speakUrl":"","error":{},"dm":{"input":"今天太阳什么时候出来","intentId":"QUERY","intentName":"查询天气信息","nlg":"今天北京市全天阴，0℃ ~ 9℃，南风3-4级，有点冷。","widget":{"webhookResp":{"result":[{"airData":89,"airQuality":"良","city":"北京市","date":"2025-03-07","dateLong":1741276800,"date_for_voice":"今天","exp":{"ct":{"expName":"穿衣指数","level":"凉","prompt":"天气偏凉，可以穿上最时尚的那件大衣来凹造型，搭一条围巾时尚指数爆表。"},"dy":{"expName":"钓鱼指数","level":"较适宜","prompt":"温差稍大，鱼儿不太活跃，可能会对钓鱼产生影响。"},"gm":{"expName":"感冒指数","level":"易发","prompt":"感冒容易发生，少去人群密集的场所有利于降低感冒的几率。"},"jt":{"expName":"交通指数","level":"良好","prompt":"阴天，路面干燥，交通气象条件良好，车辆可以正常行驶。"},"tr":{"expName":"旅游指数","level":"适宜","prompt":"天气较好，气温稍低，会感觉稍微有点凉，不过也是个好天气哦。适宜旅游，可不要错过机会呦！"},"uv":{"expName":"紫外线指数","level":"最弱","prompt":"辐射弱，请涂擦SPF8-12防晒护肤品。"},"xc":{"expName":"洗车指数","level":"较适宜","prompt":"较适宜洗车，未来一天无雨，风力较小，擦洗一新的汽车至少能保持一天。"},"yd":{"expName":"运动指数","level":"不适宜","prompt":"气温过低，特别容易着凉感冒，较不适宜户外运动，建议室内运动。"}},"extra":"","humidity":"31%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-03-07 14:00:08","pm25":"89","precipitation":"0","sunRise":"2025-03-07 06:38:00","sunSet":"2025-03-07 18:12:00","temp":9,"tempHigh":"9℃","tempLow":"0℃","tempRange":"0℃ ~ 9℃","tempReal":"5℃","visibility":"","warning":"","weather":"阴","weatherDescription":"有点冷。","weatherDescription3":"0℃到9℃，风不大，有点冷。","weatherDescription7":"0℃到9℃，风不大，有点冷。","weatherType":2,"week":"周五","wind":"南风3-4级","windLevel":0},{"airData":24,"airQuality":"优","city":"北京市","date":"2025-03-06","dateLong":1741190400,"date_for_voice":"昨天","humidity":"22%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-03-07 14:00:08","sunRise":"2025-03-06 06:40:00","sunSet":"2025-03-06 18:11:00","tempHigh":"13℃","tempLow":"1℃","tempRange":"1℃ ~ 13℃","weather":"晴转多云","weatherDescription":"有点冷。","weatherType":0,"week":"周四","wind":"西南风微风","windLevel":0},{"airData":120,"airQuality":"轻微污染","city":"北京市","date":"2025-03-08","dateLong":1741363200,"date_for_voice":"明天","humidity":"45%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-03-07 14:00:08","sunRise":"2025-03-08 06:37:00","sunSet":"2025-03-08 18:13:00","tempHigh":"14℃","tempLow":"2℃","tempRange":"2℃ ~ 14℃","weather":"晴","weatherDescription":"有点凉。","weatherType":0,"week":"周六","wind":"南风微风","windLevel":0},{"airData":140,"airQuality":"轻微污染","city":"北京市","date":"2025-03-09","dateLong":1741449600,"date_for_voice":"后天","humidity":"46%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-03-07 14:00:08","sunRise":"2025-03-09 06:35:00","sunSet":"2025-03-09 18:14:00","tempHigh":"15℃","tempLow":"6℃","tempRange":"6℃ ~ 15℃","weather":"晴转多云","weatherDescription":"有点凉。","weatherType":0,"week":"周日","wind":"西南风3-4级","windLevel":1},{"airData":180,"airQuality":"轻度污染","city":"北京市","date":"2025-03-10","dateLong":1741536000,"date_for_voice":"10号","humidity":"47%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-03-07 14:00:08","sunRise":"2025-03-10 06:34:00","sunSet":"2025-03-10 18:16:00","tempHigh":"16℃","tempLow":"3℃","tempRange":"3℃ ~ 16℃","weather":"多云","weatherDescription":"有点凉。","weatherType":1,"week":"下周一","wind":"南风转西南风微风","windLevel":0},{"airData":200,"airQuality":"轻度污染","city":"北京市","date":"2025-03-11","dateLong":1741622400,"date_for_voice":"11号","humidity":"49%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-03-07 14:00:08","sunRise":"2025-03-11 06:32:00","sunSet":"2025-03-11 18:17:00","tempHigh":"15℃","tempLow":"3℃","tempRange":"3℃ ~ 15℃","weather":"多云","weatherDescription":"有点凉。","weatherType":1,"week":"下周二","wind":"西南风转西北风微风","windLevel":0},{"airData":70,"airQuality":"良","city":"北京市","date":"2025-03-12","dateLong":1741708800,"date_for_voice":"12号","humidity":"18%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-03-07 14:00:08","sunRise":"2025-03-12 06:31:00","sunSet":"2025-03-12 18:18:00","tempHigh":"14℃","tempLow":"2℃","tempRange":"2℃ ~ 14℃","weather":"晴","weatherDescription":"有点凉。","weatherType":0,"week":"下周三","wind":"西北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-13","dateLong":1741795200,"date_for_voice":"13号","humidity":"19%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-03-07 14:00:08","sunRise":"2025-03-13 06:29:00","sunSet":"2025-03-13 18:19:00","tempHigh":"14℃","tempLow":"3℃","tempRange":"3℃ ~ 14℃","weather":"多云","weatherDescription":"有点凉。","weatherType":1,"week":"下周四","wind":"东南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-14","dateLong":1741881600,"date_for_voice":"14号","humidity":"28%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-03-07 14:00:08","sunRise":"2025-03-14 06:27:00","sunSet":"2025-03-14 18:20:00","tempHigh":"12℃","tempLow":"4℃","tempRange":"4℃ ~ 12℃","weather":"阴转小雨","weatherDescription":"有点冷。","weatherType":2,"week":"下周五","wind":"东南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-15","dateLong":1741968000,"date_for_voice":"15号","humidity":"36%","img":"http://cdn9002.iflyos.cn/osweathericon/07.png","lastUpdateTime":"2025-03-07 14:00:08","sunRise":"2025-03-15 06:26:00","sunSet":"2025-03-15 18:21:00","tempHigh":"8℃","tempLow":"1℃","tempRange":"1℃ ~ 8℃","weather":"小雨转晴","weatherDescription":"有点冷。","weatherType":7,"week":"下周六","wind":"西北风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-16","dateLong":1742054400,"date_for_voice":"16号","humidity":"19%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-03-07 14:00:08","sunRise":"2025-03-16 06:24:00","sunSet":"2025-03-16 18:22:00","tempHigh":"7℃","tempLow":"2℃","tempRange":"2℃ ~ 7℃","weather":"晴转阴","weatherDescription":"有点冷。","weatherType":0,"week":"下周日","wind":"西北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-17","dateLong":1742140800,"date_for_voice":"17号","humidity":"20%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-03-07 14:00:08","sunRise":"2025-03-17 06:23:00","sunSet":"2025-03-17 18:23:00","tempHigh":"14℃","tempLow":"5℃","tempRange":"5℃ ~ 14℃","weather":"晴","weatherDescription":"有点凉。","weatherType":0,"week":"下下周一","wind":"西北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-18","dateLong":1742227200,"date_for_voice":"18号","humidity":"22%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-03-07 14:00:08","sunRise":"2025-03-18 06:21:00","sunSet":"2025-03-18 18:24:00","tempHigh":"17℃","tempLow":"4℃","tempRange":"4℃ ~ 17℃","weather":"晴转阴","weatherDescription":"有点凉。","weatherType":0,"week":"下下周二","wind":"东南风转西南风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-19","dateLong":1742313600,"date_for_voice":"19号","humidity":"21%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-03-07 14:00:08","sunRise":"2025-03-19 06:19:00","sunSet":"2025-03-19 18:25:00","tempHigh":"15℃","tempLow":"6℃","tempRange":"6℃ ~ 15℃","weather":"晴转阴","weatherDescription":"有点凉。","weatherType":0,"week":"下下周三","wind":"东南风转西北风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-20","dateLong":1742400000,"date_for_voice":"20号","humidity":"19%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-03-07 14:00:08","sunRise":"2025-03-20 06:18:00","sunSet":"2025-03-20 18:26:00","tempHigh":"19℃","tempLow":"4℃","tempRange":"4℃ ~ 19℃","weather":"阴","weatherDescription":"温度适宜。","weatherType":2,"week":"下下周四","wind":"西北风转东南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-21","dateLong":1742486400,"date_for_voice":"21号","humidity":"16%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-03-07 14:00:08","sunRise":"2025-03-21 06:16:00","sunSet":"2025-03-21 18:27:00","tempHigh":"18℃","tempLow":"5℃","tempRange":"5℃ ~ 18℃","weather":"阴转晴","weatherDescription":"有点凉。","weatherType":2,"week":"下下周五","wind":"南风转西南风微风","windLevel":0}]},"cityName":"北京市"},"dm_intent":"custom","cityName":"北京市","shouldEndSession":true},"nlu":{"input":"今天太阳什么时候出来","skill":"天气","skillId":"2019042500000544","skillVersion":"56","semantics":{"request":{"intent":"QUERY","slots":[{"name":"datetime","normValue":"{\"datetime\":\"2025-03-07\",\"suggestDatetime\":\"2025-03-07\"}","value":"今天"},{"name":"location.city","normValue":"CURRENT_CITY","value":"CURRENT_CITY"},{"name":"location.poi","normValue":"CURRENT_POI","value":"CURRENT_POI"},{"name":"location.type","normValue":"LOC_POI","value":"LOC_POI"},{"name":"queryType","value":"内容"},{"name":"subfocus","value":"日出时间"}]}}},"timestamp":1741329685}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid9754094e@dx53a01b25e1153eef00"}
连接正常关闭
1741329685
param:b'{\n            "auth_id": "b3c9d78180f449b9a00922b6dd43843c",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid36a8c20f@dx09b81b25e1163eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "12f2025fe3fc44ab857232dc0d7e8c07","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3c2a@dx1956f565f2bb8aa532"}
started:
ws start
####################
测试进行: ctm00010e4a@hu17b5411338f0212902#46356772.pcm
{"recordId":"ase000d4a0a@hu1956f566f3b0427882:12f2025fe3fc44ab857232dc0d7e8c07","requestId":"ase000d4a0a@hu1956f566f3b0427882","sessionId":"cid000b3c2a@dx1956f565f2bb8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741329690}
{"recordId":"gty000b3c2b@dx1956f565fccb8aa532:12f2025fe3fc44ab857232dc0d7e8c07","requestId":"gty000b3c2b@dx1956f565fccb8aa532","sessionId":"cid000b3c2a@dx1956f565f2bb8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741329691}
{"recordId":"gty000b3c2b@dx1956f565fccb8aa532:12f2025fe3fc44ab857232dc0d7e8c07","requestId":"gty000b3c2b@dx1956f565fccb8aa532","sessionId":"cid000b3c2a@dx1956f565f2bb8aa532","eof":"1","text":"4/5÷12","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741329691}
send data finished:1741329691.8831344
{"recordId":"gty000b3c2b@dx1956f565fccb8aa532:12f2025fe3fc44ab857232dc0d7e8c07","requestId":"gty000b3c2b@dx1956f565fccb8aa532","sessionId":"cid000b3c2a@dx1956f565f2bb8aa532","topic":"dm.output","skill":"计算器","skillId":"2019031500001072","speakUrl":"","error":{},"dm":{"input":"4/5÷12","intentId":"CALC_ANSWER","intentName":"直接返回计算结果","nlg":"等于0.0667","shouldEndSession":true},"nlu":{"input":"4/5÷12","skill":"计算器","skillId":"2019031500001072","skillVersion":"11","semantics":{"request":{"entrypoint":"ent","hazard":false,"score":1,"slots":[{"begin":0,"end":6,"name":"Calculator","normValue":"0.0667","value":"4/5÷12"}],"template":"{Calculator}"}}},"timestamp":1741329691}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid9f9e2cbd@dx7cae1b25e11b3eef00"}
连接正常关闭
1741329691
param:b'{\n            "auth_id": "dc71d4646d224d0bba1a590546c0ef4f",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidcd9bc48c@dx139b1b25e11c3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "e7a0a0ea0cf146f0b77b2a9f4f830c79","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3b37@dx1956f56765eb86a532"}
started:
ws start
####################
测试进行: ctm00010e5c@hu17b54113cf90212902#46356799.pcm
{"recordId":"ase000f3731@hu1956f56860b05c2882:e7a0a0ea0cf146f0b77b2a9f4f830c79","requestId":"ase000f3731@hu1956f56860b05c2882","sessionId":"cid000b3b37@dx1956f56765eb86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741329696}
{"recordId":"gty000b3b3a@dx1956f567706b86a532:e7a0a0ea0cf146f0b77b2a9f4f830c79","requestId":"gty000b3b3a@dx1956f567706b86a532","sessionId":"cid000b3b37@dx1956f56765eb86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741329696}
{"recordId":"gty000b3b3a@dx1956f567706b86a532:e7a0a0ea0cf146f0b77b2a9f4f830c79","requestId":"gty000b3b3a@dx1956f567706b86a532","sessionId":"cid000b3b37@dx1956f56765eb86a532","eof":"1","text":"京东购物关闭","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741329697}
send data finished:1741329697.082304
{"recordId":"gty000b3b3a@dx1956f567706b86a532:e7a0a0ea0cf146f0b77b2a9f4f830c79","requestId":"gty000b3b3a@dx1956f567706b86a532","sessionId":"cid000b3b37@dx1956f56765eb86a532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"京东购物关闭","intentId":"chat","intentName":"闲聊","nlg":"虽然不想让你失望，但这个问题我真的不会。","shouldEndSession":true},"nlu":{"input":"京东购物关闭","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741329697}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid1adb275a@dxbf8c1b25e1213eef00"}
连接正常关闭
1741329697
param:b'{\n            "auth_id": "6243257853ae43708345f6b89eb2a0d6",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidb031624d@dx0e9e1b25e1213eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "c54068e82892476b88c53cc6774f7f1e","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3b47@dx1956f568b6cb86a532"}
started:
ws start
####################
测试进行: ctm00011399@hu17b59b5998e020c902#46470195.pcm
{"recordId":"ase000ddf0c@hu1956f569bcf04d3882:c54068e82892476b88c53cc6774f7f1e","requestId":"ase000ddf0c@hu1956f569bcf04d3882","sessionId":"cid000b3b47@dx1956f568b6cb86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741329701}
{"recordId":"gty000b3b48@dx1956f568c18b86a532:c54068e82892476b88c53cc6774f7f1e","requestId":"gty000b3b48@dx1956f568c18b86a532","sessionId":"cid000b3b47@dx1956f568b6cb86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741329702}
{"recordId":"gty000b3b48@dx1956f568c18b86a532:c54068e82892476b88c53cc6774f7f1e","requestId":"gty000b3b48@dx1956f568c18b86a532","sessionId":"cid000b3b47@dx1956f568b6cb86a532","eof":"1","text":"播放窗外的小豆豆","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741329702}
send data finished:1741329702.8437576
{"recordId":"gty000b3b48@dx1956f568c18b86a532:c54068e82892476b88c53cc6774f7f1e","requestId":"gty000b3b48@dx1956f568c18b86a532","sessionId":"cid000b3b47@dx1956f568b6cb86a532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"播放窗外的小豆豆","intentId":"chat","intentName":"闲聊","nlg":"虽然不想让你失望，但这个问题我真的不会。","shouldEndSession":true},"nlu":{"input":"播放窗外的小豆豆","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741329702}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid1a11cc02@dxb07d1b25e1263eef00"}
连接正常关闭
1741329703
param:b'{\n            "auth_id": "e4d71d8023d642dda483c571667be7ef",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidf08fd292@dxe9981b25e1273eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "1da60fb5c5a44e08be3b67a10c3bc23f","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3d25@dx1956f56a3497844532"}
started:
ws start
####################
测试进行: ctm000113b3@hu17b59b5a724020c902#46470220.pcm
{"recordId":"gty000b3d26@dx1956f56a3f77844532:1da60fb5c5a44e08be3b67a10c3bc23f","requestId":"gty000b3d26@dx1956f56a3f77844532","sessionId":"cid000b3d25@dx1956f56a3497844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741329707}
{"recordId":"gty000b3d26@dx1956f56a3f77844532:1da60fb5c5a44e08be3b67a10c3bc23f","requestId":"gty000b3d26@dx1956f56a3f77844532","sessionId":"cid000b3d25@dx1956f56a3497844532","eof":"1","text":"春节小品","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741329708}
send data finished:1741329708.087788
{"recordId":"gty000b3d26@dx1956f56a3f77844532:1da60fb5c5a44e08be3b67a10c3bc23f","requestId":"gty000b3d26@dx1956f56a3f77844532","sessionId":"cid000b3d25@dx1956f56a3497844532","topic":"dm.output","skill":"有声节目","skillId":"IFLYTEK.audioProgram","speakUrl":"","error":{},"dm":{"input":"春节小品","intentId":"PLAY","intentName":"播放","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"春节小品","skill":"有声节目","skillId":"IFLYTEK.audioProgram","skillVersion":"187.0","semantics":{"request":{"slots":[{"name":"subCategory","value":"春节"},{"name":"category","value":"小品"}]}}},"timestamp":1741329708}
{"recordId":"ase000ffefc@hu1956f56b3c105c4882:1da60fb5c5a44e08be3b67a10c3bc23f","requestId":"ase000ffefc@hu1956f56b3c105c4882","sessionId":"cid000b3d25@dx1956f56a3497844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741329708}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cida5f0c60b@dx5c461b25e12c3eef00"}
连接正常关闭
1741329708
param:b'{\n            "auth_id": "d464b8ba9c974de0bc229bde7efdd0e5",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidc7fc12a7@dx8a5e1b25e12c3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "4009d374e6c04a5a9404637c56d8aae0","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3d27@dx1956f56b6027844532"}
started:
ws start
####################
测试进行: ctm000113d0@hu17b59b5b6c3020c902#46470269.pcm
{"recordId":"ase000f4071@hu1956f56c41b05c2882:4009d374e6c04a5a9404637c56d8aae0","requestId":"ase000f4071@hu1956f56c41b05c2882","sessionId":"cid000b3d27@dx1956f56b6027844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741329712}
{"recordId":"gty000b3d28@dx1956f56b6a77844532:4009d374e6c04a5a9404637c56d8aae0","requestId":"gty000b3d28@dx1956f56b6a77844532","sessionId":"cid000b3d27@dx1956f56b6027844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741329712}
{"recordId":"gty000b3d28@dx1956f56b6a77844532:4009d374e6c04a5a9404637c56d8aae0","requestId":"gty000b3d28@dx1956f56b6a77844532","sessionId":"cid000b3d27@dx1956f56b6027844532","eof":"1","text":"于谦的相声","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1741329712}
send data finished:1741329712.8955028
{"recordId":"gty000b3d28@dx1956f56b6a77844532:4009d374e6c04a5a9404637c56d8aae0","requestId":"gty000b3d28@dx1956f56b6a77844532","sessionId":"cid000b3d27@dx1956f56b6027844532","topic":"dm.output","skill":"有声节目","skillId":"IFLYTEK.audioProgram","speakUrl":"","error":{},"dm":{"input":"于谦的相声","intentId":"PLAY","intentName":"播放","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"于谦的相声","skill":"有声节目","skillId":"IFLYTEK.audioProgram","skillVersion":"187.0","semantics":{"request":{"slots":[{"name":"actor","value":"于谦"},{"name":"category","value":"相声"}]}}},"timestamp":1741329712}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid95316759@dxe6a61b25e1303eef00"}
连接正常关闭
1741329712
param:b'{\n            "auth_id": "62ca93a3ac874f58a6e0fd16ad805574",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid7834d531@dx48911b25e1313eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "70061d99a553459497f5396fe4fa4657","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3d2b@dx1956f56c9887844532"}
started:
ws start
####################
测试进行: ctm0001148b@hu17b6230bc850212902#46571615.pcm
{"recordId":"ase000f35ac@hu1956f56da9b05c0882:70061d99a553459497f5396fe4fa4657","requestId":"ase000f35ac@hu1956f56da9b05c0882","sessionId":"cid000b3d2b@dx1956f56c9887844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741329718}
{"recordId":"gty000b3d2c@dx1956f56ca367844532:70061d99a553459497f5396fe4fa4657","requestId":"gty000b3d2c@dx1956f56ca367844532","sessionId":"cid000b3d2b@dx1956f56c9887844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741329719}
{"recordId":"gty000b3d2c@dx1956f56ca367844532:70061d99a553459497f5396fe4fa4657","requestId":"gty000b3d2c@dx1956f56ca367844532","sessionId":"cid000b3d2b@dx1956f56c9887844532","eof":"1","text":"我爸爸的姐姐的妈妈是谁","pinyin":"","topic":"asr.speech.result","age":"child","ageScore":"","gender":"","genderScore":"","language_class":"mandarin","timestamp":1741329719}
send data finished:1741329719.1362548
{"recordId":"gty000b3d2c@dx1956f56ca367844532:70061d99a553459497f5396fe4fa4657","requestId":"gty000b3d2c@dx1956f56ca367844532","sessionId":"cid000b3d2b@dx1956f56c9887844532","topic":"dm.output","skill":"LEIQIAO.relationShip","skillId":"LEIQIAO.relationShip","speakUrl":"","error":{},"dm":{"input":"我爸爸的姐姐的妈妈是谁","intentId":"CALL_ELSE","intentName":"查询","nlg":"爸爸的姐姐的妈妈是你的奶奶","shouldEndSession":true},"nlu":{"input":"我爸爸的姐姐的妈妈是谁","skill":"LEIQIAO.relationShip","skillId":"LEIQIAO.relationShip","skillVersion":"74.0","semantics":{"request":{"entrypoint":"ent","hazard":false,"score":1,"slots":[{"begin":1,"end":3,"name":"call","normValue":"爸爸","value":"爸爸"},{"begin":4,"end":6,"name":"call","normValue":"姐姐","value":"姐姐"},{"begin":7,"end":9,"name":"call","normValue":"妈妈","value":"妈妈"}],"template":"我{call}的{call}的{call}是谁"}}},"timestamp":1741329719}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidb4ff63ec@dx1af21b25e1373eef00"}
连接正常关闭
1741329719
param:b'{\n            "auth_id": "f001ded78e494ec0a9eadfa66c2bc4de",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidf9985527@dx3ee51b25e1373eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "04f3d27960614603a0e864d5dad60fb2","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3c49@dx1956f56e0f8b8aa532"}
started:
ws start
####################
测试进行: ctm00011492@hu17b6230c21d0212902#46571629.pcm
{"recordId":"gty000b3c4a@dx1956f56e1a0b8aa532:04f3d27960614603a0e864d5dad60fb2","requestId":"gty000b3c4a@dx1956f56e1a0b8aa532","sessionId":"cid000b3c49@dx1956f56e0f8b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741329724}
{"recordId":"gty000b3c4a@dx1956f56e1a0b8aa532:04f3d27960614603a0e864d5dad60fb2","requestId":"gty000b3c4a@dx1956f56e1a0b8aa532","sessionId":"cid000b3c49@dx1956f56e0f8b8aa532","eof":"1","text":"买部手机吧","pinyin":"","topic":"asr.speech.result","age":"child","ageScore":"","gender":"","genderScore":"","language_class":"mandarin","timestamp":1741329724}
send data finished:1741329724.6518397
{"recordId":"gty000b3c4a@dx1956f56e1a0b8aa532:04f3d27960614603a0e864d5dad60fb2","requestId":"gty000b3c4a@dx1956f56e1a0b8aa532","sessionId":"cid000b3c49@dx1956f56e0f8b8aa532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"买部手机吧","intentId":"chat","intentName":"闲聊","nlg":"这些问题，我还不知道啦","shouldEndSession":true},"nlu":{"input":"买部手机吧","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741329724}
{"recordId":"ase000ec5b7@hu1956f56f53605bf882:04f3d27960614603a0e864d5dad60fb2","requestId":"ase000ec5b7@hu1956f56f53605bf882","sessionId":"cid000b3c49@dx1956f56e0f8b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741329724}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidb6ee7e18@dxd69c1b25e13c3eef00"}
连接正常关闭
1741329724
param:b'{\n            "auth_id": "0c8033508ed547f7b587fb4993abc910",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid875f8d3c@dxc3341b25e13d3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "086f9de38b8040fda2156e6b4b25c764","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3b5c@dx1956f56f764b86a532"}
started:
ws start
####################
测试进行: ctm000114a0@hu17b6230cc5c0212902#46571646.pcm
{"recordId":"ase000ed7e7@hu1956f5708a21323882:086f9de38b8040fda2156e6b4b25c764","requestId":"ase000ed7e7@hu1956f5708a21323882","sessionId":"cid000b3b5c@dx1956f56f764b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741329729}
{"recordId":"gty000b3b5d@dx1956f56f821b86a532:086f9de38b8040fda2156e6b4b25c764","requestId":"gty000b3b5d@dx1956f56f821b86a532","sessionId":"cid000b3b5c@dx1956f56f764b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741329730}
{"recordId":"gty000b3b5d@dx1956f56f821b86a532:086f9de38b8040fda2156e6b4b25c764","requestId":"gty000b3b5d@dx1956f56f821b86a532","sessionId":"cid000b3b5c@dx1956f56f764b86a532","eof":"1","text":"一公斤大约等于多少平方米","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741329731}
send data finished:1741329731.086156
{"recordId":"gty000b3b5d@dx1956f56f821b86a532:086f9de38b8040fda2156e6b4b25c764","requestId":"gty000b3b5d@dx1956f56f821b86a532","sessionId":"cid000b3b5c@dx1956f56f764b86a532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"一公斤大约等于多少平方米","intentId":"chat","intentName":"闲聊","nlg":"这个我不太懂，我们聊聊别的吧。","shouldEndSession":true},"nlu":{"input":"一公斤大约等于多少平方米","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741329731}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cided65227a@dxcc621b25e1433eef00"}
连接正常关闭
1741329731
param:b'{\n            "auth_id": "b8556a9c2dce4e63be1b470349530545",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidc2bf809e@dxb8c51b25e1433eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "801cbe2b559f4874aaa41a5ddb50c9a0","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3d39@dx1956f5710017844532"}
started:
ws start
####################
测试进行: ctm000114af@hu17b6230d0130212902#46571653.pcm
{"recordId":"ase000ea223@hu1956f57209e05c3882:801cbe2b559f4874aaa41a5ddb50c9a0","requestId":"ase000ea223@hu1956f57209e05c3882","sessionId":"cid000b3d39@dx1956f5710017844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741329735}
{"recordId":"gty000b3d3a@dx1956f5710ba7844532:801cbe2b559f4874aaa41a5ddb50c9a0","requestId":"gty000b3d3a@dx1956f5710ba7844532","sessionId":"cid000b3d39@dx1956f5710017844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741329736}
{"recordId":"gty000b3d3a@dx1956f5710ba7844532:801cbe2b559f4874aaa41a5ddb50c9a0","requestId":"gty000b3d3a@dx1956f5710ba7844532","sessionId":"cid000b3d39@dx1956f5710017844532","eof":"1","text":"我要你取消闹钟","pinyin":"","topic":"asr.speech.result","age":"child","ageScore":"","gender":"","genderScore":"","language_class":"mandarin","timestamp":1741329736}
send data finished:1741329736.745399
{"recordId":"gty000b3d3a@dx1956f5710ba7844532:801cbe2b559f4874aaa41a5ddb50c9a0","requestId":"gty000b3d3a@dx1956f5710ba7844532","sessionId":"cid000b3d39@dx1956f5710017844532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"我要你取消闹钟","intentId":"chat","intentName":"闲聊","nlg":"等我学习一下，再来回答吧！现在不如问点其他的吧。","shouldEndSession":true},"nlu":{"input":"我要你取消闹钟","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741329736}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid9850a985@dx94ab1b25e1483eef00"}
连接正常关闭
1741329736
param:b'{\n            "auth_id": "a5fd3ce283a94526aa46d4e5849e2f45",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid98ff15a9@dxf7211b25e1493eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "dbafc6a7bc174657b8d4cf332db06425","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3c55@dx1956f572639b8aa532"}
started:
ws start
####################
测试进行: ctm00011891@hu17b59bae0c6020c902#46471001.pcm
{"recordId":"ase000ea559@hu1956f57359205c3882:dbafc6a7bc174657b8d4cf332db06425","requestId":"ase000ea559@hu1956f57359205c3882","sessionId":"cid000b3c55@dx1956f572639b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741329741}
{"recordId":"gty000b3c56@dx1956f5726f4b8aa532:dbafc6a7bc174657b8d4cf332db06425","requestId":"gty000b3c56@dx1956f5726f4b8aa532","sessionId":"cid000b3c55@dx1956f572639b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741329742}
{"recordId":"gty000b3c56@dx1956f5726f4b8aa532:dbafc6a7bc174657b8d4cf332db06425","requestId":"gty000b3c56@dx1956f5726f4b8aa532","sessionId":"cid000b3c55@dx1956f572639b8aa532","eof":"1","text":"马上给我播放儿歌小牧童","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741329742}
send data finished:1741329743.0258245
{"recordId":"gty000b3c56@dx1956f5726f4b8aa532:dbafc6a7bc174657b8d4cf332db06425","requestId":"gty000b3c56@dx1956f5726f4b8aa532","sessionId":"cid000b3c55@dx1956f572639b8aa532","topic":"dm.output","skill":"国学","skillId":"AIUI.chLiterature","speakUrl":"","error":{},"dm":{"input":"马上给我播放儿歌小牧童","intentId":"QUERY_GROUP","intentName":"综合查询","nlg":"即将为你播放《牧童》－吕洞宾和小牧童","widget":{"content":[{"album":"五年级必背古诗文－国学小课堂","language":"CN","tags":"内容分类 亲子 国学启蒙 国学小课堂","title":"《牧童》－吕洞宾和小牧童","subTitle":"","imageUrl":"http://p0.ifengimg.com/a/2019_02/b87fafbcb7a5666_size246_w640_h640.jpg","linkUrl":"https://p6.renbenzhihui.com/video19.ifeng.com/video09/2019/01/09/4966760-543-066-1422.mp3?pf=OH9GI&vid=4502820&tm=1741329743250&pid=212031&t=1741329743&k=6D869B1FF30BEDF43051AA5373EA68EF","id":"4502820","extra":{"source":"ifeng"}},{"album":"一年级必背古诗文－国学小课堂","language":"CN","tags":"内容分类 亲子 国学启蒙 国学小课堂","title":"《所见》－牛背上的小牧童","subTitle":"","imageUrl":"http://p0.ifengimg.com/a/2019_02/b091359975ea57c_size188_w640_h640.jpg","linkUrl":"https://p6.renbenzhihui.com/video19.ifeng.com/video09/2019/01/09/4966700-543-066-1343.mp3?pf=OH9GI&vid=4502784&tm=1741329743252&pid=212029&t=1741329743&k=C1401F412672135F2010CEECD137CE20","id":"4502784","extra":{"source":"ifeng"}}]},"shouldEndSession":true},"nlu":{"input":"马上给我播放儿歌小牧童","skill":"国学","skillId":"AIUI.chLiterature","skillVersion":"2.0","semantics":{"request":{"entrypoint":"ent","score":0.8378620147705078,"slots":[{"begin":9,"end":11,"name":"name","normValue":"牧童","value":"牧童"}],"template":"{play}儿童国学{name}"}}},"timestamp":1741329743}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidadd97058@dx79dd1b25e14f3eef00"}
连接正常关闭
1741329743
param:b'{\n            "auth_id": "470983a7a6ff48f3937dc07ade1c407b",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid308bebfd@dx52251b25e14f3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "54ffe7be0b1044a395d4da30b2deed8a","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3d41@dx1956f573f2f7844532"}
started:
ws start
####################
测试进行: ctm0001189a@hu17b59bae594020c902#46471011.pcm
{"recordId":"ase000ee28c@hu1956f574e881323882:54ffe7be0b1044a395d4da30b2deed8a","requestId":"ase000ee28c@hu1956f574e881323882","sessionId":"cid000b3d41@dx1956f573f2f7844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741329747}
{"recordId":"gty000b3d42@dx1956f573fe97844532:54ffe7be0b1044a395d4da30b2deed8a","requestId":"gty000b3d42@dx1956f573fe97844532","sessionId":"cid000b3d41@dx1956f573f2f7844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741329749}
{"recordId":"gty000b3d42@dx1956f573fe97844532:54ffe7be0b1044a395d4da30b2deed8a","requestId":"gty000b3d42@dx1956f573fe97844532","sessionId":"cid000b3d41@dx1956f573f2f7844532","eof":"1","text":"有没有小蓓蕾组合唱的儿歌","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741329749}
send data finished:1741329749.452603
{"recordId":"gty000b3d42@dx1956f573fe97844532:54ffe7be0b1044a395d4da30b2deed8a","requestId":"gty000b3d42@dx1956f573fe97844532","sessionId":"cid000b3d41@dx1956f573f2f7844532","topic":"dm.output","skill":"儿歌","skillId":"2019031500001056","speakUrl":"","error":{},"dm":{"input":"有没有小蓓蕾组合唱的儿歌","intentId":"QUERY_BY_ARTIST","intentName":"根据歌手名点播儿歌","nlg":"没有找到合适内容","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"有没有小蓓蕾组合唱的儿歌","skill":"儿歌","skillId":"2019031500001056","skillVersion":"8","semantics":{"request":{"entrypoint":"ent","hazard":false,"score":1,"slots":[{"name":"歌手名","value":"小蓓蕾组合"}],"template":"有没有{artist}唱的儿歌"}}},"timestamp":1741329749}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid628ad652@dxb0631b25e1553eef00"}
连接正常关闭
1741329749
param:b'{\n            "auth_id": "f3233c22fdbc40f58e7469fa60757853",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidcec10434@dxec621b25e1553eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "b35770329ff34a60876e4fd5366e3c8c","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3d4a@dx1956f575b557844532"}
started:
ws start
####################
测试进行: ctm000118a9@hu17b59baf074020c902#46471029.pcm
{"recordId":"ase000ee70c@hu1956f576be11323882:b35770329ff34a60876e4fd5366e3c8c","requestId":"ase000ee70c@hu1956f576be11323882","sessionId":"cid000b3d4a@dx1956f575b557844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741329755}
{"recordId":"gty000b3d4b@dx1956f575da97844532:b35770329ff34a60876e4fd5366e3c8c","requestId":"gty000b3d4b@dx1956f575da97844532","sessionId":"cid000b3d4a@dx1956f575b557844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741329756}
{"recordId":"gty000b3d4b@dx1956f575da97844532:b35770329ff34a60876e4fd5366e3c8c","requestId":"gty000b3d4b@dx1956f575da97844532","sessionId":"cid000b3d4a@dx1956f575b557844532","eof":"1","text":"可以帮我推荐个股票吗","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741329756}
send data finished:1741329756.472998
{"recordId":"gty000b3d4b@dx1956f575da97844532:b35770329ff34a60876e4fd5366e3c8c","requestId":"gty000b3d4b@dx1956f575da97844532","sessionId":"cid000b3d4a@dx1956f575b557844532","topic":"dm.output","skill":"股票","skillId":"IFLYTEK.stock","speakUrl":"","error":{},"dm":{"input":"可以帮我推荐个股票吗","intentId":"STOCK_RECOMMEND","intentName":"推荐股票","nlg":"我还不能预知未来股市，无法为您推荐股票。","shouldEndSession":true},"nlu":{"input":"可以帮我推荐个股票吗","skill":"股票","skillId":"IFLYTEK.stock","skillVersion":"375.0","semantics":{"request":{"slots":[]}}},"timestamp":1741329756}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid37a387e5@dx9d4b1b25e15c3eef00"}
连接正常关闭
1741329756
param:b'{\n            "auth_id": "c4fc455691184a628dd19d59fe6dcab7",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid7bb669c1@dx8da51b25e15c3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "4fe45d09b54842b38a3346c277a9d496","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b396a@dx1956f5772c1b8a9532"}
started:
ws start
####################
测试进行: ctm000118b6@hu17b4fbfceea020c902#46308921.pcm
{"recordId":"ase000d0320@hu1956f5788ad04d3882:4fe45d09b54842b38a3346c277a9d496","requestId":"ase000d0320@hu1956f5788ad04d3882","sessionId":"cid000b396a@dx1956f5772c1b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741329762}
{"recordId":"gty000b396b@dx1956f57737ab8a9532:4fe45d09b54842b38a3346c277a9d496","requestId":"gty000b396b@dx1956f57737ab8a9532","sessionId":"cid000b396a@dx1956f5772c1b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741329763}
{"recordId":"gty000b396b@dx1956f57737ab8a9532:4fe45d09b54842b38a3346c277a9d496","requestId":"gty000b396b@dx1956f57737ab8a9532","sessionId":"cid000b396a@dx1956f5772c1b8a9532","eof":"1","text":"我想听长发公主的故事","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741329763}
send data finished:1741329763.8102818
{"recordId":"gty000b396b@dx1956f57737ab8a9532:4fe45d09b54842b38a3346c277a9d496","requestId":"gty000b396b@dx1956f57737ab8a9532","sessionId":"cid000b396a@dx1956f5772c1b8a9532","topic":"dm.output","skill":"有声节目","skillId":"IFLYTEK.audioProgram","speakUrl":"","error":{},"dm":{"input":"我想听长发公主的故事","intentId":"PLAY","intentName":"播放","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"我想听长发公主的故事","skill":"有声节目","skillId":"IFLYTEK.audioProgram","skillVersion":"187.0","semantics":{"request":{"slots":[{"name":"category","value":"故事"},{"name":"name","value":"长发公主"}]}}},"timestamp":1741329763}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid39341d46@dx2c7b1b25e1633eef00"}
连接正常关闭
1741329763
param:b'{\n            "auth_id": "0e163f3035f04cf9a7dc8e1b7778ef01",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidf46d39cd@dxb4f91b25e1643eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "0d52ed6937ce4ca4ad86f2b24358f93a","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3d55@dx1956f57921b7844532"}
started:
ws start
####################
测试进行: ctm000118c4@hu17b4fbfd54b020c902#46308945.pcm
{"recordId":"ase000ef064@hu1956f57a93d1323882:0d52ed6937ce4ca4ad86f2b24358f93a","requestId":"ase000ef064@hu1956f57a93d1323882","sessionId":"cid000b3d55@dx1956f57921b7844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741329770}
{"recordId":"gty000b3d56@dx1956f5792cc7844532:0d52ed6937ce4ca4ad86f2b24358f93a","requestId":"gty000b3d56@dx1956f5792cc7844532","sessionId":"cid000b3d55@dx1956f57921b7844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741329773}
{"recordId":"gty000b3d56@dx1956f5792cc7844532:0d52ed6937ce4ca4ad86f2b24358f93a","requestId":"gty000b3d56@dx1956f5792cc7844532","sessionId":"cid000b3d55@dx1956f57921b7844532","eof":"1","text":"有没有调成九的影音生活广播","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741329773}
send data finished:1741329773.1790988
{"recordId":"gty000b3d56@dx1956f5792cc7844532:0d52ed6937ce4ca4ad86f2b24358f93a","requestId":"gty000b3d56@dx1956f5792cc7844532","sessionId":"cid000b3d55@dx1956f57921b7844532","topic":"dm.output","skill":"网络电台","skillId":"2019031500001032","speakUrl":"","error":{},"dm":{"input":"有没有调成九的影音生活广播","intentId":"LAUNCH","intentName":"打开","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"有没有调成九的影音生活广播","skill":"网络电台","skillId":"2019031500001032","skillVersion":"44","semantics":{"request":{"slots":[{"name":"category","value":"生活"},{"name":"code","value":"9"},{"name":"nameOrig","value":"生活电台"}]}}},"timestamp":1741329773}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidb651fac8@dxc3921b25e16d3eef00"}
连接正常关闭
1741329773
param:b'{\n            "auth_id": "43552c407e0643f5959665aa63d40651",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidac0be3aa@dx487c1b25e16d3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "8e32c6216259415eab6609f8bfa047ef","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3b83@dx1956f57b425b86a532"}
started:
ws start
####################
测试进行: ctm000118cf@hu17b4fbfd9e3020c902#46308961.pcm
{"recordId":"ase000d7e9f@hu1956f57ca240427882:8e32c6216259415eab6609f8bfa047ef","requestId":"ase000d7e9f@hu1956f57ca240427882","sessionId":"cid000b3b83@dx1956f57b425b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741329779}
{"recordId":"gty000b3b84@dx1956f57b4c0b86a532:8e32c6216259415eab6609f8bfa047ef","requestId":"gty000b3b84@dx1956f57b4c0b86a532","sessionId":"cid000b3b83@dx1956f57b425b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741329779}
{"recordId":"gty000b3b84@dx1956f57b4c0b86a532:8e32c6216259415eab6609f8bfa047ef","requestId":"gty000b3b84@dx1956f57b4c0b86a532","sessionId":"cid000b3b83@dx1956f57b425b86a532","eof":"1","text":"找一下三字经","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741329780}
send data finished:1741329780.0798066
{"recordId":"gty000b3b84@dx1956f57b4c0b86a532:8e32c6216259415eab6609f8bfa047ef","requestId":"gty000b3b84@dx1956f57b4c0b86a532","sessionId":"cid000b3b83@dx1956f57b425b86a532","topic":"dm.output","skill":"国学","skillId":"AIUI.chLiterature","speakUrl":"","error":{},"dm":{"input":"找一下三字经","intentId":"QUERY_GROUP","intentName":"综合查询","nlg":"即将为你播放三字经 披蒲编","widget":{"content":[{"album":"嘟拉新三字经","language":"CN","tags":"嘟拉学堂,嘟拉新三字经","title":"三字经 披蒲编","subTitle":"","imageUrl":"","linkUrl":"https://cdn9003.iflyos.cn/audio/national/newsanzijing/0e74254cb118f54d0259f2b9be62adef.mp3","id":"0e74254cb118f54d0259f2b9be62adef","extra":{"source":"61ertong"}},{"album":"嘟拉三字经","language":"CN","tags":"嘟拉学堂,嘟拉三字经","title":"三字经 道咸间","subTitle":"","imageUrl":"","linkUrl":"https://cdn9003.iflyos.cn/audio/national/sanzijing/38ba82ddd39ea2fcc5d06878ebcd9e01.mp3","id":"38ba82ddd39ea2fcc5d06878ebcd9e01","extra":{"source":"61ertong"}},{"album":"嘟拉新三字经","language":"CN","tags":"嘟拉学堂,嘟拉新三字经","title":"三字经 二十传","subTitle":"","imageUrl":"","linkUrl":"https://cdn9003.iflyos.cn/audio/national/newsanzijing/90b5247cef5b6f025f84e51d5cb524a3.mp3","id":"90b5247cef5b6f025f84e51d5cb524a3","extra":{"source":"61ertong"}},{"album":"嘟拉新三字经","language":"CN","tags":"嘟拉学堂,嘟拉新三字经","title":"三字经 宋齐继","subTitle":"","imageUrl":"","linkUrl":"https://cdn9003.iflyos.cn/audio/national/newsanzijing/dbe24e59d61e7b8262a02072844a3e85.mp3","id":"dbe24e59d61e7b8262a02072844a3e85","extra":{"source":"61ertong"}},{"album":"嘟拉三字经","language":"CN","tags":"嘟拉学堂,嘟拉三字经","title":"三字经 寒燠均","subTitle":"","imageUrl":"","linkUrl":"https://cdn9003.iflyos.cn/audio/national/sanzijing/9ea935750ffa477e88284a1b56f0ba2a.mp3","id":"9ea935750ffa477e88284a1b56f0ba2a","extra":{"source":"61ertong"}},{"album":"嘟拉三字经","language":"CN","tags":"嘟拉学堂,嘟拉三字经","title":"三字经 炎宋兴","subTitle":"","imageUrl":"","linkUrl":"https://cdn9003.iflyos.cn/audio/national/sanzijing/35a9930d2aadb2fbb577f4eb498cd16a.mp3","id":"35a9930d2aadb2fbb577f4eb498cd16a","extra":{"source":"61ertong"}},{"album":"嘟拉三字经","language":"CN","tags":"嘟拉学堂,嘟拉三字经","title":"三字经 莹八岁","subTitle":"","imageUrl":"","linkUrl":"https://cdn9003.iflyos.cn/audio/national/sanzijing/32059737fe686efe698566a3eef88a4a.mp3","id":"12b679e7133ef61ea43dffeefbf7027f","extra":{"source":"61ertong"}},{"album":"嘟拉三字经","language":"CN","tags":"嘟拉学堂,嘟拉三字经","title":"三字经 披蒲编","subTitle":"","imageUrl":"","linkUrl":"https://cdn9003.iflyos.cn/audio/national/sanzijing/8f8be04fa27149469b3e6a5ed7526560.mp3","id":"0e74254cb118f54d0259f2b9be62adef","extra":{"source":"61ertong"}},{"album":"嘟拉三字经","language":"CN","tags":"嘟拉学堂,嘟拉三字经","title":"三字经 曰黄道","subTitle":"","imageUrl":"","linkUrl":"https://cdn9003.iflyos.cn/audio/national/sanzijing/e1bc3a3e8fdaf68d5a6398209fca1562.mp3","id":"e1bc3a3e8fdaf68d5a6398209fca1562","extra":{"source":"61ertong"}},{"album":"嘟拉三字经","language":"CN","tags":"嘟拉学堂,嘟拉三字经","title":"三字经 读史者","subTitle":"","imageUrl":"","linkUrl":"https://cdn9003.iflyos.cn/audio/national/sanzijing/854a54e025a6be728d9026ce60183d4e.mp3","id":"854a54e025a6be728d9026ce60183d4e","extra":{"source":"61ertong"}}]},"shouldEndSession":true},"nlu":{"input":"找一下三字经","skill":"国学","skillId":"AIUI.chLiterature","skillVersion":"2.0","semantics":{"request":{"entrypoint":"ent","hazard":false,"score":1,"slots":[{"begin":3,"end":6,"name":"name","normValue":"三字经","value":"三字经"}],"template":"{search}{name}"}}},"timestamp":1741329780}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid920f38c7@dxae531b25e1743eef00"}
连接正常关闭
1741329780
param:b'{\n            "auth_id": "9349d3b0f2e448c3aa53e2ab08d4ed3c",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid7f83fbd5@dxa7211b25e1743eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "a99c6ef772c042088715943bb707787c","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3b8f@dx1956f57cf62b86a532"}
started:
ws start
####################
测试进行: ctm000118de@hu17b4fbfdea9020c902#46308980.pcm
{"recordId":"ase000f2d3c@hu1956f57e4b705c4882:a99c6ef772c042088715943bb707787c","requestId":"ase000f2d3c@hu1956f57e4b705c4882","sessionId":"cid000b3b8f@dx1956f57cf62b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741329786}
{"recordId":"gty000b3b90@dx1956f57cfedb86a532:a99c6ef772c042088715943bb707787c","requestId":"gty000b3b90@dx1956f57cfedb86a532","sessionId":"cid000b3b8f@dx1956f57cf62b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741329791}
{"recordId":"gty000b3b90@dx1956f57cfedb86a532:a99c6ef772c042088715943bb707787c","requestId":"gty000b3b90@dx1956f57cfedb86a532","sessionId":"cid000b3b8f@dx1956f57cf62b86a532","eof":"1","text":"我要把人民币兑换成美元","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741329791}
send data finished:1741329791.1543221
{"recordId":"gty000b3b90@dx1956f57cfedb86a532:a99c6ef772c042088715943bb707787c","requestId":"gty000b3b90@dx1956f57cfedb86a532","sessionId":"cid000b3b8f@dx1956f57cf62b86a532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"我要把人民币兑换成美元","intentId":"chat","intentName":"闲聊","nlg":"看自己需求。","shouldEndSession":true},"nlu":{"input":"我要把人民币兑换成美元","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741329791}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cideb002a22@dxde121b25e17f3eef00"}
连接正常关闭
1741329791
param:b'{\n            "auth_id": "63c6712570014d4a91cb4ed804229f87",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidd6586a4d@dx08ab1b25e17f3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "6370c7e776fd48e19c3307a42348eaa0","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3b96@dx1956f57faf5b86a532"}
started:
ws start
####################
测试进行: ctm0001190a@hu17ba70b828b0212902#47592803.pcm
{"recordId":"gty000b3b97@dx1956f57fba4b86a532:6370c7e776fd48e19c3307a42348eaa0","requestId":"gty000b3b97@dx1956f57fba4b86a532","sessionId":"cid000b3b96@dx1956f57faf5b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741329795}
{"recordId":"gty000b3b97@dx1956f57fba4b86a532:6370c7e776fd48e19c3307a42348eaa0","requestId":"gty000b3b97@dx1956f57fba4b86a532","sessionId":"cid000b3b96@dx1956f57faf5b86a532","eof":"1","text":"音乐关了","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741329795}
send data finished:1741329795.969774
{"recordId":"gty000b3b97@dx1956f57fba4b86a532:6370c7e776fd48e19c3307a42348eaa0","requestId":"gty000b3b97@dx1956f57fba4b86a532","sessionId":"cid000b3b96@dx1956f57faf5b86a532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"音乐关了","intentId":"INSTRUCTION","intentName":"音乐控制","nlg":"","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"音乐关了","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"操作","value":"close"}]}}},"timestamp":1741329795}
{"recordId":"ase000f72aa@hu1956f580b4905c2882:6370c7e776fd48e19c3307a42348eaa0","requestId":"ase000f72aa@hu1956f580b4905c2882","sessionId":"cid000b3b96@dx1956f57faf5b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741329795}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid05559d77@dxcec21b25e1843eef00"}
连接正常关闭
1741329796
param:b'{\n            "auth_id": "08319d2dfe33421ebd8b89b7ae779f3a",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid903790b7@dxac711b25e1843eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "b7be2162ac614c0a951d989a7c6d80ba","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3b9a@dx1956f580d2bb86a532"}
started:
ws start
####################
测试进行: ctm0001190e@hu17ba70b83df0212902#47592809.pcm
{"recordId":"ase000f7663@hu1956f5820f005c2882:b7be2162ac614c0a951d989a7c6d80ba","requestId":"ase000f7663@hu1956f5820f005c2882","sessionId":"cid000b3b9a@dx1956f580d2bb86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741329801}
{"recordId":"gty000b3b9b@dx1956f580dccb86a532:b7be2162ac614c0a951d989a7c6d80ba","requestId":"gty000b3b9b@dx1956f580dccb86a532","sessionId":"cid000b3b9a@dx1956f580d2bb86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741329802}
{"recordId":"gty000b3b9b@dx1956f580dccb86a532:b7be2162ac614c0a951d989a7c6d80ba","requestId":"gty000b3b9b@dx1956f580dccb86a532","sessionId":"cid000b3b9a@dx1956f580d2bb86a532","eof":"1","text":"收藏现在放的这首歌","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741329802}
send data finished:1741329803.0407188
{"recordId":"gty000b3b9b@dx1956f580dccb86a532:b7be2162ac614c0a951d989a7c6d80ba","requestId":"gty000b3b9b@dx1956f580dccb86a532","sessionId":"cid000b3b9a@dx1956f580d2bb86a532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"收藏现在放的这首歌","intentId":"chat","intentName":"闲聊","nlg":"这个问题有点难，你还是换一个吧。","shouldEndSession":true},"nlu":{"input":"收藏现在放的这首歌","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741329803}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidb1891eee@dx46251b25e18b3eef00"}
连接正常关闭
1741329803
param:b'{\n            "auth_id": "4c3eac5c161d48d79a28a2eb5fb17b36",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid5c7bb146@dx8a161b25e18b3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "657b183a32b64cf59e79ad91df95e4f1","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3ca4@dx1956f58292ab8aa532"}
started:
ws start
####################
测试进行: ctm00011911@hu17ba70b88b50212902#47592812.pcm
{"recordId":"ase000ece5e@hu1956f583c5405c3882:657b183a32b64cf59e79ad91df95e4f1","requestId":"ase000ece5e@hu1956f583c5405c3882","sessionId":"cid000b3ca4@dx1956f58292ab8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741329808}
{"recordId":"gty000b3ca5@dx1956f5829ccb8aa532:657b183a32b64cf59e79ad91df95e4f1","requestId":"gty000b3ca5@dx1956f5829ccb8aa532","sessionId":"cid000b3ca4@dx1956f58292ab8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741329809}
{"recordId":"gty000b3ca5@dx1956f5829ccb8aa532:657b183a32b64cf59e79ad91df95e4f1","requestId":"gty000b3ca5@dx1956f5829ccb8aa532","sessionId":"cid000b3ca4@dx1956f58292ab8aa532","eof":"1","text":"播放冯巩的相声","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741329809}
send data finished:1741329809.1672673
{"recordId":"gty000b3ca5@dx1956f5829ccb8aa532:657b183a32b64cf59e79ad91df95e4f1","requestId":"gty000b3ca5@dx1956f5829ccb8aa532","sessionId":"cid000b3ca4@dx1956f58292ab8aa532","topic":"dm.output","skill":"有声节目","skillId":"IFLYTEK.audioProgram","speakUrl":"","error":{},"dm":{"input":"播放冯巩的相声","intentId":"PLAY","intentName":"播放","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"播放冯巩的相声","skill":"有声节目","skillId":"IFLYTEK.audioProgram","skillVersion":"187.0","semantics":{"request":{"slots":[{"name":"actor","value":"冯巩"},{"name":"category","value":"相声"}]}}},"timestamp":1741329809}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidc7073696@dx96361b25e1913eef00"}
连接正常关闭
1741329809
param:b'{\n            "auth_id": "c4d1473d34c94583bf6efb9f4792a806",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid4c147e68@dx6aaf1b25e1913eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "e49dd3afdf544fd69a02a79ac06e1f05","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3d71@dx1956f5840807844532"}
started:
ws start
####################
测试进行: ctm00011914@hu17ba70b8e090212902#47592826.pcm
{"recordId":"ase000d22c4@hu1956f58523004d3882:e49dd3afdf544fd69a02a79ac06e1f05","requestId":"ase000d22c4@hu1956f58523004d3882","sessionId":"cid000b3d71@dx1956f5840807844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741329814}
{"recordId":"gty000b3d72@dx1956f5841237844532:e49dd3afdf544fd69a02a79ac06e1f05","requestId":"gty000b3d72@dx1956f5841237844532","sessionId":"cid000b3d71@dx1956f5840807844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741329814}
{"recordId":"gty000b3d72@dx1956f5841237844532:e49dd3afdf544fd69a02a79ac06e1f05","requestId":"gty000b3d72@dx1956f5841237844532","sessionId":"cid000b3d71@dx1956f5840807844532","eof":"1","text":"故事讲个故事吧","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741329814}
send data finished:1741329814.7941265
{"recordId":"gty000b3d72@dx1956f5841237844532:e49dd3afdf544fd69a02a79ac06e1f05","requestId":"gty000b3d72@dx1956f5841237844532","sessionId":"cid000b3d71@dx1956f5840807844532","topic":"dm.output","skill":"有声节目","skillId":"IFLYTEK.audioProgram","speakUrl":"","error":{},"dm":{"input":"故事讲个故事吧","intentId":"PLAY","intentName":"播放","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"故事讲个故事吧","skill":"有声节目","skillId":"IFLYTEK.audioProgram","skillVersion":"187.0","semantics":{"request":{"slots":[{"name":"category","value":"故事"}]}}},"timestamp":1741329814}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid3482a196@dx368c1b25e1963eef00"}
连接正常关闭
1741329814
param:b'{\n            "auth_id": "20629722ed2648b7bdb89a1ed0945c95",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidc7535a57@dx73eb1b25e1973eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "aa34815cf40247288f01704bb7b9a30d","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3996@dx1956f58568eb8a9532"}
started:
ws start
####################
测试进行: ctm00011919@hu17ba70b8fa30212902#47592831.pcm
{"recordId":"ase000d975f@hu1956f5866940427882:aa34815cf40247288f01704bb7b9a30d","requestId":"ase000d975f@hu1956f5866940427882","sessionId":"cid000b3996@dx1956f58568eb8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741329819}
{"recordId":"gty000b3997@dx1956f585754b8a9532:aa34815cf40247288f01704bb7b9a30d","requestId":"gty000b3997@dx1956f585754b8a9532","sessionId":"cid000b3996@dx1956f58568eb8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741329820}
{"recordId":"gty000b3997@dx1956f585754b8a9532:aa34815cf40247288f01704bb7b9a30d","requestId":"gty000b3997@dx1956f585754b8a9532","sessionId":"cid000b3996@dx1956f58568eb8a9532","eof":"1","text":"请你给我讲个故事好吗","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741329820}
send data finished:1741329820.8736541
{"recordId":"gty000b3997@dx1956f585754b8a9532:aa34815cf40247288f01704bb7b9a30d","requestId":"gty000b3997@dx1956f585754b8a9532","sessionId":"cid000b3996@dx1956f58568eb8a9532","topic":"dm.output","skill":"有声节目","skillId":"IFLYTEK.audioProgram","speakUrl":"","error":{},"dm":{"input":"请你给我讲个故事好吗","intentId":"PLAY","intentName":"播放","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"请你给我讲个故事好吗","skill":"有声节目","skillId":"IFLYTEK.audioProgram","skillVersion":"187.0","semantics":{"request":{"slots":[{"name":"category","value":"故事"}]}}},"timestamp":1741329820}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid89b0fec7@dxc3fe1b25e19c3eef00"}
连接正常关闭
1741329820
param:b'{\n            "auth_id": "1844b16192e942799d4886e1d8e82cc3",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid2a9c4483@dx21641b25e19d3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "955001a34fde47cd95da79071b9cc8ae","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3cb7@dx1956f586e39b8aa532"}
started:
ws start
####################
测试进行: ctm0001191d@hu17ba70b91a70212902#47592834.pcm
{"recordId":"ase000f853a@hu1956f587f0405c2882:955001a34fde47cd95da79071b9cc8ae","requestId":"ase000f853a@hu1956f587f0405c2882","sessionId":"cid000b3cb7@dx1956f586e39b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741329825}
{"recordId":"gty000b3cb8@dx1956f586ed4b8aa532:955001a34fde47cd95da79071b9cc8ae","requestId":"gty000b3cb8@dx1956f586ed4b8aa532","sessionId":"cid000b3cb7@dx1956f586e39b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741329825}
{"recordId":"gty000b3cb8@dx1956f586ed4b8aa532:955001a34fde47cd95da79071b9cc8ae","requestId":"gty000b3cb8@dx1956f586ed4b8aa532","sessionId":"cid000b3cb7@dx1956f586e39b8aa532","eof":"1","text":"我想听相声","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741329825}
send data finished:1741329825.8971188
{"recordId":"gty000b3cb8@dx1956f586ed4b8aa532:955001a34fde47cd95da79071b9cc8ae","requestId":"gty000b3cb8@dx1956f586ed4b8aa532","sessionId":"cid000b3cb7@dx1956f586e39b8aa532","topic":"dm.output","skill":"有声节目","skillId":"IFLYTEK.audioProgram","speakUrl":"","error":{},"dm":{"input":"我想听相声","intentId":"PLAY","intentName":"播放","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"我想听相声","skill":"有声节目","skillId":"IFLYTEK.audioProgram","skillVersion":"187.0","semantics":{"request":{"slots":[{"name":"category","value":"相声"}]}}},"timestamp":1741329825}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid2f83b9b9@dx563e1b25e1a13eef00"}
连接正常关闭
1741329825
param:b'{\n            "auth_id": "a75486519a53496d92ab9319b6a0e888",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid5d13112d@dx35ae1b25e1a23eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "2021e290529d4830ad7416a3dbf218e2","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b399f@dx1956f5881d5b8a9532"}
started:
ws start
####################
测试进行: ctm0001191f@hu17ba70b93b50212902#47592838.pcm
{"recordId":"ase000d9dee@hu1956f5892370427882:2021e290529d4830ad7416a3dbf218e2","requestId":"ase000d9dee@hu1956f5892370427882","sessionId":"cid000b399f@dx1956f5881d5b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741329830}
{"recordId":"gty000b39a0@dx1956f588272b8a9532:2021e290529d4830ad7416a3dbf218e2","requestId":"gty000b39a0@dx1956f588272b8a9532","sessionId":"cid000b399f@dx1956f5881d5b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741329830}
{"recordId":"gty000b39a0@dx1956f588272b8a9532:2021e290529d4830ad7416a3dbf218e2","requestId":"gty000b39a0@dx1956f588272b8a9532","sessionId":"cid000b399f@dx1956f5881d5b8a9532","eof":"1","text":"我要听相声","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741329830}
send data finished:1741329830.9216943
{"recordId":"gty000b39a0@dx1956f588272b8a9532:2021e290529d4830ad7416a3dbf218e2","requestId":"gty000b39a0@dx1956f588272b8a9532","sessionId":"cid000b399f@dx1956f5881d5b8a9532","topic":"dm.output","skill":"有声节目","skillId":"IFLYTEK.audioProgram","speakUrl":"","error":{},"dm":{"input":"我要听相声","intentId":"PLAY","intentName":"播放","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"我要听相声","skill":"有声节目","skillId":"IFLYTEK.audioProgram","skillVersion":"187.0","semantics":{"request":{"slots":[{"name":"category","value":"相声"}]}}},"timestamp":1741329830}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cide5972231@dxcfc01b25e1a63eef00"}
连接正常关闭
1741329831
param:b'{\n            "auth_id": "905b6a57483b424dbb27f324e39a9381",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid073e62d6@dx95c91b25e1a73eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "2d573241f5654132a41ee637c950d243","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3baf@dx1956f589595b86a532"}
started:
ws start
####################
测试进行: ctm00011923@hu17ba70b96310212902#47592841.pcm
{"recordId":"ase000da0e4@hu1956f58a7170427882:2d573241f5654132a41ee637c950d243","requestId":"ase000da0e4@hu1956f58a7170427882","sessionId":"cid000b3baf@dx1956f589595b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1741329835}
{"recordId":"gty000b3bb0@dx1956f589646b86a532:2d573241f5654132a41ee637c950d243","requestId":"gty000b3bb0@dx1956f589646b86a532","sessionId":"cid000b3baf@dx1956f589595b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1741329836}
{"recordId":"gty000b3bb0@dx1956f589646b86a532:2d573241f5654132a41ee637c950d243","requestId":"gty000b3bb0@dx1956f589646b86a532","sessionId":"cid000b3baf@dx1956f589595b86a532","eof":"1","text":"看小说共进午餐","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1741329836}
send data finished:1741329836.8243096
{"recordId":"gty000b3bb0@dx1956f589646b86a532:2d573241f5654132a41ee637c950d243","requestId":"gty000b3bb0@dx1956f589646b86a532","sessionId":"cid000b3baf@dx1956f589595b86a532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"看小说共进午餐","intentId":"chat","intentName":"闲聊","nlg":"虽然不想让你失望，但这个问题我真的不会。","shouldEndSession":true},"nlu":{"input":"看小说共进午餐","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1741329836}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid1670c4b7@dx499f1b25e1ac3eef00"}
连接正常关闭
1741329837
param:b'{\n            "auth_id": "8cd1a537574c447c8158b50305f647db",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"ciddac79ac9@dxef871b25e1ad3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "6c0624685b7e43768a4cd6cb4b1f0632","asr":{"eos":250,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3d8b@dx1956f58ad167844532"}
started:
ws start
####################
测试进行: ctm00011927@hu17ba70b96f40212902#47592845.pcm
