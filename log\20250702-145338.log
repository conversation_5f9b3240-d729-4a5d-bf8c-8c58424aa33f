1751439218
param:b'{\n            "auth_id": "d81793340fb04da681fb7cb693c1568a",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid8a5e3188@dxd2231bc023723eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"mqasr", "params": {"mid": "023806f343e444a88f1480b36512ab38","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be280@dx197c9e99991b8aa532"}
started:
ws start
####################
测试进行: ctm000d19d6@hu1934797d27204e0902#117600936.wav
{"recordId":"ase000f2fcd@hu197c9e9a4f505bf882:023806f343e444a88f1480b36512ab38","requestId":"ase000f2fcd@hu197c9e9a4f505bf882","sessionId":"cid000be280@dx197c9e99991b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1751439222}
{"recordId":"gty000be285@dx197c9e99bffb8aa532:023806f343e444a88f1480b36512ab38","requestId":"gty000be285@dx197c9e99bffb8aa532","sessionId":"cid000be280@dx197c9e99991b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1751439222}
{"recordId":"gty000be285@dx197c9e99bffb8aa532:023806f343e444a88f1480b36512ab38","requestId":"gty000be285@dx197c9e99bffb8aa532","sessionId":"cid000be280@dx197c9e99991b8aa532","eof":"1","text":"音量10%","oneshot_asr":"${oneshot_asr}","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1751439222}
ts:1751439222.5590732, sec_count:1, send silence begin
send data finished:1751439222.5630734
{"recordId":"gty000be285@dx197c9e99bffb8aa532:023806f343e444a88f1480b36512ab38","requestId":"gty000be285@dx197c9e99bffb8aa532","sessionId":"cid000be280@dx197c9e99991b8aa532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"音量10%","intentId":"INSTRUCTION","intentName":"音乐控制","nlg":"","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"音量10%","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"操作","value":"volume_select"},{"name":"百分比","value":"10"}]}}},"timestamp":1751439222}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid7f593c47@dx45be1bc023763eef00"}
连接正常关闭
1751439222
param:b'{\n            "auth_id": "abd308efa0d341ba8eec786e0aaa4259",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid75420012@dxb7831bc023763eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"mqasr", "params": {"mid": "54f016df3a474c01afb759175832dba4","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be291@dx197c9e9a845b8aa532"}
started:
ws start
####################
测试进行: ctm000dde24@hu19347966faf04e0902#117600897.wav
ts:1751439226.8891473, sec_count:1, send silence begin
{"recordId":"gty000be293@dx197c9e9aa21b8aa532:54f016df3a474c01afb759175832dba4","requestId":"gty000be293@dx197c9e9aa21b8aa532","sessionId":"cid000be291@dx197c9e9a845b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1751439226}
{"recordId":"gty000be293@dx197c9e9aa21b8aa532:54f016df3a474c01afb759175832dba4","requestId":"gty000be293@dx197c9e9aa21b8aa532","sessionId":"cid000be291@dx197c9e9a845b8aa532","eof":"1","text":"音量调到10","oneshot_asr":"${oneshot_asr}","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1751439226}
send data finished:1751439226.9325266
{"recordId":"gty000be293@dx197c9e9aa21b8aa532:54f016df3a474c01afb759175832dba4","requestId":"gty000be293@dx197c9e9aa21b8aa532","sessionId":"cid000be291@dx197c9e9a845b8aa532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"音量调到10","intentId":"INSTRUCTION","intentName":"音乐控制","nlg":"","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"音量调到10","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"级数","value":"10"},{"name":"操作","value":"volume_select"}]}}},"timestamp":1751439226}
{"recordId":"ase000f31c5@hu197c9e9b78d05bf882:54f016df3a474c01afb759175832dba4","requestId":"ase000f31c5@hu197c9e9b78d05bf882","sessionId":"cid000be291@dx197c9e9a845b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1751439227}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid9675f766@dxa9aa1bc0237b3eef00"}
连接正常关闭
1751439227
param:b'{\n            "auth_id": "f62498f3b63b46a8bdbecc3374956654",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidcabbfd0c@dx63cb1bc0237b3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"mqasr", "params": {"mid": "9fbac0f166274d259cb82cbfce6f5435","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bbef2@dx197c9e9ba36b86a532"}
started:
ws start
####################
测试进行: ctm000cfdfe@hu193487818b803a7902#117613482.wav
{"recordId":"ase000f846d@hu197c9e9c30c05c0882:9fbac0f166274d259cb82cbfce6f5435","requestId":"ase000f846d@hu197c9e9c30c05c0882","sessionId":"cid000bbef2@dx197c9e9ba36b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1751439229}
{"recordId":"gty000bbef5@dx197c9e9ba93b86a532:9fbac0f166274d259cb82cbfce6f5435","requestId":"gty000bbef5@dx197c9e9ba93b86a532","sessionId":"cid000bbef2@dx197c9e9ba36b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1751439229}
{"recordId":"gty000bbef5@dx197c9e9ba93b86a532:9fbac0f166274d259cb82cbfce6f5435","requestId":"gty000bbef5@dx197c9e9ba93b86a532","sessionId":"cid000bbef2@dx197c9e9ba36b86a532","eof":"1","text":"20℃","oneshot_asr":"${oneshot_asr}","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1751439229}
send data finished:1751439230.1084177
{"recordId":"gty000bbef5@dx197c9e9ba93b86a532:9fbac0f166274d259cb82cbfce6f5435","requestId":"gty000bbef5@dx197c9e9ba93b86a532","sessionId":"cid000bbef2@dx197c9e9ba36b86a532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"20℃","intentId":"chat","intentName":"闲聊","nlg":"光说个20我也听不懂呀，可以多说点吗？","shouldEndSession":true},"nlu":{"input":"20℃","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1751439230}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid9b1f161b@dxe9d81bc0237e3eef00"}
连接正常关闭
1751439230
param:b'{\n            "auth_id": "f9594347ec404348aaf6e5f56264ecdf",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid0b36ff04@dx430c1bc0237e3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"mqasr", "params": {"mid": "aa7987f37ef648458dc0ea55d7e7273b","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bbbda@dx197c9e9c682b8a9532"}
started:
ws start
####################
测试进行: ctm000d60a6@hu1934a4dd06004e0902#117641369.wav
{"recordId":"gty000bbbdd@dx197c9e9c806b8a9532:aa7987f37ef648458dc0ea55d7e7273b","requestId":"gty000bbbdd@dx197c9e9c806b8a9532","sessionId":"cid000bbbda@dx197c9e9c682b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1751439233}
{"recordId":"gty000bbbdd@dx197c9e9c806b8a9532:aa7987f37ef648458dc0ea55d7e7273b","requestId":"gty000bbbdd@dx197c9e9c806b8a9532","sessionId":"cid000bbbda@dx197c9e9c682b8a9532","eof":"1","text":"风速10","oneshot_asr":"${oneshot_asr}","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1751439233}
send data finished:1751439234.141659
{"recordId":"gty000bbbdd@dx197c9e9c806b8a9532:aa7987f37ef648458dc0ea55d7e7273b","requestId":"gty000bbbdd@dx197c9e9c806b8a9532","sessionId":"cid000bbbda@dx197c9e9c682b8a9532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"风速10","intentId":"chat","intentName":"闲聊","nlg":"虽然不想让你失望，但这个问题我真的不会。","shouldEndSession":true},"nlu":{"input":"风速10","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1751439234}
{"recordId":"ase000dd6d9@hu197c9e9d3ee04d3882:aa7987f37ef648458dc0ea55d7e7273b","requestId":"ase000dd6d9@hu197c9e9d3ee04d3882","sessionId":"cid000bbbda@dx197c9e9c682b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1751439234}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidaef8d265@dx0f961bc023823eef00"}
连接正常关闭
1751439234
param:b'{\n            "auth_id": "1e1ad79289fb4686943720ef363db6b2",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid618686ba@dxcdc81bc023823eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"mqasr", "params": {"mid": "8ef8e07649614e32ad1b780649ae4b05","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bbf0c@dx197c9e9d6c0b86a532"}
started:
ws start
####################
测试进行: ctm000d9398@hu1934ca4210104e0902#117648510.wav
{"recordId":"ase000f8731@hu197c9e9e23705c0882:8ef8e07649614e32ad1b780649ae4b05","requestId":"ase000f8731@hu197c9e9e23705c0882","sessionId":"cid000bbf0c@dx197c9e9d6c0b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1751439237}
{"recordId":"gty000bbf0d@dx197c9e9d752b86a532:8ef8e07649614e32ad1b780649ae4b05","requestId":"gty000bbf0d@dx197c9e9d752b86a532","sessionId":"cid000bbf0c@dx197c9e9d6c0b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1751439238}
{"recordId":"gty000bbf0d@dx197c9e9d752b86a532:8ef8e07649614e32ad1b780649ae4b05","requestId":"gty000bbf0d@dx197c9e9d752b86a532","sessionId":"cid000bbf0c@dx197c9e9d6c0b86a532","eof":"1","text":"升高到21.5度","oneshot_asr":"${oneshot_asr}","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1751439238}
send data finished:1751439239.1606064
{"recordId":"gty000bbf0d@dx197c9e9d752b86a532:8ef8e07649614e32ad1b780649ae4b05","requestId":"gty000bbf0d@dx197c9e9d752b86a532","sessionId":"cid000bbf0c@dx197c9e9d6c0b86a532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"升高到21.5度","intentId":"chat","intentName":"闲聊","nlg":"等我学习一下，再来回答吧！现在不如问点其他的吧。","shouldEndSession":true},"nlu":{"input":"升高到21.5度","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1751439239}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid4bfa82eb@dxf47b1bc023873eef00"}
连接正常关闭
1751439239
param:b'{\n            "auth_id": "7c1c1f7c781e491f8da9f11917f6dfed",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid843254fa@dx9e541bc023873eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"mqasr", "params": {"mid": "a21396b164be485486c94623fa931f11","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000be2f4@dx197c9e9e991b8aa532"}
started:
ws start
####################
测试进行: ctm000c415f@hu1934a4dca2003a7902#117641364.wav
{"recordId":"gty000be2f5@dx197c9e9ea05b8aa532:a21396b164be485486c94623fa931f11","requestId":"gty000be2f5@dx197c9e9ea05b8aa532","sessionId":"cid000be2f4@dx197c9e9e991b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1751439242}
{"recordId":"gty000be2f5@dx197c9e9ea05b8aa532:a21396b164be485486c94623fa931f11","requestId":"gty000be2f5@dx197c9e9ea05b8aa532","sessionId":"cid000be2f4@dx197c9e9e991b8aa532","eof":"1","text":"19度","oneshot_asr":"${oneshot_asr}","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1751439242}
send data finished:1751439242.5272222
{"recordId":"ase000eb124@hu197c9e9f47405c2882:a21396b164be485486c94623fa931f11","requestId":"ase000eb124@hu197c9e9f47405c2882","sessionId":"cid000be2f4@dx197c9e9e991b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1751439242}
{"recordId":"gty000be2f5@dx197c9e9ea05b8aa532:a21396b164be485486c94623fa931f11","requestId":"gty000be2f5@dx197c9e9ea05b8aa532","sessionId":"cid000be2f4@dx197c9e9e991b8aa532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"19度","intentId":"chat","intentName":"闲聊","nlg":"我是诚实的好孩子，这个问题我也不会。","shouldEndSession":true},"nlu":{"input":"19度","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1751439242}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidfc4b9715@dxf8d51bc0238a3eef00"}
连接正常关闭
1751439242
param:b'{\n            "auth_id": "0852850f50fd4a75af95eaad01e53cb0",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidf87042e9@dx50921bc0238a3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"mqasr", "params": {"mid": "8f6bbc52d08b4d758cd17f374f3f7e23","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b645d@dx197c9e9f68f7844532"}
started:
ws start
####################
测试进行: ctm000dcfdf@hu19347961f6304e0902#117600825.wav
{"recordId":"ase000f3a25@hu197c9ea091305bf882:8f6bbc52d08b4d758cd17f374f3f7e23","requestId":"ase000f3a25@hu197c9ea091305bf882","sessionId":"cid000b645d@dx197c9e9f68f7844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1751439247}
{"recordId":"gty000b645e@dx197c9e9f7007844532:8f6bbc52d08b4d758cd17f374f3f7e23","requestId":"gty000b645e@dx197c9e9f7007844532","sessionId":"cid000b645d@dx197c9e9f68f7844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1751439248}
{"recordId":"gty000b645e@dx197c9e9f7007844532:8f6bbc52d08b4d758cd17f374f3f7e23","requestId":"gty000b645e@dx197c9e9f7007844532","sessionId":"cid000b645d@dx197c9e9f68f7844532","eof":"1","text":"减少0.5度","oneshot_asr":"${oneshot_asr}","pinyin":"","topic":"asr.speech.result","age":"elder","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1751439248}
send data finished:1751439248.2233596
{"recordId":"gty000b645e@dx197c9e9f7007844532:8f6bbc52d08b4d758cd17f374f3f7e23","requestId":"gty000b645e@dx197c9e9f7007844532","sessionId":"cid000b645d@dx197c9e9f68f7844532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"减少0.5度","intentId":"chat","intentName":"闲聊","nlg":"机器人也不是万能的噢，这个我也不知道。","shouldEndSession":true},"nlu":{"input":"减少0.5度","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1751439248}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid78c02108@dxf2031bc023903eef00"}
连接正常关闭
#####运行完成,点击查看结果按钮查看结果!##########
