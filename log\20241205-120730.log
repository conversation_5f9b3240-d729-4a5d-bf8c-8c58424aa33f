1733371651
param:b'{\n            "auth_id": "8181f2868e634801bf692ebdb997ba8e",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid5a8a06dd@dx4dac1aac73043eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"asr", "params": {"mid": "8c793db8fdd84845842d83618060582b","asr":{"eos":400,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "opus-wb","channels": 1,"bit_depth": 16,"sample_rate": 16000},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000d2654@dx19395006a897824532"}
started:
ws start
####################
测试进行: ctm00010320@hu17b4cb503aa020c902#46275237.opus
{"recordId":"wgw000d2655@dx19395006aca7824532:8c793db8fdd84845842d83618060582b","requestId":"wgw000d2655@dx19395006aca7824532","sessionId":"cid000d2654@dx19395006a897824532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1733371655}
{"recordId":"wgw000d2655@dx19395006aca7824532:8c793db8fdd84845842d83618060582b","requestId":"wgw000d2655@dx19395006aca7824532","sessionId":"cid000d2654@dx19395006a897824532","eof":"1","text":"杨幂是谁","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1733371655}
send data finished
{"recordId":"ase000e2a7c@hu1939500760905bf882:8c793db8fdd84845842d83618060582b","requestId":"ase000e2a7c@hu1939500760905bf882","sessionId":"cid000d2654@dx19395006a897824532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1733371655}
{"recordId":"wgw000d2655@dx19395006aca7824532:8c793db8fdd84845842d83618060582b","requestId":"wgw000d2655@dx19395006aca7824532","sessionId":"cid000d2654@dx19395006a897824532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"杨幂是谁","intentId":"chat","intentName":"闲聊","nlg":"杨幂，1986年9月12日出生于北京市，演员、歌手、制片人，代表作有《神雕侠侣》、《亲爱的翻译官》、《三生三世十里桃花》等。","shouldEndSession":true},"nlu":{"input":"杨幂是谁","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1733371655}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidb0b3bc2c@dx239f1aac73073eef00"}
连接正常关闭
#####运行完成,点击查看结果按钮查看结果!##########
