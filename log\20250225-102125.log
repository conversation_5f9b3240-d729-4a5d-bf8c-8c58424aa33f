1740450086
param:b'{\n            "auth_id": "72140c1a08dd443c9c854bb8afd784ac",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid191b43c7@dxc86b1b1875263eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "d10564e8780a4d389e751182eec16168","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b41b3@dx1953ae8c052b86a532"}
started:
ws start
####################
测试进行: ctm00010030@hu17be9d9d04b020c902#48546724.pcm
{"recordId":"ase000e3f84@hu1953ae8ca4b1323882:d10564e8780a4d389e751182eec16168","requestId":"ase000e3f84@hu1953ae8ca4b1323882","sessionId":"cid000b41b3@dx1953ae8c052b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450089}
{"recordId":"gty000b41b4@dx1953ae8c109b86a532:d10564e8780a4d389e751182eec16168","requestId":"gty000b41b4@dx1953ae8c109b86a532","sessionId":"cid000b41b3@dx1953ae8c052b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450091}
{"recordId":"gty000b41b4@dx1953ae8c109b86a532:d10564e8780a4d389e751182eec16168","requestId":"gty000b41b4@dx1953ae8c109b86a532","sessionId":"cid000b41b3@dx1953ae8c052b86a532","eof":"1","text":"河北今日的24小时降水多少","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1740450091}
send data finished:1740450091.549796
{"recordId":"gty000b41b4@dx1953ae8c109b86a532:d10564e8780a4d389e751182eec16168","requestId":"gty000b41b4@dx1953ae8c109b86a532","sessionId":"cid000b41b3@dx1953ae8c052b86a532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"河北今日的24小时降水多少","intentId":"chat","intentName":"闲聊","nlg":"我就是个机器人，这个问题也太难啦。你简单点说呗！","shouldEndSession":true},"nlu":{"input":"河北今日的24小时降水多少","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1740450091}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid65b03968@dxa7331b18752b3eef00"}
连接正常关闭
1740450091
param:b'{\n            "auth_id": "73e10eb1f1184f9ca4872153d48ffd82",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cide2f336f5@dx06e31b18752b3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "28d028198fdf4354be756d80e82b82a1","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b4002@dx1953ae8d443b8a9532"}
started:
ws start
####################
测试进行: ctm00010036@hu17be9d9d06f020c902#48546721.pcm
{"recordId":"ase000e4324@hu1953ae8de351323882:28d028198fdf4354be756d80e82b82a1","requestId":"ase000e4324@hu1953ae8de351323882","sessionId":"cid000b4002@dx1953ae8d443b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450094}
{"recordId":"gty000b4003@dx1953ae8d4f6b8a9532:28d028198fdf4354be756d80e82b82a1","requestId":"gty000b4003@dx1953ae8d4f6b8a9532","sessionId":"cid000b4002@dx1953ae8d443b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450095}
{"recordId":"gty000b4003@dx1953ae8d4f6b8a9532:28d028198fdf4354be756d80e82b82a1","requestId":"gty000b4003@dx1953ae8d4f6b8a9532","sessionId":"cid000b4002@dx1953ae8d443b8a9532","eof":"1","text":"未来三天湖北天气预报","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1740450095}
send data finished:1740450095.7173538
{"recordId":"gty000b4003@dx1953ae8d4f6b8a9532:28d028198fdf4354be756d80e82b82a1","requestId":"gty000b4003@dx1953ae8d4f6b8a9532","sessionId":"cid000b4002@dx1953ae8d443b8a9532","topic":"dm.output","skill":"天气","skillId":"2019042500000544","speakUrl":"","error":{},"dm":{"input":"未来三天湖北天气预报","intentId":"QUERY","intentName":"查询天气信息","nlg":"请问你想查询湖北哪个城市的天气？","widget":{"cityName":""},"dm_intent":"custom","cityName":"","shouldEndSession":false},"nlu":{"input":"未来三天湖北天气预报","skill":"天气","skillId":"2019042500000544","skillVersion":"56","semantics":{"request":{"intent":"QUERY","slots":[{"name":"queryType","value":"内容"},{"name":"subfocus","value":"天气状态"},{"name":"datetime","normValue":"{\"datetime\":\"2025-02-25/2025-02-28\",\"suggestDatetime\":\"2025-02-25/2025-02-28\"}","value":"未来三天"},{"name":"location.province","normValue":"湖北省","value":"湖北省"},{"name":"location.provinceAddr","normValue":"湖北","value":"湖北"},{"name":"location.type","normValue":"LOC_BASIC","value":"LOC_BASIC"}]}}},"timestamp":1740450095}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid8160e4d1@dx30f51b18752f3eef00"}
连接正常关闭
1740450095
param:b'{\n            "auth_id": "56f8cedbd5154c55afd01147bc72746a",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cide8d5a202@dx03971b1875303eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "24fc1e2be0f34c3ab0a5f98700882a99","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b4008@dx1953ae8e418b8a9532"}
started:
ws start
####################
测试进行: ctm00010043@hu17be9d9d09e020c902#48546725.pcm
{"action":"error","code":"10907","data":"","desc":"10037;code=10037","sid":"ase000ee6fd@hu1953ae8e4050336882"}
{'action': 'error', 'code': '10907', 'data': '', 'desc': '10037;code=10037', 'sid': 'ase000ee6fd@hu1953ae8e4050336882'}
发送结束标识
发送断开连接标识
send data finished:1740450097.0808933
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid6dcb4ecd@dxd31c1b1875313eef00"}
连接正常关闭
1740450097
param:b'{\n            "auth_id": "ea4e6ecb1ae5480fb5526b02d0209898",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid4f58ccae@dxc0e61b1875313eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "795991c3880f44eba04744217aefe14a","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b4446@dx1953ae8e91d7844532"}
started:
ws start
####################
测试进行: ctm00010045@hu17be9d9d0a9020c902#48546723.pcm
send data finished:1740450111.0586293
{"action":"error","code":"20010108","data":"","desc":"{\"code\":20010108,\"sub\":\"\"}","sid":"gty000b4447@dx1953ae8e9d07844532"}
{'action': 'error', 'code': '20010108', 'data': '', 'desc': '{"code":20010108,"sub":""}', 'sid': 'gty000b4447@dx1953ae8e9d07844532'}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidc4862fd0@dx19b81b18754e3eef00"}
连接正常关闭
1740450126
param:b'{\n            "auth_id": "3b066dece4fa4c598f7f19e3a5ca0b2f",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid873c3224@dxb22b1b18754e3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "08ed649d94814371987c51d59da3d05f","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b41c6@dx1953ae95c67b86a532"}
started:
ws start
####################
测试进行: ctm00010047@hu17be9d9d0ac020c902#48546726.pcm
{"recordId":"ase000de6ac@hu1953ae968800427882:08ed649d94814371987c51d59da3d05f","requestId":"ase000de6ac@hu1953ae968800427882","sessionId":"cid000b41c6@dx1953ae95c67b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450130}
{"recordId":"gty000b41c7@dx1953ae95d02b86a532:08ed649d94814371987c51d59da3d05f","requestId":"gty000b41c7@dx1953ae95d02b86a532","sessionId":"cid000b41c6@dx1953ae95c67b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450130}
{"recordId":"gty000b41c7@dx1953ae95d02b86a532:08ed649d94814371987c51d59da3d05f","requestId":"gty000b41c7@dx1953ae95d02b86a532","sessionId":"cid000b41c6@dx1953ae95c67b86a532","eof":"1","text":"我想知道全国台风天气预报","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1740450130}
send data finished:1740450130.741985
{"recordId":"gty000b41c7@dx1953ae95d02b86a532:08ed649d94814371987c51d59da3d05f","requestId":"gty000b41c7@dx1953ae95d02b86a532","sessionId":"cid000b41c6@dx1953ae95c67b86a532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"我想知道全国台风天气预报","intentId":"chat","intentName":"闲聊","nlg":"不太懂欸，这个台风你熟悉吗？","shouldEndSession":true},"nlu":{"input":"我想知道全国台风天气预报","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1740450130}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid053938bc@dx582d1b1875523eef00"}
连接正常关闭
1740450130
param:b'{\n            "auth_id": "5653b2538e224c04ac6790350f2d5b44",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid74ba8b56@dx16061b1875533eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "a2c59aa8873941808231ea153cfc7d84","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b41cb@dx1953ae96d28b86a532"}
started:
ws start
####################
测试进行: ctm0001004c@hu17be9d9d0bb020c902#48546727.pcm
{"recordId":"ase000e277d@hu1953ae9788805c3882:a2c59aa8873941808231ea153cfc7d84","requestId":"ase000e277d@hu1953ae9788805c3882","sessionId":"cid000b41cb@dx1953ae96d28b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450134}
{"recordId":"gty000b41cc@dx1953ae96dbdb86a532:a2c59aa8873941808231ea153cfc7d84","requestId":"gty000b41cc@dx1953ae96dbdb86a532","sessionId":"cid000b41cb@dx1953ae96d28b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450135}
{"recordId":"gty000b41cc@dx1953ae96dbdb86a532:a2c59aa8873941808231ea153cfc7d84","requestId":"gty000b41cc@dx1953ae96dbdb86a532","sessionId":"cid000b41cb@dx1953ae96d28b86a532","eof":"1","text":"南京今日的空气质量指数如何","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1740450135}
send data finished:1740450135.189883
{"recordId":"gty000b41cc@dx1953ae96dbdb86a532:a2c59aa8873941808231ea153cfc7d84","requestId":"gty000b41cc@dx1953ae96dbdb86a532","sessionId":"cid000b41cb@dx1953ae96d28b86a532","topic":"dm.output","skill":"天气","skillId":"2019042500000544","speakUrl":"","error":{},"dm":{"input":"南京今日的空气质量指数如何","intentId":"QUERY","intentName":"查询天气信息","nlg":"今日南京市空气质量良，空气质量指数98，pm2.5指数98","widget":{"webhookResp":{"result":[{"airData":98,"airQuality":"良","city":"南京市","date":"2025-02-25","dateLong":1740412800,"date_for_voice":"今天","exp":{"ct":{"expName":"穿衣指数","level":"凉","prompt":"天气偏凉，可以穿上最时尚的那件大衣来凹造型，搭一条围巾时尚指数爆表。"},"dy":{"expName":"钓鱼指数","level":"较适宜","prompt":"气压小幅波动，可能会影响鱼儿的进食。"},"gm":{"expName":"感冒指数","level":"较易发","prompt":"感冒较易发生，干净整洁的环境和清新流通的空气都有利于降低感冒的几率，体质较弱的童鞋们要特别加强自我保护。"},"jt":{"expName":"交通指数","level":"良好","prompt":"天气较好，路面干燥，交通气象条件良好，车辆可以正常行驶。"},"tr":{"expName":"旅游指数","level":"适宜","prompt":"天气较好，温度适宜，但风稍微有点大。这样的天气适宜旅游，您可以尽情地享受大自然的无限风光。"},"uv":{"expName":"紫外线指数","level":"最弱","prompt":"辐射弱，请涂擦SPF8-12防晒护肤品。"},"xc":{"expName":"洗车指数","level":"较适宜","prompt":"较适宜洗车，未来一天无雨，风力较小，擦洗一新的汽车至少能保持一天。"},"yd":{"expName":"运动指数","level":"不适宜","prompt":"气压有点偏低了，较不适宜在户外进行剧烈运动。"}},"extra":"","humidity":"62%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-02-25 10:00:08","pm25":"98","precipitation":"0","sunRise":"2025-02-25 06:37:00","sunSet":"2025-02-25 17:59:00","temp":5,"tempHigh":"10℃","tempLow":"3℃","tempRange":"3℃ ~ 10℃","tempReal":"3℃","visibility":"","warning":"","weather":"多云","weatherDescription":"有点冷。","weatherDescription3":"3℃到20℃，风不大，有点凉。","weatherDescription7":"13℃到9℃，2号、3号有雨，风不大，有点凉。","weatherType":1,"week":"周二","wind":"东南风微风","windLevel":0},{"airData":64,"airQuality":"良","city":"南京市","date":"2025-02-24","dateLong":1740326400,"date_for_voice":"昨天","humidity":"43%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-02-24 06:38:00","sunSet":"2025-02-24 17:58:00","tempHigh":"8℃","tempLow":"2℃","tempRange":"2℃ ~ 8℃","weather":"晴转阴","weatherDescription":"有点冷。","weatherType":0,"week":"周一","wind":"东风转东南风微风","windLevel":0},{"airData":100,"airQuality":"良","city":"南京市","date":"2025-02-26","dateLong":1740499200,"date_for_voice":"明天","humidity":"58%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-02-26 06:36:00","sunSet":"2025-02-26 18:00:00","tempHigh":"16℃","tempLow":"7℃","tempRange":"7℃ ~ 16℃","weather":"多云","weatherDescription":"有点凉。","weatherType":1,"week":"周三","wind":"东南风3-4级","windLevel":1},{"airData":92,"airQuality":"良","city":"南京市","date":"2025-02-27","dateLong":1740585600,"date_for_voice":"后天","humidity":"74%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-02-27 06:34:00","sunSet":"2025-02-27 18:01:00","tempHigh":"20℃","tempLow":"9℃","tempRange":"9℃ ~ 20℃","weather":"多云","weatherDescription":"温度适宜。","weatherType":1,"week":"周四","wind":"西南风转东南风微风","windLevel":0},{"airData":85,"airQuality":"良","city":"南京市","date":"2025-02-28","dateLong":1740672000,"date_for_voice":"28号","humidity":"79%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-02-28 06:33:00","sunSet":"2025-02-28 18:01:00","tempHigh":"24℃","tempLow":"13℃","tempRange":"13℃ ~ 24℃","weather":"晴转多云","weatherDescription":"气候温暖。","weatherType":0,"week":"周五","wind":"东南风3-4级","windLevel":1},{"airData":90,"airQuality":"良","city":"南京市","date":"2025-03-01","dateLong":1740758400,"date_for_voice":"1号","humidity":"81%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-01 06:32:00","sunSet":"2025-03-01 18:02:00","tempHigh":"26℃","tempLow":"16℃","tempRange":"16℃ ~ 26℃","weather":"多云转阴","weatherDescription":"气候温暖。","weatherType":1,"week":"周六","wind":"南风转东风3-4级","windLevel":1},{"airData":77,"airQuality":"良","city":"南京市","date":"2025-03-02","dateLong":1740844800,"date_for_voice":"2号","humidity":"92%","img":"http://cdn9002.iflyos.cn/osweathericon/09.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-02 06:31:00","sunSet":"2025-03-02 18:03:00","tempHigh":"23℃","tempLow":"7℃","tempRange":"7℃ ~ 23℃","weather":"大雨转中雨","weatherDescription":"温度适宜。","weatherType":9,"week":"周日","wind":"东北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"南京市","date":"2025-03-03","dateLong":1740931200,"date_for_voice":"3号","humidity":"81%","img":"http://cdn9002.iflyos.cn/osweathericon/08.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-03 06:30:00","sunSet":"2025-03-03 18:04:00","tempHigh":"9℃","tempLow":"3℃","tempRange":"3℃ ~ 9℃","weather":"中雨","weatherDescription":"有点冷。","weatherType":8,"week":"下周一","wind":"东北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"南京市","date":"2025-03-04","dateLong":1741017600,"date_for_voice":"4号","humidity":"81%","img":"http://cdn9002.iflyos.cn/osweathericon/07.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-04 06:29:00","sunSet":"2025-03-04 18:04:00","tempHigh":"7℃","tempLow":"3℃","tempRange":"3℃ ~ 7℃","weather":"小雨转阴","weatherDescription":"有点冷。","weatherType":7,"week":"下周二","wind":"西北风转东北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"南京市","date":"2025-03-05","dateLong":1741104000,"date_for_voice":"5号","humidity":"76%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-05 06:27:00","sunSet":"2025-03-05 18:05:00","tempHigh":"13℃","tempLow":"2℃","tempRange":"2℃ ~ 13℃","weather":"阴","weatherDescription":"有点冷。","weatherType":2,"week":"下周三","wind":"东北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"南京市","date":"2025-03-06","dateLong":1741190400,"date_for_voice":"6号","humidity":"81%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-06 06:26:00","sunSet":"2025-03-06 18:06:00","tempHigh":"10℃","tempLow":"1℃","tempRange":"1℃ ~ 10℃","weather":"阴","weatherDescription":"有点冷。","weatherType":2,"week":"下周四","wind":"东北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"南京市","date":"2025-03-07","dateLong":1741276800,"date_for_voice":"7号","humidity":"72%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-07 06:25:00","sunSet":"2025-03-07 18:07:00","tempHigh":"8℃","tempLow":"0℃","tempRange":"0℃ ~ 8℃","weather":"阴","weatherDescription":"有点冷。","weatherType":2,"week":"下周五","wind":"西北风转东北风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"南京市","date":"2025-03-08","dateLong":1741363200,"date_for_voice":"8号","humidity":"69%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-08 06:24:00","sunSet":"2025-03-08 18:07:00","tempHigh":"8℃","tempLow":"2℃","tempRange":"2℃ ~ 8℃","weather":"阴","weatherDescription":"有点冷。","weatherType":2,"week":"下周六","wind":"东北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"南京市","date":"2025-03-09","dateLong":1741449600,"date_for_voice":"9号","humidity":"72%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-09 06:22:00","sunSet":"2025-03-09 18:08:00","tempHigh":"11℃","tempLow":"4℃","tempRange":"4℃ ~ 11℃","weather":"阴","weatherDescription":"有点冷。","weatherType":2,"week":"下周日","wind":"东北风转东风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"南京市","date":"2025-03-10","dateLong":1741536000,"date_for_voice":"10号","humidity":"73%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-10 06:21:00","sunSet":"2025-03-10 18:09:00","tempHigh":"13℃","tempLow":"0℃","tempRange":"0℃ ~ 13℃","weather":"晴转阴","weatherDescription":"有点冷。","weatherType":0,"week":"下下周一","wind":"东北风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"南京市","date":"2025-03-11","dateLong":1741622400,"date_for_voice":"11号","humidity":"75%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-11 06:20:00","sunSet":"2025-03-11 18:10:00","tempHigh":"12℃","tempLow":"1℃","tempRange":"1℃ ~ 12℃","weather":"阴","weatherDescription":"有点冷。","weatherType":2,"week":"下下周二","wind":"东北风微风","windLevel":0}]},"cityName":"南京市"},"dm_intent":"custom","cityName":"南京市","shouldEndSession":true},"nlu":{"input":"南京今日的空气质量指数如何","skill":"天气","skillId":"2019042500000544","skillVersion":"56","semantics":{"request":{"intent":"QUERY","slots":[{"name":"datetime","normValue":"{\"datetime\":\"2025-02-25\",\"suggestDatetime\":\"2025-02-25\"}","value":"今日"},{"name":"location.type","normValue":"LOC_BASIC","value":"LOC_BASIC"},{"name":"location.city","normValue":"南京市","value":"南京市"},{"name":"location.cityAddr","normValue":"南京","value":"南京"},{"name":"queryType","value":"内容"},{"name":"subfocus","value":"pm25"}]}}},"timestamp":1740450135}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid77705ee5@dxbf611b1875573eef00"}
连接正常关闭
1740450135
param:b'{\n            "auth_id": "a2b1e7f6d580430e8393906febae1754",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid0a98b63e@dxa94a1b1875573eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "ec8785bcc3604d08a68d08adeaca6606","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b4020@dx1953ae97e5cb8a9532"}
started:
ws start
####################
测试进行: ctm0001004f@hu17be9d9d0ce020c902#48546729.pcm
send data finished:1740450149.5184934
{"action":"error","code":"20010108","data":"","desc":"{\"code\":20010108,\"sub\":\"\"}","sid":"gty000b4021@dx1953ae97efbb8a9532"}
{'action': 'error', 'code': '20010108', 'data': '', 'desc': '{"code":20010108,"sub":""}', 'sid': 'gty000b4021@dx1953ae97efbb8a9532'}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid2884ffd9@dxc2b81b1875753eef00"}
连接正常关闭
1740450165
param:b'{\n            "auth_id": "7e70e232ca3345799b25c137b7dfbd6d",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid7cbc6aea@dx3b6d1b1875753eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "b435868007a943a0ac523e693953431d","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b41f2@dx1953ae9f2efb86a532"}
started:
ws start
####################
测试进行: ctm000100cf@hu17be9d9d4a6020c902#48546734.pcm
{"recordId":"ase000fdfbb@hu1953ae9fcd705c0882:b435868007a943a0ac523e693953431d","requestId":"ase000fdfbb@hu1953ae9fcd705c0882","sessionId":"cid000b41f2@dx1953ae9f2efb86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450168}
{"recordId":"gty000b41f3@dx1953ae9f395b86a532:b435868007a943a0ac523e693953431d","requestId":"gty000b41f3@dx1953ae9f395b86a532","sessionId":"cid000b41f2@dx1953ae9f2efb86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450168}
{"recordId":"gty000b41f3@dx1953ae9f395b86a532:b435868007a943a0ac523e693953431d","requestId":"gty000b41f3@dx1953ae9f395b86a532","sessionId":"cid000b41f2@dx1953ae9f2efb86a532","eof":"1","text":"云南省今日的风力情况","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1740450168}
send data finished:1740450168.6948621
{"recordId":"gty000b41f3@dx1953ae9f395b86a532:b435868007a943a0ac523e693953431d","requestId":"gty000b41f3@dx1953ae9f395b86a532","sessionId":"cid000b41f2@dx1953ae9f2efb86a532","topic":"dm.output","skill":"天气","skillId":"2019042500000544","speakUrl":"","error":{},"dm":{"input":"云南省今日的风力情况","intentId":"QUERY","intentName":"查询天气信息","nlg":"请问你想查询云南省哪个城市的天气？","widget":{"cityName":""},"dm_intent":"custom","cityName":"","shouldEndSession":false},"nlu":{"input":"云南省今日的风力情况","skill":"天气","skillId":"2019042500000544","skillVersion":"56","semantics":{"request":{"intent":"QUERY","slots":[{"name":"location.province","normValue":"云南省","value":"云南省"},{"name":"location.provinceAddr","normValue":"云南","value":"云南"},{"name":"location.type","normValue":"LOC_BASIC","value":"LOC_BASIC"},{"name":"queryType","value":"内容"},{"name":"subfocus","value":"风力情况"},{"name":"datetime","normValue":"{\"datetime\":\"2025-02-25\",\"suggestDatetime\":\"2025-02-25\"}","value":"今日"}]}}},"timestamp":1740450168}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid9542319d@dx2c771b1875783eef00"}
连接正常关闭
1740450168
param:b'{\n            "auth_id": "5ad3a998dc614100af6808009eb4f7ec",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid8632c5b2@dx7f871b1875793eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "9d125e51e61641c1bfcc2a1460f3be9c","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b4486@dx1953aea01497844532"}
started:
ws start
####################
测试进行: ctm000100d0@hu17be9d9d4a6020c902#48546732.pcm
{"recordId":"ase000e7dc1@hu1953aea0aeb1323882:9d125e51e61641c1bfcc2a1460f3be9c","requestId":"ase000e7dc1@hu1953aea0aeb1323882","sessionId":"cid000b4486@dx1953aea01497844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450171}
{"recordId":"gty000b4487@dx1953aea01fc7844532:9d125e51e61641c1bfcc2a1460f3be9c","requestId":"gty000b4487@dx1953aea01fc7844532","sessionId":"cid000b4486@dx1953aea01497844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450172}
{"recordId":"gty000b4487@dx1953aea01fc7844532:9d125e51e61641c1bfcc2a1460f3be9c","requestId":"gty000b4487@dx1953aea01fc7844532","sessionId":"cid000b4486@dx1953aea01497844532","eof":"1","text":"四川省最新的天气预警","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1740450172}
send data finished:1740450172.7718492
{"recordId":"gty000b4487@dx1953aea01fc7844532:9d125e51e61641c1bfcc2a1460f3be9c","requestId":"gty000b4487@dx1953aea01fc7844532","sessionId":"cid000b4486@dx1953aea01497844532","topic":"dm.output","skill":"天气","skillId":"2019042500000544","speakUrl":"","error":{},"dm":{"input":"四川省最新的天气预警","intentId":"QUERY","intentName":"查询天气信息","nlg":"请问你想查询四川省哪个城市的天气？","widget":{"cityName":""},"dm_intent":"custom","cityName":"","shouldEndSession":false},"nlu":{"input":"四川省最新的天气预警","skill":"天气","skillId":"2019042500000544","skillVersion":"56","semantics":{"request":{"intent":"QUERY","slots":[{"name":"datetime","normValue":"{\"datetime\":\"\",\"suggestDatetime\":\"\"}","value":"最新"},{"name":"location.province","normValue":"四川省","value":"四川省"},{"name":"location.provinceAddr","normValue":"四川","value":"四川"},{"name":"location.type","normValue":"LOC_BASIC","value":"LOC_BASIC"},{"name":"queryType","value":"内容"},{"name":"subfocus","value":"天气状况"}]}}},"timestamp":1740450172}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid12902f5a@dx90171b18757c3eef00"}
连接正常关闭
1740450172
param:b'{\n            "auth_id": "8de77abcaf58460da6af46ebe0e42b24",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid79c39cd7@dxdf571b18757d3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "6d591cdf4574413db7946de5871bf673","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b448a@dx1953aea11117844532"}
started:
ws start
####################
测试进行: ctm000100d1@hu17be9d9d4ad020c902#48546733.pcm
{"recordId":"ase000e57d1@hu1953aea1bc205c4882:6d591cdf4574413db7946de5871bf673","requestId":"ase000e57d1@hu1953aea1bc205c4882","sessionId":"cid000b448a@dx1953aea11117844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450176}
{"recordId":"gty000b448b@dx1953aea11d27844532:6d591cdf4574413db7946de5871bf673","requestId":"gty000b448b@dx1953aea11d27844532","sessionId":"cid000b448a@dx1953aea11117844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450176}
{"recordId":"gty000b448b@dx1953aea11d27844532:6d591cdf4574413db7946de5871bf673","requestId":"gty000b448b@dx1953aea11d27844532","sessionId":"cid000b448a@dx1953aea11117844532","eof":"1","text":"北京下午3点会下雨吗","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1740450176}
send data finished:1740450176.8476164
{"recordId":"gty000b448b@dx1953aea11d27844532:6d591cdf4574413db7946de5871bf673","requestId":"gty000b448b@dx1953aea11d27844532","sessionId":"cid000b448a@dx1953aea11117844532","topic":"dm.output","skill":"天气","skillId":"2019042500000544","speakUrl":"","error":{},"dm":{"input":"北京下午3点会下雨吗","intentId":"QUERY","intentName":"查询天气信息","nlg":"下午3点北京市晴，不下雨","widget":{"webhookResp":{"result":[{"airData":54,"airQuality":"良","city":"北京市","date":"2025-02-25","dateLong":1740412800,"date_for_voice":"今天","exp":{"ct":{"expName":"穿衣指数","level":"凉","prompt":"天气偏凉，可以穿上最时尚的那件大衣来凹造型，搭一条围巾时尚指数爆表。"},"dy":{"expName":"钓鱼指数","level":"较适宜","prompt":"气压小幅波动，可能会影响鱼儿的进食。"},"gm":{"expName":"感冒指数","level":"易发","prompt":"感冒容易发生，少去人群密集的场所有利于降低感冒的几率。"},"jt":{"expName":"交通指数","level":"良好","prompt":"天气较好，路面干燥，交通气象条件良好，车辆可以正常行驶。"},"tr":{"expName":"旅游指数","level":"适宜","prompt":"天气较好，温度适宜，是个好天气哦。这样的天气适宜旅游，您可以尽情地享受大自然的风光。"},"uv":{"expName":"紫外线指数","level":"弱","prompt":"辐射较弱，请涂擦SPF12-15、PA+护肤品。"},"xc":{"expName":"洗车指数","level":"较适宜","prompt":"较适宜洗车，未来一天无雨，风力较小，擦洗一新的汽车至少能保持一天。"},"yd":{"expName":"运动指数","level":"不适宜","prompt":"气温过低，特别容易着凉感冒，较不适宜户外运动，建议室内运动。"}},"extra":"","humidity":"10%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-02-25 10:00:08","pm25":"34","precipitation":"0","sunRise":"2025-02-25 06:53:00","sunSet":"2025-02-25 18:01:00","temp":8,"tempHigh":"10℃","tempLow":"-3℃","tempRange":"-3℃ ~ 10℃","tempReal":"2℃","visibility":"","warning":"大风蓝色预警","weather":"晴","weatherDescription":"有点冷。","weatherDescription3":"-1℃到14℃，风不大，有点冷。北京市气象台发布大风蓝色预警信号。","weatherDescription7":"-1℃到9℃，风不大，有点冷。北京市气象台发布大风蓝色预警信号。","weatherType":0,"week":"周二","wind":"西北风4-5级","windLevel":0},{"airData":42,"airQuality":"优","city":"北京市","date":"2025-02-24","dateLong":1740326400,"date_for_voice":"昨天","humidity":"14%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-02-24 06:55:00","sunSet":"2025-02-24 18:00:00","tempHigh":"9℃","tempLow":"-3℃","tempRange":"-3℃ ~ 9℃","weather":"晴","weatherDescription":"有点冷。","weatherType":0,"week":"周一","wind":"西南风转西北风微风","windLevel":0},{"airData":70,"airQuality":"良","city":"北京市","date":"2025-02-26","dateLong":1740499200,"date_for_voice":"明天","humidity":"18%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-02-26 06:52:00","sunSet":"2025-02-26 18:02:00","tempHigh":"12℃","tempLow":"-1℃","tempRange":"-1℃ ~ 12℃","weather":"晴转多云","weatherDescription":"有点冷。","weatherType":0,"week":"周三","wind":"西南风转北风微风","windLevel":0},{"airData":110,"airQuality":"轻微污染","city":"北京市","date":"2025-02-27","dateLong":1740585600,"date_for_voice":"后天","humidity":"21%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-02-27 06:51:00","sunSet":"2025-02-27 18:04:00","tempHigh":"14℃","tempLow":"1℃","tempRange":"1℃ ~ 14℃","weather":"多云转晴","weatherDescription":"有点凉。","weatherType":1,"week":"周四","wind":"西南风微风","windLevel":0},{"airData":110,"airQuality":"轻微污染","city":"北京市","date":"2025-02-28","dateLong":1740672000,"date_for_voice":"28号","humidity":"16%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-02-28 06:49:00","sunSet":"2025-02-28 18:05:00","tempHigh":"16℃","tempLow":"3℃","tempRange":"3℃ ~ 16℃","weather":"晴转多云","weatherDescription":"有点凉。","weatherType":0,"week":"周五","wind":"东南风转西北风微风","windLevel":0},{"airData":130,"airQuality":"轻微污染","city":"北京市","date":"2025-03-01","dateLong":1740758400,"date_for_voice":"1号","humidity":"44%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-01 06:48:00","sunSet":"2025-03-01 18:06:00","tempHigh":"12℃","tempLow":"3℃","tempRange":"3℃ ~ 12℃","weather":"多云转阴","weatherDescription":"有点冷。","weatherType":1,"week":"周六","wind":"东北风微风","windLevel":0},{"airData":50,"airQuality":"优","city":"北京市","date":"2025-03-02","dateLong":1740844800,"date_for_voice":"2号","humidity":"30%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-02 06:46:00","sunSet":"2025-03-02 18:07:00","tempHigh":"9℃","tempLow":"2℃","tempRange":"2℃ ~ 9℃","weather":"多云转晴","weatherDescription":"有点冷。","weatherType":1,"week":"周日","wind":"东北风转东南风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-03","dateLong":1740931200,"date_for_voice":"3号","humidity":"21%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-03 06:45:00","sunSet":"2025-03-03 18:08:00","tempHigh":"7℃","tempLow":"2℃","tempRange":"2℃ ~ 7℃","weather":"多云","weatherDescription":"有点冷。","weatherType":1,"week":"下周一","wind":"南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-04","dateLong":1741017600,"date_for_voice":"4号","humidity":"42%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-04 06:43:00","sunSet":"2025-03-04 18:09:00","tempHigh":"8℃","tempLow":"0℃","tempRange":"0℃ ~ 8℃","weather":"多云","weatherDescription":"有点冷。","weatherType":1,"week":"下周二","wind":"南风转东南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-05","dateLong":1741104000,"date_for_voice":"5号","humidity":"36%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-05 06:42:00","sunSet":"2025-03-05 18:10:00","tempHigh":"9℃","tempLow":"-1℃","tempRange":"-1℃ ~ 9℃","weather":"晴转阴","weatherDescription":"有点冷。","weatherType":0,"week":"下周三","wind":"南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-06","dateLong":1741190400,"date_for_voice":"6号","humidity":"45%","img":"http://cdn9002.iflyos.cn/osweathericon/08.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-06 06:40:00","sunSet":"2025-03-06 18:11:00","tempHigh":"7℃","tempLow":"0℃","tempRange":"0℃ ~ 7℃","weather":"雨转阴","weatherDescription":"有点冷。","weatherType":8,"week":"下周四","wind":"东南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-07","dateLong":1741276800,"date_for_voice":"7号","humidity":"43%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-07 06:38:00","sunSet":"2025-03-07 18:12:00","tempHigh":"7℃","tempLow":"-1℃","tempRange":"-1℃ ~ 7℃","weather":"阴","weatherDescription":"有点冷。","weatherType":2,"week":"下周五","wind":"南风转东南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-08","dateLong":1741363200,"date_for_voice":"8号","humidity":"34%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-08 06:37:00","sunSet":"2025-03-08 18:13:00","tempHigh":"9℃","tempLow":"3℃","tempRange":"3℃ ~ 9℃","weather":"阴","weatherDescription":"有点冷。","weatherType":2,"week":"下周六","wind":"南风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-09","dateLong":1741449600,"date_for_voice":"9号","humidity":"30%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-09 06:35:00","sunSet":"2025-03-09 18:14:00","tempHigh":"10℃","tempLow":"1℃","tempRange":"1℃ ~ 10℃","weather":"阴","weatherDescription":"有点冷。","weatherType":2,"week":"下周日","wind":"西北风转东风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-10","dateLong":1741536000,"date_for_voice":"10号","humidity":"36%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-10 06:34:00","sunSet":"2025-03-10 18:16:00","tempHigh":"4℃","tempLow":"-5℃","tempRange":"-5℃ ~ 4℃","weather":"阴","weatherDescription":"天气寒冷，注意保暖。","weatherType":2,"week":"下下周一","wind":"东南风转西南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-11","dateLong":1741622400,"date_for_voice":"11号","humidity":"35%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-11 06:32:00","sunSet":"2025-03-11 18:17:00","tempHigh":"3℃","tempLow":"-4℃","tempRange":"-4℃ ~ 3℃","weather":"阴转晴","weatherDescription":"天气寒冷，注意保暖。","weatherType":2,"week":"下下周二","wind":"西北风3-4级","windLevel":1}]},"cityName":"北京市"},"dm_intent":"custom","cityName":"北京市","shouldEndSession":true},"nlu":{"input":"北京下午3点会下雨吗","skill":"天气","skillId":"2019042500000544","skillVersion":"56","semantics":{"request":{"intent":"QUERY","slots":[{"name":"datetime","normValue":"{\"datetime\":\"T15:00:00\",\"suggestDatetime\":\"2025-02-25T15:00:00\"}","value":"下午3点"},{"name":"location.city","normValue":"北京市","value":"北京市"},{"name":"location.cityAddr","normValue":"北京","value":"北京"},{"name":"location.type","normValue":"LOC_BASIC","value":"LOC_BASIC"},{"name":"queryType","value":"确认"},{"name":"questionWord","value":"会"},{"name":"subfocus","value":"雨"}]}}},"timestamp":1740450176}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid53f9c08f@dxf4d11b1875803eef00"}
连接正常关闭
1740450176
param:b'{\n            "auth_id": "b87e71a3210646ef9b795084ddcbcee3",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid512e67d1@dx57b51b1875813eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "4f21222892ec4af1a2eff054ec97c1c1","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b4492@dx1953aea21427844532"}
started:
ws start
####################
测试进行: ctm000100d2@hu17be9d9d4b1020c902#48546736.pcm
{"recordId":"ase000d9aa0@hu1953aea2a7204d3882:4f21222892ec4af1a2eff054ec97c1c1","requestId":"ase000d9aa0@hu1953aea2a7204d3882","sessionId":"cid000b4492@dx1953aea21427844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450179}
{"recordId":"gty000b4493@dx1953aea21e87844532:4f21222892ec4af1a2eff054ec97c1c1","requestId":"gty000b4493@dx1953aea21e87844532","sessionId":"cid000b4492@dx1953aea21427844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450181}
{"recordId":"gty000b4493@dx1953aea21e87844532:4f21222892ec4af1a2eff054ec97c1c1","requestId":"gty000b4493@dx1953aea21e87844532","sessionId":"cid000b4492@dx1953aea21427844532","eof":"1","text":"杭州今天下午3点会出太阳吗","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1740450181}
send data finished:1740450181.5850089
{"recordId":"gty000b4493@dx1953aea21e87844532:4f21222892ec4af1a2eff054ec97c1c1","requestId":"gty000b4493@dx1953aea21e87844532","sessionId":"cid000b4492@dx1953aea21427844532","topic":"dm.output","skill":"天气","skillId":"2019042500000544","speakUrl":"","error":{},"dm":{"input":"杭州今天下午3点会出太阳吗","intentId":"QUERY","intentName":"查询天气信息","nlg":"杭州市今天不是晴天，霾，4℃ ~ 9℃","widget":{"webhookResp":{"result":[{"airData":105,"airQuality":"轻微污染","city":"杭州市","date":"2025-02-25","dateLong":1740412800,"date_for_voice":"今天","exp":{"ct":{"expName":"穿衣指数","level":"凉","prompt":"天气偏凉，可以穿上最时尚的那件大衣来凹造型，搭一条围巾时尚指数爆表。"},"dy":{"expName":"钓鱼指数","level":"较适宜","prompt":"气压小幅波动，可能会影响鱼儿的进食。"},"gm":{"expName":"感冒指数","level":"易发","prompt":"感冒容易发生，少去人群密集的场所有利于降低感冒的几率。"},"jt":{"expName":"交通指数","level":"良好","prompt":"天气较好，路面干燥，交通气象条件良好，车辆可以正常行驶。"},"tr":{"expName":"旅游指数","level":"一般","prompt":"天空状况还是比较好的，但温度稍微有点低，且风稍大，会让您感觉些许凉意。外出请注意防风。"},"uv":{"expName":"紫外线指数","level":"弱","prompt":"辐射较弱，请涂擦SPF12-15、PA+护肤品。"},"xc":{"expName":"洗车指数","level":"较适宜","prompt":"较适宜洗车，未来一天无雨，风力较小，擦洗一新的汽车至少能保持一天。"},"yd":{"expName":"运动指数","level":"不适宜","prompt":"空气轻度污染，不宜在户外运动。"}},"extra":"","humidity":"66%","img":"http://cdn9002.iflyos.cn/osweathericon/53.png","lastUpdateTime":"2025-02-25 10:00:08","pm25":"105","precipitation":"0","sunRise":"2025-02-25 06:30:00","sunSet":"2025-02-25 17:55:00","temp":5,"tempHigh":"9℃","tempLow":"4℃","tempRange":"4℃ ~ 9℃","tempReal":"2℃","visibility":"","warning":"","weather":"霾","weatherDescription":"有点冷。","weatherDescription3":"4℃到9℃，明天有雨，风不大，有点凉。","weatherDescription7":"11℃到9℃，明天有雨，1号到3号有雨，风不大，有点凉。","weatherType":53,"week":"周二","wind":"南风微风","windLevel":0},{"airData":84,"airQuality":"良","city":"杭州市","date":"2025-02-24","dateLong":1740326400,"date_for_voice":"昨天","humidity":"54%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-02-24 06:31:00","sunSet":"2025-02-24 17:54:00","tempHigh":"10℃","tempLow":"1℃","tempRange":"1℃ ~ 10℃","weather":"晴转多云","weatherDescription":"有点冷。","weatherType":0,"week":"周一","wind":"东北风转东风微风","windLevel":0},{"airData":95,"airQuality":"良","city":"杭州市","date":"2025-02-26","dateLong":1740499200,"date_for_voice":"明天","humidity":"69%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-02-26 06:29:00","sunSet":"2025-02-26 17:56:00","tempHigh":"13℃","tempLow":"6℃","tempRange":"6℃ ~ 13℃","weather":"多云转小雨","weatherDescription":"有点冷。","weatherType":1,"week":"周三","wind":"东风转东北风微风","windLevel":0},{"airData":85,"airQuality":"良","city":"杭州市","date":"2025-02-27","dateLong":1740585600,"date_for_voice":"后天","humidity":"85%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-02-27 06:28:00","sunSet":"2025-02-27 17:57:00","tempHigh":"20℃","tempLow":"9℃","tempRange":"9℃ ~ 20℃","weather":"多云","weatherDescription":"温度适宜。","weatherType":1,"week":"周四","wind":"东南风转东风微风","windLevel":0},{"airData":70,"airQuality":"良","city":"杭州市","date":"2025-02-28","dateLong":1740672000,"date_for_voice":"28号","humidity":"85%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-02-28 06:26:00","sunSet":"2025-02-28 17:57:00","tempHigh":"22℃","tempLow":"11℃","tempRange":"11℃ ~ 22℃","weather":"多云","weatherDescription":"温度适宜。","weatherType":1,"week":"周五","wind":"东风转东北风微风","windLevel":0},{"airData":60,"airQuality":"良","city":"杭州市","date":"2025-03-01","dateLong":1740758400,"date_for_voice":"1号","humidity":"77%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-01 06:25:00","sunSet":"2025-03-01 17:58:00","tempHigh":"28℃","tempLow":"14℃","tempRange":"14℃ ~ 28℃","weather":"多云转小雨","weatherDescription":"气候温暖。","weatherType":1,"week":"周六","wind":"西南风3-4级","windLevel":1},{"airData":55,"airQuality":"良","city":"杭州市","date":"2025-03-02","dateLong":1740844800,"date_for_voice":"2号","humidity":"82%","img":"http://cdn9002.iflyos.cn/osweathericon/07.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-02 06:24:00","sunSet":"2025-03-02 17:59:00","tempHigh":"28℃","tempLow":"9℃","tempRange":"9℃ ~ 28℃","weather":"小到中雨转中雨","weatherDescription":"气候温暖。","weatherType":7,"week":"周日","wind":"西南风转东北风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"杭州市","date":"2025-03-03","dateLong":1740931200,"date_for_voice":"3号","humidity":"87%","img":"http://cdn9002.iflyos.cn/osweathericon/08.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-03 06:23:00","sunSet":"2025-03-03 17:59:00","tempHigh":"9℃","tempLow":"7℃","tempRange":"7℃ ~ 9℃","weather":"中雨转小雨","weatherDescription":"有点冷。","weatherType":8,"week":"下周一","wind":"东北风转北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"杭州市","date":"2025-03-04","dateLong":1741017600,"date_for_voice":"4号","humidity":"81%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-04 06:22:00","sunSet":"2025-03-04 18:00:00","tempHigh":"10℃","tempLow":"6℃","tempRange":"6℃ ~ 10℃","weather":"多云","weatherDescription":"有点冷。","weatherType":1,"week":"下周二","wind":"西北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"杭州市","date":"2025-03-05","dateLong":1741104000,"date_for_voice":"5号","humidity":"78%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-05 06:21:00","sunSet":"2025-03-05 18:01:00","tempHigh":"13℃","tempLow":"5℃","tempRange":"5℃ ~ 13℃","weather":"阴","weatherDescription":"有点冷。","weatherType":2,"week":"下周三","wind":"东北风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"杭州市","date":"2025-03-06","dateLong":1741190400,"date_for_voice":"6号","humidity":"82%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-06 06:20:00","sunSet":"2025-03-06 18:01:00","tempHigh":"11℃","tempLow":"3℃","tempRange":"3℃ ~ 11℃","weather":"阴","weatherDescription":"有点冷。","weatherType":2,"week":"下周四","wind":"东北风转西北风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"杭州市","date":"2025-03-07","dateLong":1741276800,"date_for_voice":"7号","humidity":"75%","img":"http://cdn9002.iflyos.cn/osweathericon/07.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-07 06:19:00","sunSet":"2025-03-07 18:02:00","tempHigh":"9℃","tempLow":"5℃","tempRange":"5℃ ~ 9℃","weather":"小雨","weatherDescription":"有点冷。","weatherType":7,"week":"下周五","wind":"西北风转东北风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"杭州市","date":"2025-03-08","dateLong":1741363200,"date_for_voice":"8号","humidity":"73%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-08 06:17:00","sunSet":"2025-03-08 18:03:00","tempHigh":"14℃","tempLow":"8℃","tempRange":"8℃ ~ 14℃","weather":"阴转小雨","weatherDescription":"有点凉。","weatherType":2,"week":"下周六","wind":"东北风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"杭州市","date":"2025-03-09","dateLong":1741449600,"date_for_voice":"9号","humidity":"79%","img":"http://cdn9002.iflyos.cn/osweathericon/07.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-09 06:16:00","sunSet":"2025-03-09 18:03:00","tempHigh":"11℃","tempLow":"4℃","tempRange":"4℃ ~ 11℃","weather":"小雨转阴","weatherDescription":"有点冷。","weatherType":7,"week":"下周日","wind":"东北风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"杭州市","date":"2025-03-10","dateLong":1741536000,"date_for_voice":"10号","humidity":"76%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-10 06:15:00","sunSet":"2025-03-10 18:04:00","tempHigh":"11℃","tempLow":"5℃","tempRange":"5℃ ~ 11℃","weather":"阴转小雨","weatherDescription":"有点冷。","weatherType":2,"week":"下下周一","wind":"东北风转东风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"杭州市","date":"2025-03-11","dateLong":1741622400,"date_for_voice":"11号","humidity":"74%","img":"http://cdn9002.iflyos.cn/osweathericon/07.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-11 06:14:00","sunSet":"2025-03-11 18:05:00","tempHigh":"7℃","tempLow":"0℃","tempRange":"0℃ ~ 7℃","weather":"小雨转雨夹雪","weatherDescription":"有点冷。","weatherType":7,"week":"下下周二","wind":"东北风微风","windLevel":0}]},"cityName":"杭州市"},"dm_intent":"custom","cityName":"杭州市","shouldEndSession":true},"nlu":{"input":"杭州今天下午3点会出太阳吗","skill":"天气","skillId":"2019042500000544","skillVersion":"56","semantics":{"request":{"intent":"QUERY","slots":[{"name":"datetime","normValue":"{\"datetime\":\"2025-02-25T15:00:00\",\"suggestDatetime\":\"2025-02-25T15:00:00\"}","value":"今天下午3点"},{"name":"location.city","normValue":"杭州市","value":"杭州市"},{"name":"location.cityAddr","normValue":"杭州","value":"杭州"},{"name":"location.type","normValue":"LOC_BASIC","value":"LOC_BASIC"},{"name":"queryType","value":"确认"},{"name":"subfocus","value":"太阳"}]}}},"timestamp":1740450181}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid1e067742@dx66001b1875853eef00"}
连接正常关闭
1740450181
param:b'{\n            "auth_id": "0b96d12009a34e608c92bdda9bf92b3a",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid726b7312@dxdacf1b1875853eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "a1005ddd39f3467b93dbb01c5dfcccf9","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b4052@dx1953aea33f8b8a9532"}
started:
ws start
####################
测试进行: ctm00010129@hu17be9ebd5a1020c902#48548047.pcm
{"recordId":"ase000ffae4@hu1953aea411d05c2882:a1005ddd39f3467b93dbb01c5dfcccf9","requestId":"ase000ffae4@hu1953aea411d05c2882","sessionId":"cid000b4052@dx1953aea33f8b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450185}
{"recordId":"gty000b4053@dx1953aea3498b8a9532:a1005ddd39f3467b93dbb01c5dfcccf9","requestId":"gty000b4053@dx1953aea3498b8a9532","sessionId":"cid000b4052@dx1953aea33f8b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450185}
{"recordId":"gty000b4053@dx1953aea3498b8a9532:a1005ddd39f3467b93dbb01c5dfcccf9","requestId":"gty000b4053@dx1953aea3498b8a9532","sessionId":"cid000b4052@dx1953aea33f8b8a9532","eof":"1","text":"帮我变成敲击","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"female","genderScore":"","language_class":"mandarin","timestamp":1740450185}
send data finished:1740450185.8562539
{"recordId":"gty000b4053@dx1953aea3498b8a9532:a1005ddd39f3467b93dbb01c5dfcccf9","requestId":"gty000b4053@dx1953aea3498b8a9532","sessionId":"cid000b4052@dx1953aea33f8b8a9532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"帮我变成敲击","intentId":"chat","intentName":"闲聊","nlg":"想破了我的小脑袋都没想出来，你换个问题吧！","shouldEndSession":true},"nlu":{"input":"帮我变成敲击","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1740450186}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidfa2ca179@dx88601b18758a3eef00"}
连接正常关闭
1740450186
param:b'{\n            "auth_id": "71aa063f7e0542d99767fbc74db0b498",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid780b069e@dx3bb61b18758a3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "f444f65216b44c7791a88bfa7f729e01","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3f02@dx1953aea44b0b8aa532"}
started:
ws start
####################
测试进行: ctm000101b0@hu17be9ebdb56020c902#48548068.pcm
{"recordId":"ase000ffdea@hu1953aea50e305c2882:f444f65216b44c7791a88bfa7f729e01","requestId":"ase000ffdea@hu1953aea50e305c2882","sessionId":"cid000b3f02@dx1953aea44b0b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450189}
{"recordId":"gty000b3f03@dx1953aea454fb8aa532:f444f65216b44c7791a88bfa7f729e01","requestId":"gty000b3f03@dx1953aea454fb8aa532","sessionId":"cid000b3f02@dx1953aea44b0b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450190}
{"recordId":"gty000b3f03@dx1953aea454fb8aa532:f444f65216b44c7791a88bfa7f729e01","requestId":"gty000b3f03@dx1953aea454fb8aa532","sessionId":"cid000b3f02@dx1953aea44b0b8aa532","eof":"1","text":"河北今天的气温是多少度","pinyin":"","topic":"asr.speech.result","age":"child","ageScore":"","gender":"","genderScore":"","language_class":"mandarin","timestamp":1740450190}
send data finished:1740450190.841204
{"recordId":"gty000b3f03@dx1953aea454fb8aa532:f444f65216b44c7791a88bfa7f729e01","requestId":"gty000b3f03@dx1953aea454fb8aa532","sessionId":"cid000b3f02@dx1953aea44b0b8aa532","topic":"dm.output","skill":"天气","skillId":"2019042500000544","speakUrl":"","error":{},"dm":{"input":"河北今天的气温是多少度","intentId":"QUERY","intentName":"查询天气信息","nlg":"请问你想查询河北哪个城市的天气？","widget":{"cityName":""},"dm_intent":"custom","cityName":"","shouldEndSession":false},"nlu":{"input":"河北今天的气温是多少度","skill":"天气","skillId":"2019042500000544","skillVersion":"56","semantics":{"request":{"intent":"QUERY","slots":[{"name":"datetime","normValue":"{\"datetime\":\"2025-02-25\",\"suggestDatetime\":\"2025-02-25\"}","value":"今天"},{"name":"location.province","normValue":"河北省","value":"河北省"},{"name":"location.provinceAddr","normValue":"河北","value":"河北"},{"name":"location.type","normValue":"LOC_BASIC","value":"LOC_BASIC"},{"name":"queryType","value":"内容"},{"name":"subfocus","value":"温度"}]}}},"timestamp":1740450190}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid74ab7e63@dx08911b18758e3eef00"}
连接正常关闭
1740450190
param:b'{\n            "auth_id": "56132ddc97b340b49d5a19297375aa4d",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid7428a33c@dx25e71b18758f3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "4dd13866b8c74244bcebdf23f56bc8bb","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b4200@dx1953aea57acb86a532"}
started:
ws start
####################
测试进行: ctm000101b2@hu17be9ebdb77020c902#48548064.pcm
{"recordId":"ase000e658e@hu1953aea61b505c4882:4dd13866b8c74244bcebdf23f56bc8bb","requestId":"ase000e658e@hu1953aea61b505c4882","sessionId":"cid000b4200@dx1953aea57acb86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450194}
{"recordId":"gty000b4201@dx1953aea584bb86a532:4dd13866b8c74244bcebdf23f56bc8bb","requestId":"gty000b4201@dx1953aea584bb86a532","sessionId":"cid000b4200@dx1953aea57acb86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450194}
{"recordId":"gty000b4201@dx1953aea584bb86a532:4dd13866b8c74244bcebdf23f56bc8bb","requestId":"gty000b4201@dx1953aea584bb86a532","sessionId":"cid000b4200@dx1953aea57acb86a532","eof":"1","text":"今天的体感温度","pinyin":"","topic":"asr.speech.result","age":"child","ageScore":"","gender":"","genderScore":"","language_class":"mandarin","timestamp":1740450194}
send data finished:1740450194.7135715
{"recordId":"gty000b4201@dx1953aea584bb86a532:4dd13866b8c74244bcebdf23f56bc8bb","requestId":"gty000b4201@dx1953aea584bb86a532","sessionId":"cid000b4200@dx1953aea57acb86a532","topic":"dm.output","skill":"天气","skillId":"2019042500000544","speakUrl":"","error":{},"dm":{"input":"今天的体感温度","intentId":"QUERY","intentName":"查询天气信息","nlg":"北京市今天-3℃ ~ 10℃，有点冷。","widget":{"webhookResp":{"result":[{"airData":54,"airQuality":"良","city":"北京市","date":"2025-02-25","dateLong":1740412800,"date_for_voice":"今天","exp":{"ct":{"expName":"穿衣指数","level":"凉","prompt":"天气偏凉，可以穿上最时尚的那件大衣来凹造型，搭一条围巾时尚指数爆表。"},"dy":{"expName":"钓鱼指数","level":"较适宜","prompt":"气压小幅波动，可能会影响鱼儿的进食。"},"gm":{"expName":"感冒指数","level":"易发","prompt":"感冒容易发生，少去人群密集的场所有利于降低感冒的几率。"},"jt":{"expName":"交通指数","level":"良好","prompt":"天气较好，路面干燥，交通气象条件良好，车辆可以正常行驶。"},"tr":{"expName":"旅游指数","level":"适宜","prompt":"天气较好，温度适宜，是个好天气哦。这样的天气适宜旅游，您可以尽情地享受大自然的风光。"},"uv":{"expName":"紫外线指数","level":"弱","prompt":"辐射较弱，请涂擦SPF12-15、PA+护肤品。"},"xc":{"expName":"洗车指数","level":"较适宜","prompt":"较适宜洗车，未来一天无雨，风力较小，擦洗一新的汽车至少能保持一天。"},"yd":{"expName":"运动指数","level":"不适宜","prompt":"气温过低，特别容易着凉感冒，较不适宜户外运动，建议室内运动。"}},"extra":"","humidity":"10%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-02-25 10:00:08","pm25":"34","precipitation":"0","sunRise":"2025-02-25 06:53:00","sunSet":"2025-02-25 18:01:00","temp":8,"tempHigh":"10℃","tempLow":"-3℃","tempRange":"-3℃ ~ 10℃","tempReal":"2℃","visibility":"","warning":"大风蓝色预警","weather":"晴","weatherDescription":"有点冷。","weatherDescription3":"-1℃到14℃，风不大，有点冷。北京市气象台发布大风蓝色预警信号。","weatherDescription7":"-1℃到9℃，风不大，有点冷。北京市气象台发布大风蓝色预警信号。","weatherType":0,"week":"周二","wind":"西北风4-5级","windLevel":0},{"airData":42,"airQuality":"优","city":"北京市","date":"2025-02-24","dateLong":1740326400,"date_for_voice":"昨天","humidity":"14%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-02-24 06:55:00","sunSet":"2025-02-24 18:00:00","tempHigh":"9℃","tempLow":"-3℃","tempRange":"-3℃ ~ 9℃","weather":"晴","weatherDescription":"有点冷。","weatherType":0,"week":"周一","wind":"西南风转西北风微风","windLevel":0},{"airData":70,"airQuality":"良","city":"北京市","date":"2025-02-26","dateLong":1740499200,"date_for_voice":"明天","humidity":"18%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-02-26 06:52:00","sunSet":"2025-02-26 18:02:00","tempHigh":"12℃","tempLow":"-1℃","tempRange":"-1℃ ~ 12℃","weather":"晴转多云","weatherDescription":"有点冷。","weatherType":0,"week":"周三","wind":"西南风转北风微风","windLevel":0},{"airData":110,"airQuality":"轻微污染","city":"北京市","date":"2025-02-27","dateLong":1740585600,"date_for_voice":"后天","humidity":"21%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-02-27 06:51:00","sunSet":"2025-02-27 18:04:00","tempHigh":"14℃","tempLow":"1℃","tempRange":"1℃ ~ 14℃","weather":"多云转晴","weatherDescription":"有点凉。","weatherType":1,"week":"周四","wind":"西南风微风","windLevel":0},{"airData":110,"airQuality":"轻微污染","city":"北京市","date":"2025-02-28","dateLong":1740672000,"date_for_voice":"28号","humidity":"16%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-02-28 06:49:00","sunSet":"2025-02-28 18:05:00","tempHigh":"16℃","tempLow":"3℃","tempRange":"3℃ ~ 16℃","weather":"晴转多云","weatherDescription":"有点凉。","weatherType":0,"week":"周五","wind":"东南风转西北风微风","windLevel":0},{"airData":130,"airQuality":"轻微污染","city":"北京市","date":"2025-03-01","dateLong":1740758400,"date_for_voice":"1号","humidity":"44%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-01 06:48:00","sunSet":"2025-03-01 18:06:00","tempHigh":"12℃","tempLow":"3℃","tempRange":"3℃ ~ 12℃","weather":"多云转阴","weatherDescription":"有点冷。","weatherType":1,"week":"周六","wind":"东北风微风","windLevel":0},{"airData":50,"airQuality":"优","city":"北京市","date":"2025-03-02","dateLong":1740844800,"date_for_voice":"2号","humidity":"30%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-02 06:46:00","sunSet":"2025-03-02 18:07:00","tempHigh":"9℃","tempLow":"2℃","tempRange":"2℃ ~ 9℃","weather":"多云转晴","weatherDescription":"有点冷。","weatherType":1,"week":"周日","wind":"东北风转东南风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-03","dateLong":1740931200,"date_for_voice":"3号","humidity":"21%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-03 06:45:00","sunSet":"2025-03-03 18:08:00","tempHigh":"7℃","tempLow":"2℃","tempRange":"2℃ ~ 7℃","weather":"多云","weatherDescription":"有点冷。","weatherType":1,"week":"下周一","wind":"南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-04","dateLong":1741017600,"date_for_voice":"4号","humidity":"42%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-04 06:43:00","sunSet":"2025-03-04 18:09:00","tempHigh":"8℃","tempLow":"0℃","tempRange":"0℃ ~ 8℃","weather":"多云","weatherDescription":"有点冷。","weatherType":1,"week":"下周二","wind":"南风转东南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-05","dateLong":1741104000,"date_for_voice":"5号","humidity":"36%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-05 06:42:00","sunSet":"2025-03-05 18:10:00","tempHigh":"9℃","tempLow":"-1℃","tempRange":"-1℃ ~ 9℃","weather":"晴转阴","weatherDescription":"有点冷。","weatherType":0,"week":"下周三","wind":"南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-06","dateLong":1741190400,"date_for_voice":"6号","humidity":"45%","img":"http://cdn9002.iflyos.cn/osweathericon/08.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-06 06:40:00","sunSet":"2025-03-06 18:11:00","tempHigh":"7℃","tempLow":"0℃","tempRange":"0℃ ~ 7℃","weather":"雨转阴","weatherDescription":"有点冷。","weatherType":8,"week":"下周四","wind":"东南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-07","dateLong":1741276800,"date_for_voice":"7号","humidity":"43%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-07 06:38:00","sunSet":"2025-03-07 18:12:00","tempHigh":"7℃","tempLow":"-1℃","tempRange":"-1℃ ~ 7℃","weather":"阴","weatherDescription":"有点冷。","weatherType":2,"week":"下周五","wind":"南风转东南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-08","dateLong":1741363200,"date_for_voice":"8号","humidity":"34%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-08 06:37:00","sunSet":"2025-03-08 18:13:00","tempHigh":"9℃","tempLow":"3℃","tempRange":"3℃ ~ 9℃","weather":"阴","weatherDescription":"有点冷。","weatherType":2,"week":"下周六","wind":"南风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-09","dateLong":1741449600,"date_for_voice":"9号","humidity":"30%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-09 06:35:00","sunSet":"2025-03-09 18:14:00","tempHigh":"10℃","tempLow":"1℃","tempRange":"1℃ ~ 10℃","weather":"阴","weatherDescription":"有点冷。","weatherType":2,"week":"下周日","wind":"西北风转东风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-10","dateLong":1741536000,"date_for_voice":"10号","humidity":"36%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-10 06:34:00","sunSet":"2025-03-10 18:16:00","tempHigh":"4℃","tempLow":"-5℃","tempRange":"-5℃ ~ 4℃","weather":"阴","weatherDescription":"天气寒冷，注意保暖。","weatherType":2,"week":"下下周一","wind":"东南风转西南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-11","dateLong":1741622400,"date_for_voice":"11号","humidity":"35%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-11 06:32:00","sunSet":"2025-03-11 18:17:00","tempHigh":"3℃","tempLow":"-4℃","tempRange":"-4℃ ~ 3℃","weather":"阴转晴","weatherDescription":"天气寒冷，注意保暖。","weatherType":2,"week":"下下周二","wind":"西北风3-4级","windLevel":1}]},"cityName":"北京市"},"dm_intent":"custom","cityName":"北京市","shouldEndSession":true},"nlu":{"input":"今天的体感温度","skill":"天气","skillId":"2019042500000544","skillVersion":"56","semantics":{"request":{"intent":"QUERY","slots":[{"name":"queryType","value":"内容"},{"name":"subfocus","value":"温度"},{"name":"datetime","normValue":"{\"datetime\":\"2025-02-25\",\"suggestDatetime\":\"2025-02-25\"}","value":"今天"},{"name":"location.city","normValue":"CURRENT_CITY","value":"CURRENT_CITY"},{"name":"location.poi","normValue":"CURRENT_POI","value":"CURRENT_POI"},{"name":"location.type","normValue":"LOC_POI","value":"LOC_POI"}]}}},"timestamp":1740450194}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid6a2a0176@dx57651b1875923eef00"}
连接正常关闭
1740450194
param:b'{\n            "auth_id": "faed6ebefe21457e958b00c94d50772e",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidea3d9507@dx79581b1875933eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "8a24dd000905486f82a52c7795cc7cec","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3f0a@dx1953aea66fdb8aa532"}
started:
ws start
####################
测试进行: ctm000101dd@hu17be9ebdcf7020c902#48548073.pcm
{"recordId":"ase000ff674@hu1953aea6fe405c0882:8a24dd000905486f82a52c7795cc7cec","requestId":"ase000ff674@hu1953aea6fe405c0882","sessionId":"cid000b3f0a@dx1953aea66fdb8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450197}
{"recordId":"gty000b3f0b@dx1953aea67aab8aa532:8a24dd000905486f82a52c7795cc7cec","requestId":"gty000b3f0b@dx1953aea67aab8aa532","sessionId":"cid000b3f0a@dx1953aea66fdb8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450198}
{"recordId":"gty000b3f0b@dx1953aea67aab8aa532:8a24dd000905486f82a52c7795cc7cec","requestId":"gty000b3f0b@dx1953aea67aab8aa532","sessionId":"cid000b3f0a@dx1953aea66fdb8aa532","eof":"1","text":"近期有龙卷风吗","pinyin":"","topic":"asr.speech.result","age":"child","ageScore":"","gender":"","genderScore":"","language_class":"mandarin","timestamp":1740450198}
send data finished:1740450198.1295738
{"recordId":"gty000b3f0b@dx1953aea67aab8aa532:8a24dd000905486f82a52c7795cc7cec","requestId":"gty000b3f0b@dx1953aea67aab8aa532","sessionId":"cid000b3f0a@dx1953aea66fdb8aa532","topic":"dm.output","skill":"天气","skillId":"2019042500000544","speakUrl":"","error":{},"dm":{"input":"近期有龙卷风吗","intentId":"QUERY","intentName":"查询天气信息","nlg":"北京市近期西北风4-5级，风不大","widget":{"webhookResp":{"result":[{"airData":54,"airQuality":"良","city":"北京市","date":"2025-02-25","dateLong":1740412800,"date_for_voice":"今天","exp":{"ct":{"expName":"穿衣指数","level":"凉","prompt":"天气偏凉，可以穿上最时尚的那件大衣来凹造型，搭一条围巾时尚指数爆表。"},"dy":{"expName":"钓鱼指数","level":"较适宜","prompt":"气压小幅波动，可能会影响鱼儿的进食。"},"gm":{"expName":"感冒指数","level":"易发","prompt":"感冒容易发生，少去人群密集的场所有利于降低感冒的几率。"},"jt":{"expName":"交通指数","level":"良好","prompt":"天气较好，路面干燥，交通气象条件良好，车辆可以正常行驶。"},"tr":{"expName":"旅游指数","level":"适宜","prompt":"天气较好，温度适宜，是个好天气哦。这样的天气适宜旅游，您可以尽情地享受大自然的风光。"},"uv":{"expName":"紫外线指数","level":"弱","prompt":"辐射较弱，请涂擦SPF12-15、PA+护肤品。"},"xc":{"expName":"洗车指数","level":"较适宜","prompt":"较适宜洗车，未来一天无雨，风力较小，擦洗一新的汽车至少能保持一天。"},"yd":{"expName":"运动指数","level":"不适宜","prompt":"气温过低，特别容易着凉感冒，较不适宜户外运动，建议室内运动。"}},"extra":"","humidity":"10%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-02-25 10:00:08","pm25":"34","precipitation":"0","sunRise":"2025-02-25 06:53:00","sunSet":"2025-02-25 18:01:00","temp":8,"tempHigh":"10℃","tempLow":"-3℃","tempRange":"-3℃ ~ 10℃","tempReal":"2℃","visibility":"","warning":"大风蓝色预警","weather":"晴","weatherDescription":"有点冷。","weatherDescription3":"-1℃到14℃，风不大，有点冷。北京市气象台发布大风蓝色预警信号。","weatherDescription7":"-1℃到9℃，风不大，有点冷。北京市气象台发布大风蓝色预警信号。","weatherType":0,"week":"周二","wind":"西北风4-5级","windLevel":0},{"airData":42,"airQuality":"优","city":"北京市","date":"2025-02-24","dateLong":1740326400,"date_for_voice":"昨天","humidity":"14%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-02-24 06:55:00","sunSet":"2025-02-24 18:00:00","tempHigh":"9℃","tempLow":"-3℃","tempRange":"-3℃ ~ 9℃","weather":"晴","weatherDescription":"有点冷。","weatherType":0,"week":"周一","wind":"西南风转西北风微风","windLevel":0},{"airData":70,"airQuality":"良","city":"北京市","date":"2025-02-26","dateLong":1740499200,"date_for_voice":"明天","humidity":"18%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-02-26 06:52:00","sunSet":"2025-02-26 18:02:00","tempHigh":"12℃","tempLow":"-1℃","tempRange":"-1℃ ~ 12℃","weather":"晴转多云","weatherDescription":"有点冷。","weatherType":0,"week":"周三","wind":"西南风转北风微风","windLevel":0},{"airData":110,"airQuality":"轻微污染","city":"北京市","date":"2025-02-27","dateLong":1740585600,"date_for_voice":"后天","humidity":"21%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-02-27 06:51:00","sunSet":"2025-02-27 18:04:00","tempHigh":"14℃","tempLow":"1℃","tempRange":"1℃ ~ 14℃","weather":"多云转晴","weatherDescription":"有点凉。","weatherType":1,"week":"周四","wind":"西南风微风","windLevel":0},{"airData":110,"airQuality":"轻微污染","city":"北京市","date":"2025-02-28","dateLong":1740672000,"date_for_voice":"28号","humidity":"16%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-02-28 06:49:00","sunSet":"2025-02-28 18:05:00","tempHigh":"16℃","tempLow":"3℃","tempRange":"3℃ ~ 16℃","weather":"晴转多云","weatherDescription":"有点凉。","weatherType":0,"week":"周五","wind":"东南风转西北风微风","windLevel":0},{"airData":130,"airQuality":"轻微污染","city":"北京市","date":"2025-03-01","dateLong":1740758400,"date_for_voice":"1号","humidity":"44%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-01 06:48:00","sunSet":"2025-03-01 18:06:00","tempHigh":"12℃","tempLow":"3℃","tempRange":"3℃ ~ 12℃","weather":"多云转阴","weatherDescription":"有点冷。","weatherType":1,"week":"周六","wind":"东北风微风","windLevel":0},{"airData":50,"airQuality":"优","city":"北京市","date":"2025-03-02","dateLong":1740844800,"date_for_voice":"2号","humidity":"30%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-02 06:46:00","sunSet":"2025-03-02 18:07:00","tempHigh":"9℃","tempLow":"2℃","tempRange":"2℃ ~ 9℃","weather":"多云转晴","weatherDescription":"有点冷。","weatherType":1,"week":"周日","wind":"东北风转东南风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-03","dateLong":1740931200,"date_for_voice":"3号","humidity":"21%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-03 06:45:00","sunSet":"2025-03-03 18:08:00","tempHigh":"7℃","tempLow":"2℃","tempRange":"2℃ ~ 7℃","weather":"多云","weatherDescription":"有点冷。","weatherType":1,"week":"下周一","wind":"南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-04","dateLong":1741017600,"date_for_voice":"4号","humidity":"42%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-04 06:43:00","sunSet":"2025-03-04 18:09:00","tempHigh":"8℃","tempLow":"0℃","tempRange":"0℃ ~ 8℃","weather":"多云","weatherDescription":"有点冷。","weatherType":1,"week":"下周二","wind":"南风转东南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-05","dateLong":1741104000,"date_for_voice":"5号","humidity":"36%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-05 06:42:00","sunSet":"2025-03-05 18:10:00","tempHigh":"9℃","tempLow":"-1℃","tempRange":"-1℃ ~ 9℃","weather":"晴转阴","weatherDescription":"有点冷。","weatherType":0,"week":"下周三","wind":"南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-06","dateLong":1741190400,"date_for_voice":"6号","humidity":"45%","img":"http://cdn9002.iflyos.cn/osweathericon/08.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-06 06:40:00","sunSet":"2025-03-06 18:11:00","tempHigh":"7℃","tempLow":"0℃","tempRange":"0℃ ~ 7℃","weather":"雨转阴","weatherDescription":"有点冷。","weatherType":8,"week":"下周四","wind":"东南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-07","dateLong":1741276800,"date_for_voice":"7号","humidity":"43%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-07 06:38:00","sunSet":"2025-03-07 18:12:00","tempHigh":"7℃","tempLow":"-1℃","tempRange":"-1℃ ~ 7℃","weather":"阴","weatherDescription":"有点冷。","weatherType":2,"week":"下周五","wind":"南风转东南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-08","dateLong":1741363200,"date_for_voice":"8号","humidity":"34%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-08 06:37:00","sunSet":"2025-03-08 18:13:00","tempHigh":"9℃","tempLow":"3℃","tempRange":"3℃ ~ 9℃","weather":"阴","weatherDescription":"有点冷。","weatherType":2,"week":"下周六","wind":"南风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-09","dateLong":1741449600,"date_for_voice":"9号","humidity":"30%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-09 06:35:00","sunSet":"2025-03-09 18:14:00","tempHigh":"10℃","tempLow":"1℃","tempRange":"1℃ ~ 10℃","weather":"阴","weatherDescription":"有点冷。","weatherType":2,"week":"下周日","wind":"西北风转东风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-10","dateLong":1741536000,"date_for_voice":"10号","humidity":"36%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-10 06:34:00","sunSet":"2025-03-10 18:16:00","tempHigh":"4℃","tempLow":"-5℃","tempRange":"-5℃ ~ 4℃","weather":"阴","weatherDescription":"天气寒冷，注意保暖。","weatherType":2,"week":"下下周一","wind":"东南风转西南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-11","dateLong":1741622400,"date_for_voice":"11号","humidity":"35%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-11 06:32:00","sunSet":"2025-03-11 18:17:00","tempHigh":"3℃","tempLow":"-4℃","tempRange":"-4℃ ~ 3℃","weather":"阴转晴","weatherDescription":"天气寒冷，注意保暖。","weatherType":2,"week":"下下周二","wind":"西北风3-4级","windLevel":1}]},"cityName":"北京市"},"dm_intent":"custom","cityName":"北京市","shouldEndSession":true},"nlu":{"input":"近期有龙卷风吗","skill":"天气","skillId":"2019042500000544","skillVersion":"56","semantics":{"request":{"intent":"QUERY","slots":[{"name":"datetime","normValue":"{\"datetime\":\"2025-02-15/2025-03-07\",\"suggestDatetime\":\"2025-02-15/2025-03-07\"}","value":"近期"},{"name":"location.poi","normValue":"CURRENT_POI","value":"CURRENT_POI"},{"name":"location.type","normValue":"LOC_POI","value":"LOC_POI"},{"name":"location.city","normValue":"CURRENT_CITY","value":"CURRENT_CITY"},{"name":"queryType","value":"确认"},{"name":"subfocus","value":"风"}]}}},"timestamp":1740450198}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid9bd29f6a@dx20561b1875963eef00"}
连接正常关闭
1740450198
param:b'{\n            "auth_id": "85ef0c5b326543758b8c1ea3dd93a42f",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid1cb50a98@dx5da61b1875963eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "b830ea938a8c4551978e338c733e4eec","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b4204@dx1953aea7438b86a532"}
started:
ws start
####################
测试进行: ctm000101df@hu17be9ebdd08020c902#48548072.pcm
{"recordId":"gty000b4205@dx1953aea74eeb86a532:b830ea938a8c4551978e338c733e4eec","requestId":"gty000b4205@dx1953aea74eeb86a532","sessionId":"cid000b4204@dx1953aea7438b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450201}
{"recordId":"gty000b4205@dx1953aea74eeb86a532:b830ea938a8c4551978e338c733e4eec","requestId":"gty000b4205@dx1953aea74eeb86a532","sessionId":"cid000b4204@dx1953aea7438b86a532","eof":"1","text":"今天有雨吗","pinyin":"","topic":"asr.speech.result","age":"child","ageScore":"","gender":"","genderScore":"","language_class":"mandarin","timestamp":1740450201}
send data finished:1740450201.179651
{"recordId":"gty000b4205@dx1953aea74eeb86a532:b830ea938a8c4551978e338c733e4eec","requestId":"gty000b4205@dx1953aea74eeb86a532","sessionId":"cid000b4204@dx1953aea7438b86a532","topic":"dm.output","skill":"天气","skillId":"2019042500000544","speakUrl":"","error":{},"dm":{"input":"今天有雨吗","intentId":"QUERY","intentName":"查询天气信息","nlg":"北京市今天晴，不下雨","widget":{"webhookResp":{"result":[{"airData":54,"airQuality":"良","city":"北京市","date":"2025-02-25","dateLong":1740412800,"date_for_voice":"今天","exp":{"ct":{"expName":"穿衣指数","level":"凉","prompt":"天气偏凉，可以穿上最时尚的那件大衣来凹造型，搭一条围巾时尚指数爆表。"},"dy":{"expName":"钓鱼指数","level":"较适宜","prompt":"气压小幅波动，可能会影响鱼儿的进食。"},"gm":{"expName":"感冒指数","level":"易发","prompt":"感冒容易发生，少去人群密集的场所有利于降低感冒的几率。"},"jt":{"expName":"交通指数","level":"良好","prompt":"天气较好，路面干燥，交通气象条件良好，车辆可以正常行驶。"},"tr":{"expName":"旅游指数","level":"适宜","prompt":"天气较好，温度适宜，是个好天气哦。这样的天气适宜旅游，您可以尽情地享受大自然的风光。"},"uv":{"expName":"紫外线指数","level":"弱","prompt":"辐射较弱，请涂擦SPF12-15、PA+护肤品。"},"xc":{"expName":"洗车指数","level":"较适宜","prompt":"较适宜洗车，未来一天无雨，风力较小，擦洗一新的汽车至少能保持一天。"},"yd":{"expName":"运动指数","level":"不适宜","prompt":"气温过低，特别容易着凉感冒，较不适宜户外运动，建议室内运动。"}},"extra":"","humidity":"10%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-02-25 10:00:08","pm25":"34","precipitation":"0","sunRise":"2025-02-25 06:53:00","sunSet":"2025-02-25 18:01:00","temp":8,"tempHigh":"10℃","tempLow":"-3℃","tempRange":"-3℃ ~ 10℃","tempReal":"2℃","visibility":"","warning":"大风蓝色预警","weather":"晴","weatherDescription":"有点冷。","weatherDescription3":"-1℃到14℃，风不大，有点冷。北京市气象台发布大风蓝色预警信号。","weatherDescription7":"-1℃到9℃，风不大，有点冷。北京市气象台发布大风蓝色预警信号。","weatherType":0,"week":"周二","wind":"西北风4-5级","windLevel":0},{"airData":42,"airQuality":"优","city":"北京市","date":"2025-02-24","dateLong":1740326400,"date_for_voice":"昨天","humidity":"14%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-02-24 06:55:00","sunSet":"2025-02-24 18:00:00","tempHigh":"9℃","tempLow":"-3℃","tempRange":"-3℃ ~ 9℃","weather":"晴","weatherDescription":"有点冷。","weatherType":0,"week":"周一","wind":"西南风转西北风微风","windLevel":0},{"airData":70,"airQuality":"良","city":"北京市","date":"2025-02-26","dateLong":1740499200,"date_for_voice":"明天","humidity":"18%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-02-26 06:52:00","sunSet":"2025-02-26 18:02:00","tempHigh":"12℃","tempLow":"-1℃","tempRange":"-1℃ ~ 12℃","weather":"晴转多云","weatherDescription":"有点冷。","weatherType":0,"week":"周三","wind":"西南风转北风微风","windLevel":0},{"airData":110,"airQuality":"轻微污染","city":"北京市","date":"2025-02-27","dateLong":1740585600,"date_for_voice":"后天","humidity":"21%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-02-27 06:51:00","sunSet":"2025-02-27 18:04:00","tempHigh":"14℃","tempLow":"1℃","tempRange":"1℃ ~ 14℃","weather":"多云转晴","weatherDescription":"有点凉。","weatherType":1,"week":"周四","wind":"西南风微风","windLevel":0},{"airData":110,"airQuality":"轻微污染","city":"北京市","date":"2025-02-28","dateLong":1740672000,"date_for_voice":"28号","humidity":"16%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-02-28 06:49:00","sunSet":"2025-02-28 18:05:00","tempHigh":"16℃","tempLow":"3℃","tempRange":"3℃ ~ 16℃","weather":"晴转多云","weatherDescription":"有点凉。","weatherType":0,"week":"周五","wind":"东南风转西北风微风","windLevel":0},{"airData":130,"airQuality":"轻微污染","city":"北京市","date":"2025-03-01","dateLong":1740758400,"date_for_voice":"1号","humidity":"44%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-01 06:48:00","sunSet":"2025-03-01 18:06:00","tempHigh":"12℃","tempLow":"3℃","tempRange":"3℃ ~ 12℃","weather":"多云转阴","weatherDescription":"有点冷。","weatherType":1,"week":"周六","wind":"东北风微风","windLevel":0},{"airData":50,"airQuality":"优","city":"北京市","date":"2025-03-02","dateLong":1740844800,"date_for_voice":"2号","humidity":"30%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-02 06:46:00","sunSet":"2025-03-02 18:07:00","tempHigh":"9℃","tempLow":"2℃","tempRange":"2℃ ~ 9℃","weather":"多云转晴","weatherDescription":"有点冷。","weatherType":1,"week":"周日","wind":"东北风转东南风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-03","dateLong":1740931200,"date_for_voice":"3号","humidity":"21%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-03 06:45:00","sunSet":"2025-03-03 18:08:00","tempHigh":"7℃","tempLow":"2℃","tempRange":"2℃ ~ 7℃","weather":"多云","weatherDescription":"有点冷。","weatherType":1,"week":"下周一","wind":"南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-04","dateLong":1741017600,"date_for_voice":"4号","humidity":"42%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-04 06:43:00","sunSet":"2025-03-04 18:09:00","tempHigh":"8℃","tempLow":"0℃","tempRange":"0℃ ~ 8℃","weather":"多云","weatherDescription":"有点冷。","weatherType":1,"week":"下周二","wind":"南风转东南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-05","dateLong":1741104000,"date_for_voice":"5号","humidity":"36%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-05 06:42:00","sunSet":"2025-03-05 18:10:00","tempHigh":"9℃","tempLow":"-1℃","tempRange":"-1℃ ~ 9℃","weather":"晴转阴","weatherDescription":"有点冷。","weatherType":0,"week":"下周三","wind":"南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-06","dateLong":1741190400,"date_for_voice":"6号","humidity":"45%","img":"http://cdn9002.iflyos.cn/osweathericon/08.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-06 06:40:00","sunSet":"2025-03-06 18:11:00","tempHigh":"7℃","tempLow":"0℃","tempRange":"0℃ ~ 7℃","weather":"雨转阴","weatherDescription":"有点冷。","weatherType":8,"week":"下周四","wind":"东南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-07","dateLong":1741276800,"date_for_voice":"7号","humidity":"43%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-07 06:38:00","sunSet":"2025-03-07 18:12:00","tempHigh":"7℃","tempLow":"-1℃","tempRange":"-1℃ ~ 7℃","weather":"阴","weatherDescription":"有点冷。","weatherType":2,"week":"下周五","wind":"南风转东南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-08","dateLong":1741363200,"date_for_voice":"8号","humidity":"34%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-08 06:37:00","sunSet":"2025-03-08 18:13:00","tempHigh":"9℃","tempLow":"3℃","tempRange":"3℃ ~ 9℃","weather":"阴","weatherDescription":"有点冷。","weatherType":2,"week":"下周六","wind":"南风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-09","dateLong":1741449600,"date_for_voice":"9号","humidity":"30%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-09 06:35:00","sunSet":"2025-03-09 18:14:00","tempHigh":"10℃","tempLow":"1℃","tempRange":"1℃ ~ 10℃","weather":"阴","weatherDescription":"有点冷。","weatherType":2,"week":"下周日","wind":"西北风转东风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-10","dateLong":1741536000,"date_for_voice":"10号","humidity":"36%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-10 06:34:00","sunSet":"2025-03-10 18:16:00","tempHigh":"4℃","tempLow":"-5℃","tempRange":"-5℃ ~ 4℃","weather":"阴","weatherDescription":"天气寒冷，注意保暖。","weatherType":2,"week":"下下周一","wind":"东南风转西南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"北京市","date":"2025-03-11","dateLong":1741622400,"date_for_voice":"11号","humidity":"35%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-11 06:32:00","sunSet":"2025-03-11 18:17:00","tempHigh":"3℃","tempLow":"-4℃","tempRange":"-4℃ ~ 3℃","weather":"阴转晴","weatherDescription":"天气寒冷，注意保暖。","weatherType":2,"week":"下下周二","wind":"西北风3-4级","windLevel":1}]},"cityName":"北京市"},"dm_intent":"custom","cityName":"北京市","shouldEndSession":true},"nlu":{"input":"今天有雨吗","skill":"天气","skillId":"2019042500000544","skillVersion":"56","semantics":{"request":{"intent":"QUERY","slots":[{"name":"queryType","value":"确认"},{"name":"questionWord","value":"有"},{"name":"subfocus","value":"雨"},{"name":"datetime","normValue":"{\"datetime\":\"2025-02-25\",\"suggestDatetime\":\"2025-02-25\"}","value":"今天"},{"name":"location.city","normValue":"CURRENT_CITY","value":"CURRENT_CITY"},{"name":"location.poi","normValue":"CURRENT_POI","value":"CURRENT_POI"},{"name":"location.type","normValue":"LOC_POI","value":"LOC_POI"}]}}},"timestamp":1740450201}
{"recordId":"ase000daad5@hu1953aea7e6004d3882:b830ea938a8c4551978e338c733e4eec","requestId":"ase000daad5@hu1953aea7e6004d3882","sessionId":"cid000b4204@dx1953aea7438b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450201}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid0b093bcd@dxd1b31b1875993eef00"}
连接正常关闭
1740450201
param:b'{\n            "auth_id": "4ee2145949c14cba8eddd902f4787e61",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid50ed2a60@dxe5eb1b1875993eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "0ec78db22554496b8d35979e645683e6","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b4062@dx1953aea802db8a9532"}
started:
ws start
####################
测试进行: ctm00010317@hu17be9ebe959020c902#48548103.pcm
{"recordId":"ase000d1f28@hu1953aea894b0427882:0ec78db22554496b8d35979e645683e6","requestId":"ase000d1f28@hu1953aea894b0427882","sessionId":"cid000b4062@dx1953aea802db8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450204}
{"recordId":"gty000b4063@dx1953aea80e8b8a9532:0ec78db22554496b8d35979e645683e6","requestId":"gty000b4063@dx1953aea80e8b8a9532","sessionId":"cid000b4062@dx1953aea802db8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450205}
{"recordId":"gty000b4063@dx1953aea80e8b8a9532:0ec78db22554496b8d35979e645683e6","requestId":"gty000b4063@dx1953aea80e8b8a9532","sessionId":"cid000b4062@dx1953aea802db8a9532","eof":"1","text":"卷云的形状是什么样子的","pinyin":"","topic":"asr.speech.result","age":"child","ageScore":"","gender":"","genderScore":"","language_class":"mandarin","timestamp":1740450205}
send data finished:1740450205.2355857
{"recordId":"gty000b4063@dx1953aea80e8b8a9532:0ec78db22554496b8d35979e645683e6","requestId":"gty000b4063@dx1953aea80e8b8a9532","sessionId":"cid000b4062@dx1953aea802db8a9532","topic":"dm.output","skill":"词典","skillId":"wordsDictionary","speakUrl":"","error":{},"dm":{"input":"卷云的形状是什么样子的","intentId":"MEANING_QUERY","intentName":"查询词语解释","nlg":"我知道卷云哦，它的解释是：1.卷状的云。2.一种白色透光，带有柔丝光泽的个体分散的云。一般出现于五千米以上高空。","shouldEndSession":true},"nlu":{"input":"卷云的形状是什么样子的","skill":"词典","skillId":"wordsDictionary","skillVersion":"196.0","semantics":{"request":{"slots":[{"name":"name","value":"卷云"}]}}},"timestamp":1740450205}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid9ed859a3@dxe1a71b18759d3eef00"}
连接正常关闭
1740450205
param:b'{\n            "auth_id": "55096991797e4417aecededda3ea755a",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid60df249f@dx2bc21b18759d3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "05b0b80833c34398a1049226b42b1d73","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b44a7@dx1953aea90227844532"}
started:
ws start
####################
测试进行: ctm0001052b@hu17be9ebfeba020c902#48548163.pcm
{"recordId":"ase000d226d@hu1953aea9b1c0427882:05b0b80833c34398a1049226b42b1d73","requestId":"ase000d226d@hu1953aea9b1c0427882","sessionId":"cid000b44a7@dx1953aea90227844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450208}
{"recordId":"gty000b44a8@dx1953aea90a97844532:05b0b80833c34398a1049226b42b1d73","requestId":"gty000b44a8@dx1953aea90a97844532","sessionId":"cid000b44a7@dx1953aea90227844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450209}
{"recordId":"gty000b44a8@dx1953aea90a97844532:05b0b80833c34398a1049226b42b1d73","requestId":"gty000b44a8@dx1953aea90a97844532","sessionId":"cid000b44a7@dx1953aea90227844532","eof":"1","text":"内蒙古暴风雪警报","pinyin":"","topic":"asr.speech.result","age":"child","ageScore":"","gender":"","genderScore":"","language_class":"mandarin","timestamp":1740450209}
send data finished:1740450209.693078
{"recordId":"gty000b44a8@dx1953aea90a97844532:05b0b80833c34398a1049226b42b1d73","requestId":"gty000b44a8@dx1953aea90a97844532","sessionId":"cid000b44a7@dx1953aea90227844532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"内蒙古暴风雪警报","intentId":"chat","intentName":"闲聊","nlg":"这个我真不懂，换个我懂的呗。","shouldEndSession":true},"nlu":{"input":"内蒙古暴风雪警报","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1740450209}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid52088525@dx0ff41b1875a13eef00"}
连接正常关闭
1740450209
param:b'{\n            "auth_id": "4f081184d8ac4e688d4e0fc0487253c9",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidbaefcf52@dx44771b1875a23eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "a4c8ff81954243adadf46a37de1c9cb4","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b406c@dx1953aeaa1bdb8a9532"}
started:
ws start
####################
测试进行: ctm000105f7@hu17be9f5ce580212902#48549848.pcm
{"recordId":"ase000d259d@hu1953aeaabdc0427882:a4c8ff81954243adadf46a37de1c9cb4","requestId":"ase000d259d@hu1953aeaabdc0427882","sessionId":"cid000b406c@dx1953aeaa1bdb8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450212}
{"recordId":"gty000b406d@dx1953aeaa26eb8a9532:a4c8ff81954243adadf46a37de1c9cb4","requestId":"gty000b406d@dx1953aeaa26eb8a9532","sessionId":"cid000b406c@dx1953aeaa1bdb8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450214}
{"recordId":"gty000b406d@dx1953aeaa26eb8a9532:a4c8ff81954243adadf46a37de1c9cb4","requestId":"gty000b406d@dx1953aeaa26eb8a9532","sessionId":"cid000b406c@dx1953aeaa1bdb8a9532","eof":"1","text":"表姐的老公的姐姐叫什么","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1740450214}
send data finished:1740450214.295168
{"recordId":"gty000b406d@dx1953aeaa26eb8a9532:a4c8ff81954243adadf46a37de1c9cb4","requestId":"gty000b406d@dx1953aeaa26eb8a9532","sessionId":"cid000b406c@dx1953aeaa1bdb8a9532","topic":"dm.output","skill":"LEIQIAO.relationShip","skillId":"LEIQIAO.relationShip","speakUrl":"","error":{},"dm":{"input":"表姐的老公的姐姐叫什么","intentId":"CALL_ELSE","intentName":"查询","nlg":"关系有些复杂，等我了解后再告诉你吧","shouldEndSession":true},"nlu":{"input":"表姐的老公的姐姐叫什么","skill":"LEIQIAO.relationShip","skillId":"LEIQIAO.relationShip","skillVersion":"74.0","semantics":{"request":{"entrypoint":"ent","hazard":false,"score":1,"slots":[{"begin":0,"end":2,"name":"call","normValue":"姑表姐","value":"表姐"},{"begin":3,"end":5,"name":"call","normValue":"老公","value":"老公"},{"begin":6,"end":8,"name":"call","normValue":"姐姐","value":"姐姐"}],"template":"{call}的{call}的{call}叫什么"}}},"timestamp":1740450214}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidfc0fc3dc@dxf46e1b1875a63eef00"}
连接正常关闭
1740450214
param:b'{\n            "auth_id": "5f695645318d44cb826fc16128bcab02",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid24ff2c73@dx82541b1875a63eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "e995d501067f4ddaaf933fdc171467b4","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b4215@dx1953aeab337b86a532"}
started:
ws start
####################
测试进行: ctm000105fa@hu17be9f5ce840212902#48549847.pcm
{"recordId":"ase000e9080@hu1953aeabda305bf882:e995d501067f4ddaaf933fdc171467b4","requestId":"ase000e9080@hu1953aeabda305bf882","sessionId":"cid000b4215@dx1953aeab337b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450217}
{"recordId":"gty000b4216@dx1953aeab3eab86a532:e995d501067f4ddaaf933fdc171467b4","requestId":"gty000b4216@dx1953aeab3eab86a532","sessionId":"cid000b4215@dx1953aeab337b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450218}
{"recordId":"gty000b4216@dx1953aeab3eab86a532:e995d501067f4ddaaf933fdc171467b4","requestId":"gty000b4216@dx1953aeab3eab86a532","sessionId":"cid000b4215@dx1953aeab337b86a532","eof":"1","text":"舅舅的儿子叫什么","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1740450218}
send data finished:1740450218.1772177
{"recordId":"gty000b4216@dx1953aeab3eab86a532:e995d501067f4ddaaf933fdc171467b4","requestId":"gty000b4216@dx1953aeab3eab86a532","sessionId":"cid000b4215@dx1953aeab337b86a532","topic":"dm.output","skill":"LEIQIAO.relationShip","skillId":"LEIQIAO.relationShip","speakUrl":"","error":{},"dm":{"input":"舅舅的儿子叫什么","intentId":"CALL_ELSE","intentName":"查询","nlg":"舅舅的儿子是你的舅表哥,舅表弟","shouldEndSession":true},"nlu":{"input":"舅舅的儿子叫什么","skill":"LEIQIAO.relationShip","skillId":"LEIQIAO.relationShip","skillVersion":"74.0","semantics":{"request":{"entrypoint":"ent","hazard":false,"score":1,"slots":[{"begin":0,"end":2,"name":"call","normValue":"舅舅","value":"舅舅"},{"begin":3,"end":5,"name":"call","normValue":"儿子","value":"儿子"}],"template":"{call}的{call}叫什么"}}},"timestamp":1740450218}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid1b30d52a@dx193f1b1875aa3eef00"}
连接正常关闭
1740450218
param:b'{\n            "auth_id": "1e5e674d43b541e281bab050181e0284",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidfa720704@dx69c21b1875aa3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "49d01d02d2c14db09678acfa73e85a92","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3f1c@dx1953aeac270b8aa532"}
started:
ws start
####################
测试进行: ctm0001061f@hu17be9ec0885020c902#48548185.pcm
{"recordId":"ase000e6987@hu1953aeacb8705c3882:49d01d02d2c14db09678acfa73e85a92","requestId":"ase000e6987@hu1953aeacb8705c3882","sessionId":"cid000b3f1c@dx1953aeac270b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450221}
{"recordId":"gty000b3f1d@dx1953aeac326b8aa532:49d01d02d2c14db09678acfa73e85a92","requestId":"gty000b3f1d@dx1953aeac326b8aa532","sessionId":"cid000b3f1c@dx1953aeac270b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450222}
{"recordId":"gty000b3f1d@dx1953aeac326b8aa532:49d01d02d2c14db09678acfa73e85a92","requestId":"gty000b3f1d@dx1953aeac326b8aa532","sessionId":"cid000b3f1c@dx1953aeac270b8aa532","eof":"1","text":"长沙今天会不会下雨呀","pinyin":"","topic":"asr.speech.result","age":"child","ageScore":"","gender":"","genderScore":"","language_class":"mandarin","timestamp":1740450222}
send data finished:1740450222.1951635
{"recordId":"gty000b3f1d@dx1953aeac326b8aa532:49d01d02d2c14db09678acfa73e85a92","requestId":"gty000b3f1d@dx1953aeac326b8aa532","sessionId":"cid000b3f1c@dx1953aeac270b8aa532","topic":"dm.output","skill":"天气","skillId":"2019042500000544","speakUrl":"","error":{},"dm":{"input":"长沙今天会不会下雨呀","intentId":"QUERY","intentName":"查询天气信息","nlg":"不下雨，今天长沙市阴，适合在家听音乐看书哦","widget":{"webhookResp":{"result":[{"airData":85,"airQuality":"良","city":"长沙市","date":"2025-02-25","dateLong":1740412800,"date_for_voice":"今天","exp":{"ct":{"expName":"穿衣指数","level":"凉","prompt":"天气偏凉，可以穿上最时尚的那件大衣来凹造型，搭一条围巾时尚指数爆表。"},"dy":{"expName":"钓鱼指数","level":"不适宜","prompt":"雾不散，鱼儿不愿进食，不适宜外出钓鱼。"},"gm":{"expName":"感冒指数","level":"极易发","prompt":"感冒极易发生，避免去人群密集的场所，勤洗手勤通风有利于降低感冒几率。"},"jt":{"expName":"交通指数","level":"良好","prompt":"天气较好，路面干燥，交通气象条件良好，车辆可以正常行驶。"},"tr":{"expName":"旅游指数","level":"适宜","prompt":"天气较好，但丝毫不会影响您出行的心情。温度适宜又有微风相伴，适宜旅游。"},"uv":{"expName":"紫外线指数","level":"最弱","prompt":"辐射弱，请涂擦SPF8-12防晒护肤品。"},"xc":{"expName":"洗车指数","level":"较适宜","prompt":"较适宜洗车，未来一天无雨，风力较小，擦洗一新的汽车至少能保持一天。"},"yd":{"expName":"运动指数","level":"不适宜","prompt":"受到雾天气的影响，不宜在户外运动。"}},"extra":"","humidity":"91%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","pm25":"85","precipitation":"0","sunRise":"2025-02-25 06:57:00","sunSet":"2025-02-25 18:25:00","temp":5,"tempHigh":"11℃","tempLow":"5℃","tempRange":"5℃ ~ 11℃","tempReal":"2℃","visibility":"","warning":"大雾黄色预警","weather":"阴","weatherDescription":"有点冷。","weatherDescription3":"16℃到21℃，明天有雨，风不大，有点凉。湖南省气象台发布大雾黄色预警。","weatherDescription7":"16℃到27℃，明天有雨，2号、3号有雨，风不大，温度适宜。湖南省气象台发布大雾黄色预警。","weatherType":2,"week":"周二","wind":"西风微风","windLevel":0},{"airData":96,"airQuality":"良","city":"长沙市","date":"2025-02-24","dateLong":1740326400,"date_for_voice":"昨天","humidity":"64%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-02-24 06:58:00","sunSet":"2025-02-24 18:25:00","tempHigh":"13℃","tempLow":"8℃","tempRange":"8℃ ~ 13℃","weather":"多云转小雨","weatherDescription":"有点冷。","weatherType":1,"week":"周一","wind":"西北风微风","windLevel":0},{"airData":110,"airQuality":"轻微污染","city":"长沙市","date":"2025-02-26","dateLong":1740499200,"date_for_voice":"明天","humidity":"83%","img":"http://cdn9002.iflyos.cn/osweathericon/07.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-02-26 06:56:00","sunSet":"2025-02-26 18:26:00","tempHigh":"14℃","tempLow":"9℃","tempRange":"9℃ ~ 14℃","weather":"小雨","weatherDescription":"有点凉。","weatherType":7,"week":"周三","wind":"东南风微风","windLevel":0},{"airData":80,"airQuality":"良","city":"长沙市","date":"2025-02-27","dateLong":1740585600,"date_for_voice":"后天","humidity":"83%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-02-27 06:55:00","sunSet":"2025-02-27 18:27:00","tempHigh":"21℃","tempLow":"16℃","tempRange":"16℃ ~ 21℃","weather":"多云","weatherDescription":"温度适宜。","weatherType":1,"week":"周四","wind":"南风转东南风3-4级","windLevel":1},{"airData":70,"airQuality":"良","city":"长沙市","date":"2025-02-28","dateLong":1740672000,"date_for_voice":"28号","humidity":"78%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-02-28 06:54:00","sunSet":"2025-02-28 18:27:00","tempHigh":"25℃","tempLow":"17℃","tempRange":"17℃ ~ 25℃","weather":"晴","weatherDescription":"气候温暖。","weatherType":0,"week":"周五","wind":"南风转东南风3-4级","windLevel":1},{"airData":70,"airQuality":"良","city":"长沙市","date":"2025-03-01","dateLong":1740758400,"date_for_voice":"1号","humidity":"75%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-01 06:53:00","sunSet":"2025-03-01 18:28:00","tempHigh":"27℃","tempLow":"17℃","tempRange":"17℃ ~ 27℃","weather":"晴转多云","weatherDescription":"气候温暖。","weatherType":0,"week":"周六","wind":"西南风转南风3-4级","windLevel":1},{"airData":60,"airQuality":"良","city":"长沙市","date":"2025-03-02","dateLong":1740844800,"date_for_voice":"2号","humidity":"83%","img":"http://cdn9002.iflyos.cn/osweathericon/08.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-02 06:52:00","sunSet":"2025-03-02 18:29:00","tempHigh":"23℃","tempLow":"3℃","tempRange":"3℃ ~ 23℃","weather":"中雨","weatherDescription":"温度适宜。","weatherType":8,"week":"周日","wind":"西北风4-5级","windLevel":2},{"airData":-1,"airQuality":"未知","city":"长沙市","date":"2025-03-03","dateLong":1740931200,"date_for_voice":"3号","humidity":"85%","img":"http://cdn9002.iflyos.cn/osweathericon/07.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-03 06:51:00","sunSet":"2025-03-03 18:29:00","tempHigh":"13℃","tempLow":"3℃","tempRange":"3℃ ~ 13℃","weather":"小雨转多云","weatherDescription":"有点冷。","weatherType":7,"week":"下周一","wind":"西北风4-5级","windLevel":2},{"airData":-1,"airQuality":"未知","city":"长沙市","date":"2025-03-04","dateLong":1741017600,"date_for_voice":"4号","humidity":"77%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-04 06:50:00","sunSet":"2025-03-04 18:30:00","tempHigh":"10℃","tempLow":"4℃","tempRange":"4℃ ~ 10℃","weather":"晴转阴","weatherDescription":"有点冷。","weatherType":0,"week":"下周二","wind":"西北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"长沙市","date":"2025-03-05","dateLong":1741104000,"date_for_voice":"5号","humidity":"84%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-05 06:49:00","sunSet":"2025-03-05 18:31:00","tempHigh":"14℃","tempLow":"6℃","tempRange":"6℃ ~ 14℃","weather":"阴转小雨","weatherDescription":"有点凉。","weatherType":2,"week":"下周三","wind":"北风转西北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"长沙市","date":"2025-03-06","dateLong":1741190400,"date_for_voice":"6号","humidity":"90%","img":"http://cdn9002.iflyos.cn/osweathericon/07.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-06 06:48:00","sunSet":"2025-03-06 18:31:00","tempHigh":"12℃","tempLow":"5℃","tempRange":"5℃ ~ 12℃","weather":"小雨","weatherDescription":"有点冷。","weatherType":7,"week":"下周四","wind":"西北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"长沙市","date":"2025-03-07","dateLong":1741276800,"date_for_voice":"7号","humidity":"83%","img":"http://cdn9002.iflyos.cn/osweathericon/07.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-07 06:47:00","sunSet":"2025-03-07 18:32:00","tempHigh":"5℃","tempLow":"3℃","tempRange":"3℃ ~ 5℃","weather":"小雨","weatherDescription":"天气寒冷，注意保暖。","weatherType":7,"week":"下周五","wind":"西北风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"长沙市","date":"2025-03-08","dateLong":1741363200,"date_for_voice":"8号","humidity":"84%","img":"http://cdn9002.iflyos.cn/osweathericon/07.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-08 06:45:00","sunSet":"2025-03-08 18:32:00","tempHigh":"6℃","tempLow":"3℃","tempRange":"3℃ ~ 6℃","weather":"小雨","weatherDescription":"天气寒冷，注意保暖。","weatherType":7,"week":"下周六","wind":"西北风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"长沙市","date":"2025-03-09","dateLong":1741449600,"date_for_voice":"9号","humidity":"88%","img":"http://cdn9002.iflyos.cn/osweathericon/07.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-09 06:44:00","sunSet":"2025-03-09 18:33:00","tempHigh":"7℃","tempLow":"3℃","tempRange":"3℃ ~ 7℃","weather":"小雨转阴","weatherDescription":"有点冷。","weatherType":7,"week":"下周日","wind":"西北风转东南风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"长沙市","date":"2025-03-10","dateLong":1741536000,"date_for_voice":"10号","humidity":"85%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-10 06:43:00","sunSet":"2025-03-10 18:34:00","tempHigh":"6℃","tempLow":"2℃","tempRange":"2℃ ~ 6℃","weather":"阴转雨夹雪","weatherDescription":"天气寒冷，注意保暖。","weatherType":2,"week":"下下周一","wind":"北风转西北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"长沙市","date":"2025-03-11","dateLong":1741622400,"date_for_voice":"11号","humidity":"84%","img":"http://cdn9002.iflyos.cn/osweathericon/08.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-11 06:42:00","sunSet":"2025-03-11 18:34:00","tempHigh":"6℃","tempLow":"1℃","tempRange":"1℃ ~ 6℃","weather":"雨转雨夹雪","weatherDescription":"天气寒冷，注意保暖。","weatherType":8,"week":"下下周二","wind":"西北风3-4级","windLevel":1}]},"cityName":"长沙市"},"dm_intent":"custom","cityName":"长沙市","shouldEndSession":true},"nlu":{"input":"长沙今天会不会下雨呀","skill":"天气","skillId":"2019042500000544","skillVersion":"56","semantics":{"request":{"intent":"QUERY","slots":[{"name":"datetime","normValue":"{\"datetime\":\"2025-02-25\",\"suggestDatetime\":\"2025-02-25\"}","value":"今天"},{"name":"location.type","normValue":"LOC_BASIC","value":"LOC_BASIC"},{"name":"location.city","normValue":"长沙市","value":"长沙市"},{"name":"location.cityAddr","normValue":"长沙","value":"长沙"},{"name":"queryType","value":"确认"},{"name":"questionWord","value":"会不会"},{"name":"subfocus","value":"雨"}]}}},"timestamp":1740450222}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidfaf1095f@dx7afd1b1875ae3eef00"}
连接正常关闭
1740450222
param:b'{\n            "auth_id": "1354a422f75146bf9c84b01d41724644",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid6889146c@dx5b6a1b1875ae3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "421d174c48a746abb3dd1588c09588c6","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b44b1@dx1953aead2567844532"}
started:
ws start
####################
测试进行: ctm000106cf@hu17be9ec0fd8020c902#48548207.pcm
{"recordId":"ase000dbcba@hu1953aeadb6804d3882:421d174c48a746abb3dd1588c09588c6","requestId":"ase000dbcba@hu1953aeadb6804d3882","sessionId":"cid000b44b1@dx1953aead2567844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450225}
{"recordId":"gty000b44b2@dx1953aead3087844532:421d174c48a746abb3dd1588c09588c6","requestId":"gty000b44b2@dx1953aead3087844532","sessionId":"cid000b44b1@dx1953aead2567844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450226}
{"recordId":"gty000b44b2@dx1953aead3087844532:421d174c48a746abb3dd1588c09588c6","requestId":"gty000b44b2@dx1953aead3087844532","sessionId":"cid000b44b1@dx1953aead2567844532","eof":"1","text":"沿海地区的气压是多少","pinyin":"","topic":"asr.speech.result","age":"child","ageScore":"","gender":"","genderScore":"","language_class":"mandarin","timestamp":1740450226}
send data finished:1740450226.577612
{"recordId":"gty000b44b2@dx1953aead3087844532:421d174c48a746abb3dd1588c09588c6","requestId":"gty000b44b2@dx1953aead3087844532","sessionId":"cid000b44b1@dx1953aead2567844532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"沿海地区的气压是多少","intentId":"chat","intentName":"闲聊","nlg":"你成功难住我啦，换个简单的问我吧。","shouldEndSession":true},"nlu":{"input":"沿海地区的气压是多少","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1740450226}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid0e2983c1@dx40161b1875b23eef00"}
连接正常关闭
1740450226
param:b'{\n            "auth_id": "40de89fba94f40cbb5de34348d8b47f0",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid06b1800e@dx4e141b1875b33eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "94f78a5b27fa48fc943cd523a91e78cb","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b4222@dx1953aeae3e9b86a532"}
started:
ws start
####################
测试进行: ctm000106de@hu17be9ec1062020c902#48548208.pcm
{"recordId":"ase000e992e@hu1953aeaec5e05bf882:94f78a5b27fa48fc943cd523a91e78cb","requestId":"ase000e992e@hu1953aeaec5e05bf882","sessionId":"cid000b4222@dx1953aeae3e9b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450229}
{"recordId":"gty000b4223@dx1953aeae4a2b86a532:94f78a5b27fa48fc943cd523a91e78cb","requestId":"gty000b4223@dx1953aeae4a2b86a532","sessionId":"cid000b4222@dx1953aeae3e9b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450230}
{"recordId":"gty000b4223@dx1953aeae4a2b86a532:94f78a5b27fa48fc943cd523a91e78cb","requestId":"gty000b4223@dx1953aeae4a2b86a532","sessionId":"cid000b4222@dx1953aeae3e9b86a532","eof":"1","text":"芜湖今天的空气污染指数","pinyin":"","topic":"asr.speech.result","age":"child","ageScore":"","gender":"","genderScore":"","language_class":"mandarin","timestamp":1740450230}
send data finished:1740450230.606071
{"recordId":"gty000b4223@dx1953aeae4a2b86a532:94f78a5b27fa48fc943cd523a91e78cb","requestId":"gty000b4223@dx1953aeae4a2b86a532","sessionId":"cid000b4222@dx1953aeae3e9b86a532","topic":"dm.output","skill":"天气","skillId":"2019042500000544","speakUrl":"","error":{},"dm":{"input":"芜湖今天的空气污染指数","intentId":"QUERY","intentName":"查询天气信息","nlg":"今天芜湖市空气质量良，空气质量指数93，pm2.5指数92","widget":{"webhookResp":{"result":[{"airData":93,"airQuality":"良","city":"芜湖市","date":"2025-02-25","dateLong":1740412800,"date_for_voice":"今天","exp":{"ct":{"expName":"穿衣指数","level":"凉","prompt":"天气偏凉，可以穿上最时尚的那件大衣来凹造型，搭一条围巾时尚指数爆表。"},"dy":{"expName":"钓鱼指数","level":"较适宜","prompt":"气压小幅波动，可能会影响鱼儿的进食。"},"gm":{"expName":"感冒指数","level":"较易发","prompt":"感冒较易发生，干净整洁的环境和清新流通的空气都有利于降低感冒的几率，体质较弱的童鞋们要特别加强自我保护。"},"jt":{"expName":"交通指数","level":"良好","prompt":"天气较好，路面干燥，交通气象条件良好，车辆可以正常行驶。"},"tr":{"expName":"旅游指数","level":"适宜","prompt":"天气较好，但丝毫不会影响您出行的心情。温度适宜又有微风相伴，适宜旅游。"},"uv":{"expName":"紫外线指数","level":"最弱","prompt":"辐射弱，请涂擦SPF8-12防晒护肤品。"},"xc":{"expName":"洗车指数","level":"较适宜","prompt":"较适宜洗车，未来一天无雨，风力较小，擦洗一新的汽车至少能保持一天。"},"yd":{"expName":"运动指数","level":"较适宜","prompt":"天气还不错，只是气温有点低，户外运动要注意保暖。"}},"extra":"","humidity":"58%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","pm25":"92","precipitation":"0","sunRise":"2025-02-25 06:38:00","sunSet":"2025-02-25 18:01:00","temp":6,"tempHigh":"11℃","tempLow":"3℃","tempRange":"3℃ ~ 11℃","tempReal":"3℃","visibility":"","warning":"","weather":"阴","weatherDescription":"有点冷。","weatherDescription3":"3℃到20℃，风不大，有点凉。","weatherDescription7":"1℃到27℃，1号到3号有雨，风不大，温度适宜。","weatherType":2,"week":"周二","wind":"南风微风","windLevel":0},{"airData":77,"airQuality":"良","city":"芜湖市","date":"2025-02-24","dateLong":1740326400,"date_for_voice":"昨天","humidity":"40%","img":"http://cdn9002.iflyos.cn/osweathericon/00.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-02-24 06:39:00","sunSet":"2025-02-24 18:01:00","tempHigh":"9℃","tempLow":"3℃","tempRange":"3℃ ~ 9℃","weather":"晴转阴","weatherDescription":"有点冷。","weatherType":0,"week":"周一","wind":"东风微风","windLevel":0},{"airData":108,"airQuality":"轻微污染","city":"芜湖市","date":"2025-02-26","dateLong":1740499200,"date_for_voice":"明天","humidity":"61%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-02-26 06:37:00","sunSet":"2025-02-26 18:02:00","tempHigh":"15℃","tempLow":"5℃","tempRange":"5℃ ~ 15℃","weather":"多云","weatherDescription":"有点凉。","weatherType":1,"week":"周三","wind":"东南风微风","windLevel":0},{"airData":92,"airQuality":"良","city":"芜湖市","date":"2025-02-27","dateLong":1740585600,"date_for_voice":"后天","humidity":"79%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-02-27 06:36:00","sunSet":"2025-02-27 18:03:00","tempHigh":"20℃","tempLow":"9℃","tempRange":"9℃ ~ 20℃","weather":"多云","weatherDescription":"温度适宜。","weatherType":1,"week":"周四","wind":"西南风转东南风微风","windLevel":0},{"airData":70,"airQuality":"良","city":"芜湖市","date":"2025-02-28","dateLong":1740672000,"date_for_voice":"28号","humidity":"79%","img":"http://cdn9002.iflyos.cn/osweathericon/01.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-02-28 06:34:00","sunSet":"2025-02-28 18:04:00","tempHigh":"24℃","tempLow":"10℃","tempRange":"10℃ ~ 24℃","weather":"多云转晴","weatherDescription":"气候温暖。","weatherType":1,"week":"周五","wind":"东风3-4级","windLevel":1},{"airData":64,"airQuality":"良","city":"芜湖市","date":"2025-03-01","dateLong":1740758400,"date_for_voice":"1号","humidity":"75%","img":"http://cdn9002.iflyos.cn/osweathericon/07.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-01 06:33:00","sunSet":"2025-03-01 18:04:00","tempHigh":"27℃","tempLow":"17℃","tempRange":"17℃ ~ 27℃","weather":"小雨转阴","weatherDescription":"气候温暖。","weatherType":7,"week":"周六","wind":"西南风转东北风3-4级","windLevel":1},{"airData":58,"airQuality":"良","city":"芜湖市","date":"2025-03-02","dateLong":1740844800,"date_for_voice":"2号","humidity":"89%","img":"http://cdn9002.iflyos.cn/osweathericon/07.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-02 06:32:00","sunSet":"2025-03-02 18:05:00","tempHigh":"27℃","tempLow":"6℃","tempRange":"6℃ ~ 27℃","weather":"小雨","weatherDescription":"气候温暖。","weatherType":7,"week":"周日","wind":"东北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"芜湖市","date":"2025-03-03","dateLong":1740931200,"date_for_voice":"3号","humidity":"85%","img":"http://cdn9002.iflyos.cn/osweathericon/08.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-03 06:31:00","sunSet":"2025-03-03 18:06:00","tempHigh":"10℃","tempLow":"1℃","tempRange":"1℃ ~ 10℃","weather":"中雨转小雨","weatherDescription":"有点冷。","weatherType":8,"week":"下周一","wind":"东北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"芜湖市","date":"2025-03-04","dateLong":1741017600,"date_for_voice":"4号","humidity":"82%","img":"http://cdn9002.iflyos.cn/osweathericon/07.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-04 06:30:00","sunSet":"2025-03-04 18:07:00","tempHigh":"7℃","tempLow":"5℃","tempRange":"5℃ ~ 7℃","weather":"小雨转阴","weatherDescription":"有点冷。","weatherType":7,"week":"下周二","wind":"东北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"芜湖市","date":"2025-03-05","dateLong":1741104000,"date_for_voice":"5号","humidity":"74%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-05 06:29:00","sunSet":"2025-03-05 18:07:00","tempHigh":"13℃","tempLow":"4℃","tempRange":"4℃ ~ 13℃","weather":"阴","weatherDescription":"有点冷。","weatherType":2,"week":"下周三","wind":"东北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"芜湖市","date":"2025-03-06","dateLong":1741190400,"date_for_voice":"6号","humidity":"83%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-06 06:28:00","sunSet":"2025-03-06 18:08:00","tempHigh":"9℃","tempLow":"2℃","tempRange":"2℃ ~ 9℃","weather":"阴","weatherDescription":"有点冷。","weatherType":2,"week":"下周四","wind":"东北风转西风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"芜湖市","date":"2025-03-07","dateLong":1741276800,"date_for_voice":"7号","humidity":"75%","img":"http://cdn9002.iflyos.cn/osweathericon/07.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-07 06:26:00","sunSet":"2025-03-07 18:09:00","tempHigh":"7℃","tempLow":"4℃","tempRange":"4℃ ~ 7℃","weather":"小雨","weatherDescription":"有点冷。","weatherType":7,"week":"下周五","wind":"西北风转东北风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"芜湖市","date":"2025-03-08","dateLong":1741363200,"date_for_voice":"8号","humidity":"70%","img":"http://cdn9002.iflyos.cn/osweathericon/07.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-08 06:25:00","sunSet":"2025-03-08 18:10:00","tempHigh":"7℃","tempLow":"5℃","tempRange":"5℃ ~ 7℃","weather":"小雨","weatherDescription":"有点冷。","weatherType":7,"week":"下周六","wind":"东北风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"芜湖市","date":"2025-03-09","dateLong":1741449600,"date_for_voice":"9号","humidity":"76%","img":"http://cdn9002.iflyos.cn/osweathericon/07.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-09 06:24:00","sunSet":"2025-03-09 18:10:00","tempHigh":"5℃","tempLow":"2℃","tempRange":"2℃ ~ 5℃","weather":"小雨转阴","weatherDescription":"天气寒冷，注意保暖。","weatherType":7,"week":"下周日","wind":"东北风转东风3-4级","windLevel":1},{"airData":-1,"airQuality":"未知","city":"芜湖市","date":"2025-03-10","dateLong":1741536000,"date_for_voice":"10号","humidity":"76%","img":"http://cdn9002.iflyos.cn/osweathericon/02.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-10 06:23:00","sunSet":"2025-03-10 18:11:00","tempHigh":"8℃","tempLow":"1℃","tempRange":"1℃ ~ 8℃","weather":"阴转小雨","weatherDescription":"有点冷。","weatherType":2,"week":"下下周一","wind":"东北风微风","windLevel":0},{"airData":-1,"airQuality":"未知","city":"芜湖市","date":"2025-03-11","dateLong":1741622400,"date_for_voice":"11号","humidity":"77%","img":"http://cdn9002.iflyos.cn/osweathericon/06.png","lastUpdateTime":"2025-02-25 10:00:08","sunRise":"2025-03-11 06:22:00","sunSet":"2025-03-11 18:12:00","tempHigh":"2℃","tempLow":"1℃","tempRange":"1℃ ~ 2℃","weather":"雨夹雪","weatherDescription":"天气寒冷，注意保暖。","weatherType":6,"week":"下下周二","wind":"东北风3-4级","windLevel":1}]},"cityName":"芜湖市"},"dm_intent":"custom","cityName":"芜湖市","shouldEndSession":true},"nlu":{"input":"芜湖今天的空气污染指数","skill":"天气","skillId":"2019042500000544","skillVersion":"56","semantics":{"request":{"intent":"QUERY","slots":[{"name":"queryType","value":"确认"},{"name":"subfocus","value":"pm25"},{"name":"datetime","normValue":"{\"datetime\":\"2025-02-25\",\"suggestDatetime\":\"2025-02-25\"}","value":"今天"},{"name":"location.city","normValue":"芜湖市","value":"芜湖市"},{"name":"location.cityAddr","normValue":"芜湖","value":"芜湖"},{"name":"location.type","normValue":"LOC_BASIC","value":"LOC_BASIC"}]}}},"timestamp":1740450230}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidb6bb4f45@dxc4001b1875b63eef00"}
连接正常关闭
1740450230
param:b'{\n            "auth_id": "ad4fa8c1bccc4bf5ac32eed10d049890",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid50f79c2c@dxcbe11b1875b73eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "64eb457e05864a2ab9a27fa80d4ff58a","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b4082@dx1953aeaf376b8a9532"}
started:
ws start
####################
测试进行: ctm0001078b@hu17be5053f5e020c902#48499431.pcm
{"recordId":"ase000e8366@hu1953aeafe4a05c4882:64eb457e05864a2ab9a27fa80d4ff58a","requestId":"ase000e8366@hu1953aeafe4a05c4882","sessionId":"cid000b4082@dx1953aeaf376b8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450234}
{"recordId":"gty000b4083@dx1953aeaf41fb8a9532:64eb457e05864a2ab9a27fa80d4ff58a","requestId":"gty000b4083@dx1953aeaf41fb8a9532","sessionId":"cid000b4082@dx1953aeaf376b8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450235}
{"recordId":"gty000b4083@dx1953aeaf41fb8a9532:64eb457e05864a2ab9a27fa80d4ff58a","requestId":"gty000b4083@dx1953aeaf41fb8a9532","sessionId":"cid000b4082@dx1953aeaf376b8a9532","eof":"1","text":"有没有小阿枫的爱江山更爱美人","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1740450235}
send data finished:1740450235.719762
{"recordId":"gty000b4083@dx1953aeaf41fb8a9532:64eb457e05864a2ab9a27fa80d4ff58a","requestId":"gty000b4083@dx1953aeaf41fb8a9532","sessionId":"cid000b4082@dx1953aeaf376b8a9532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"有没有小阿枫的爱江山更爱美人","intentId":"PLAY","intentName":"点歌","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"有没有小阿枫的爱江山更爱美人","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"歌曲名","value":"爱江山更爱美人"},{"name":"歌手名","value":"小阿枫"}]}}},"timestamp":1740450235}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidb8d3dedd@dx82b51b1875bb3eef00"}
连接正常关闭
1740450235
param:b'{\n            "auth_id": "25cdff2d83b149c79a793b603e87a50d",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid4a7023bc@dx08e21b1875bc3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "228332c23da643a89a16846a0a13e297","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b422d@dx1953aeb06feb86a532"}
started:
ws start
####################
测试进行: ctm0001078f@hu17be505457e020c902#48499438.pcm
{"recordId":"ase000f1517@hu1953aeb11df05c0882:228332c23da643a89a16846a0a13e297","requestId":"ase000f1517@hu1953aeb11df05c0882","sessionId":"cid000b422d@dx1953aeb06feb86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450239}
{"recordId":"gty000b422e@dx1953aeb07aab86a532:228332c23da643a89a16846a0a13e297","requestId":"gty000b422e@dx1953aeb07aab86a532","sessionId":"cid000b422d@dx1953aeb06feb86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450241}
{"recordId":"gty000b422e@dx1953aeb07aab86a532:228332c23da643a89a16846a0a13e297","requestId":"gty000b422e@dx1953aeb07aab86a532","sessionId":"cid000b422d@dx1953aeb06feb86a532","eof":"1","text":"有没有最近很火的海伦的桥边姑娘","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1740450241}
send data finished:1740450241.1243975
{"recordId":"gty000b422e@dx1953aeb07aab86a532:228332c23da643a89a16846a0a13e297","requestId":"gty000b422e@dx1953aeb07aab86a532","sessionId":"cid000b422d@dx1953aeb06feb86a532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"有没有最近很火的海伦的桥边姑娘","intentId":"PLAY","intentName":"点歌","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"有没有最近很火的海伦的桥边姑娘","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"歌曲名","value":"桥边姑娘"},{"name":"歌手名","value":"海伦"},{"name":"排行榜","value":"最新|最热"},{"name":"标签","value":"新歌|热歌"}]}}},"timestamp":1740450241}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid016331a9@dx01a31b1875c13eef00"}
连接正常关闭
1740450241
param:b'{\n            "auth_id": "d507a243a28c458cba6b61cc43d1d0a8",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid98fc3c00@dxd85d1b1875c13eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "eb58a17001b847d1a8687572d86006cf","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b44bf@dx1953aeb1c387844532"}
started:
ws start
####################
测试进行: ctm00010792@hu17be5054ab1020c902#48499440.pcm
{"recordId":"ase000ea47b@hu1953aeb251e05bf882:eb58a17001b847d1a8687572d86006cf","requestId":"ase000ea47b@hu1953aeb251e05bf882","sessionId":"cid000b44bf@dx1953aeb1c387844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450244}
{"recordId":"gty000b44c0@dx1953aeb1ce87844532:eb58a17001b847d1a8687572d86006cf","requestId":"gty000b44c0@dx1953aeb1ce87844532","sessionId":"cid000b44bf@dx1953aeb1c387844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450244}
{"recordId":"gty000b44c0@dx1953aeb1ce87844532:eb58a17001b847d1a8687572d86006cf","requestId":"gty000b44c0@dx1953aeb1ce87844532","sessionId":"cid000b44bf@dx1953aeb1c387844532","eof":"1","text":"给我放一首","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1740450244}
send data finished:1740450244.2175555
{"recordId":"gty000b44c0@dx1953aeb1ce87844532:eb58a17001b847d1a8687572d86006cf","requestId":"gty000b44c0@dx1953aeb1ce87844532","sessionId":"cid000b44bf@dx1953aeb1c387844532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"给我放一首","intentId":"RANDOM_SEARCH","intentName":"随机放歌","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"给我放一首","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[]}}},"timestamp":1740450244}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid12a4030e@dx8fe51b1875c43eef00"}
连接正常关闭
1740450244
param:b'{\n            "auth_id": "de4ff2ff95c5463d9e3a3a1c4f92c9e4",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid5b6fc5ae@dxa9531b1875c43eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "bfc9f679a7b54b2aa1708fce41a7eaa0","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b44c5@dx1953aeb281d7844532"}
started:
ws start
####################
测试进行: ctm00010794@hu17be5054b60020c902#48499441.pcm
{"recordId":"ase000f2946@hu1953aeb315505c2882:bfc9f679a7b54b2aa1708fce41a7eaa0","requestId":"ase000f2946@hu1953aeb315505c2882","sessionId":"cid000b44c5@dx1953aeb281d7844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450247}
{"recordId":"gty000b44c6@dx1953aeb28d27844532:bfc9f679a7b54b2aa1708fce41a7eaa0","requestId":"gty000b44c6@dx1953aeb28d27844532","sessionId":"cid000b44c5@dx1953aeb281d7844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450248}
{"recordId":"gty000b44c6@dx1953aeb28d27844532:bfc9f679a7b54b2aa1708fce41a7eaa0","requestId":"gty000b44c6@dx1953aeb28d27844532","sessionId":"cid000b44c5@dx1953aeb281d7844532","eof":"1","text":"给我放一首任然的飞鸟与蝉","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1740450248}
send data finished:1740450248.7324579
{"recordId":"gty000b44c6@dx1953aeb28d27844532:bfc9f679a7b54b2aa1708fce41a7eaa0","requestId":"gty000b44c6@dx1953aeb28d27844532","sessionId":"cid000b44c5@dx1953aeb281d7844532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"给我放一首任然的飞鸟与蝉","intentId":"PLAY","intentName":"点歌","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"给我放一首任然的飞鸟与蝉","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"歌曲名","value":"飞鸟与蝉"},{"name":"歌手名","value":"任然"}]}}},"timestamp":1740450248}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid644db628@dx6e321b1875c83eef00"}
连接正常关闭
1740450248
param:b'{\n            "auth_id": "eae5a3eaeaf6416cac33c0804d3ba035",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidc321b5da@dx2f6f1b1875c93eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "fc84dd52bcfd4b3db86ba37708d126cc","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b44c7@dx1953aeb3a657844532"}
started:
ws start
####################
测试进行: ctm0001079a@hu17be505536c020c902#48499443.pcm
{"recordId":"gty000b44c8@dx1953aeb3b197844532:fc84dd52bcfd4b3db86ba37708d126cc","requestId":"gty000b44c8@dx1953aeb3b197844532","sessionId":"cid000b44c7@dx1953aeb3a657844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450251}
{"recordId":"gty000b44c8@dx1953aeb3b197844532:fc84dd52bcfd4b3db86ba37708d126cc","requestId":"gty000b44c8@dx1953aeb3b197844532","sessionId":"cid000b44c7@dx1953aeb3a657844532","eof":"1","text":"","pinyin":"","topic":"asr.speech.result","age":"child","ageScore":"","gender":"","genderScore":"","language_class":"mandarin","timestamp":1740450251}
{"recordId":"gty000b44c8@dx1953aeb3b197844532:fc84dd52bcfd4b3db86ba37708d126cc","requestId":"gty000b44c8@dx1953aeb3b197844532","sessionId":"cid000b44c7@dx1953aeb3a657844532","topic":"dm.output","skill":"","skillId":"","speakUrl":"","error":{"errId":"010305","errMsg":"asr result is null"},"dm":{},"nlu":{"input":"","skill":"","skillId":""},"timestamp":1740450251}
send data finished:1740450251.4743283
{"recordId":"ase000f1ec8@hu1953aeb42de05c0882:fc84dd52bcfd4b3db86ba37708d126cc","requestId":"ase000f1ec8@hu1953aeb42de05c0882","sessionId":"cid000b44c7@dx1953aeb3a657844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450251}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cide3d0ee8c@dx17191b1875cb3eef00"}
连接正常关闭
1740450251
param:b'{\n            "auth_id": "63022f299bce4ec093f3f701513965f3",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid90125c1c@dx34b61b1875cb3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "d792f55476a9429499e66b2aaee17bfb","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b44cb@dx1953aeb451c7844532"}
started:
ws start
####################
测试进行: ctm0001079d@hu17be505568a020c902#48499447.pcm
{"recordId":"gty000b44cc@dx1953aeb45d77844532:d792f55476a9429499e66b2aaee17bfb","requestId":"gty000b44cc@dx1953aeb45d77844532","sessionId":"cid000b44cb@dx1953aeb451c7844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450255}
{"recordId":"gty000b44cc@dx1953aeb45d77844532:d792f55476a9429499e66b2aaee17bfb","requestId":"gty000b44cc@dx1953aeb45d77844532","sessionId":"cid000b44cb@dx1953aeb451c7844532","eof":"1","text":"给我唱一首","pinyin":"","topic":"asr.speech.result","age":"child","ageScore":"","gender":"","genderScore":"","language_class":"mandarin","timestamp":1740450255}
{"recordId":"ase000dd3a5@hu1953aeb500d04d3882:d792f55476a9429499e66b2aaee17bfb","requestId":"ase000dd3a5@hu1953aeb500d04d3882","sessionId":"cid000b44cb@dx1953aeb451c7844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450255}
send data finished:1740450255.0922534
{"recordId":"gty000b44cc@dx1953aeb45d77844532:d792f55476a9429499e66b2aaee17bfb","requestId":"gty000b44cc@dx1953aeb45d77844532","sessionId":"cid000b44cb@dx1953aeb451c7844532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"给我唱一首","intentId":"RANDOM_SEARCH","intentName":"随机放歌","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"给我唱一首","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[]}}},"timestamp":1740450255}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid4d83fd13@dx76081b1875cf3eef00"}
连接正常关闭
1740450255
param:b'{\n            "auth_id": "1597ed81ac1249108899931ce3494e72",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid3c87add3@dxbeb41b1875cf3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "5e6846a2d00948f9b8888415cb8bdd9a","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b4096@dx1953aeb5287b8a9532"}
started:
ws start
####################
测试进行: ctm0001079e@hu17be50556b4020c902#48499450.pcm
send data finished:1740450272.7985005
1740450332
param:b'{\n            "auth_id": "df8ddcac4c854ef586eb8e110a5a2b26",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid326a6c26@dx6b071b18761d3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "01241ec613704a6c9f30f14ff767aba5","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b40cc@dx1953aec81e1b8a9532"}
started:
ws start
####################
测试进行: ctm000107a0@hu17be50557ea020c902#48499448.pcm
send data finished:1740450347.1708503
{"action":"error","code":"20010108","data":"","desc":"{\"code\":20010108,\"sub\":\"\"}","sid":"gty000b40cd@dx1953aec8297b8a9532"}
{'action': 'error', 'code': '20010108', 'data': '', 'desc': '{"code":20010108,"sub":""}', 'sid': 'gty000b40cd@dx1953aec8297b8a9532'}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid4315510d@dxfb231b18763a3eef00"}
连接正常关闭
1740450362
param:b'{\n            "auth_id": "1252ea0d3f964af8b0c2d9960e0eab9e",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid01bdafd6@dxd7031b18763a3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "97406c683f10472bb9253f9de69a4f65","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b4299@dx1953aecf6b5b86a532"}
started:
ws start
####################
测试进行: ctm000107a1@hu17be5055806020c902#48499449.pcm
send data finished:1740450376.9263954
{"action":"error","code":"20010108","data":"","desc":"{\"code\":20010108,\"sub\":\"\"}","sid":"gty000b429a@dx1953aecf752b86a532"}
{'action': 'error', 'code': '20010108', 'data': '', 'desc': '{"code":20010108,"sub":""}', 'sid': 'gty000b429a@dx1953aecf752b86a532'}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid0493c40a@dx7a4c1b1876583eef00"}
连接正常关闭
1740450392
param:b'{\n            "auth_id": "355d405265474f78942121b5d9a2f200",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidda5942b8@dxadb51b1876583eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "3c1fe4122d1e49fdabc6081ff245a3b8","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b3fbe@dx1953aed6af4b8aa532"}
started:
ws start
####################
测试进行: ctm000107a3@hu17be5055c3d020c902#48499455.pcm
send data finished:1740450408.0680122
1740450467
param:b'{\n            "auth_id": "2f473feef7524e9db73df3599104c320",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidac076dc7@dx68ef1b1876a43eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "3cd92eb47f4e4fc08aebda01793b7681","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b42ee@dx1953aee9164b86a532"}
started:
ws start
####################
测试进行: ctm000107a6@hu17be50560d6020c902#48499458.pcm
send data finished:1740450483.4190347
1740450543
param:b'{\n            "auth_id": "9ad3cf15b64a451f94b8db069d7a0d11",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidfe5c2de8@dxb5171b1876ef3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "b237861a29b44cc4b47d391e2442a0e0","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b433f@dx1953aefb7d1b86a532"}
started:
ws start
####################
测试进行: ctm000107a7@hu17be50561be020c902#48499459.pcm
{"recordId":"ase000e6493@hu1953aefc28a05c3882:b237861a29b44cc4b47d391e2442a0e0","requestId":"ase000e6493@hu1953aefc28a05c3882","sessionId":"cid000b433f@dx1953aefb7d1b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450546}
{"recordId":"gty000b4342@dx1953aefb886b86a532:b237861a29b44cc4b47d391e2442a0e0","requestId":"gty000b4342@dx1953aefb886b86a532","sessionId":"cid000b433f@dx1953aefb7d1b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450549}
{"recordId":"gty000b4342@dx1953aefb886b86a532:b237861a29b44cc4b47d391e2442a0e0","requestId":"gty000b4342@dx1953aefb886b86a532","sessionId":"cid000b433f@dx1953aefb7d1b86a532","eof":"1","text":"给我放一首易烊千玺的精彩才刚刚开始","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1740450549}
send data finished:1740450549.767421
{"recordId":"gty000b4342@dx1953aefb886b86a532:b237861a29b44cc4b47d391e2442a0e0","requestId":"gty000b4342@dx1953aefb886b86a532","sessionId":"cid000b433f@dx1953aefb7d1b86a532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"给我放一首易烊千玺的精彩才刚刚开始","intentId":"PLAY","intentName":"点歌","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"给我放一首易烊千玺的精彩才刚刚开始","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"歌曲名","value":"精彩才刚刚开始"},{"name":"歌手名","value":"易烊千玺"}]}}},"timestamp":1740450549}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cida73a9a0f@dxbdba1b1876f53eef00"}
连接正常关闭
1740450549
param:b'{\n            "auth_id": "f243f5ce122a4436a6204b125a3b4ea1",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidcce81fe8@dx01131b1876f63eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "eba6f46037e248d4ab58df07c8398772","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b418a@dx1953aefd1bdb8a9532"}
started:
ws start
####################
测试进行: ctm000107aa@hu17be505666f020c902#48499464.pcm
{"recordId":"ase000ea388@hu1953aefdbf01323882:eba6f46037e248d4ab58df07c8398772","requestId":"ase000ea388@hu1953aefdbf01323882","sessionId":"cid000b418a@dx1953aefd1bdb8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450552}
{"recordId":"gty000b418b@dx1953aefd273b8a9532:eba6f46037e248d4ab58df07c8398772","requestId":"gty000b418b@dx1953aefd273b8a9532","sessionId":"cid000b418a@dx1953aefd1bdb8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450556}
{"recordId":"gty000b418b@dx1953aefd273b8a9532:eba6f46037e248d4ab58df07c8398772","requestId":"gty000b418b@dx1953aefd273b8a9532","sessionId":"cid000b418a@dx1953aefd1bdb8a9532","eof":"1","text":"给我放一首夏洛特烦恼暖水曲一次就好","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1740450556}
send data finished:1740450556.5279748
{"recordId":"gty000b418b@dx1953aefd273b8a9532:eba6f46037e248d4ab58df07c8398772","requestId":"gty000b418b@dx1953aefd273b8a9532","sessionId":"cid000b418a@dx1953aefd1bdb8a9532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"给我放一首夏洛特烦恼暖水曲一次就好","intentId":"PLAY","intentName":"点歌","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"给我放一首夏洛特烦恼暖水曲一次就好","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"歌曲名","value":"一次就好"},{"name":"电影","value":"夏洛特烦恼"},{"name":"歌曲来源","value":"夏洛特烦恼"}]}}},"timestamp":1740450556}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidf8063a13@dxeb5b1b1876fc3eef00"}
连接正常关闭
1740450556
param:b'{\n            "auth_id": "4ccdfb5c098d4dc2983a5d05c9194ae8",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid55c52b7b@dx63601b1876fc3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "fdc1016205dd405e876e37d63389fe50","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b403d@dx1953aefec42b8aa532"}
started:
ws start
####################
测试进行: ctm000107ab@hu17be50567e0020c902#48499463.pcm
{"recordId":"ase000e7e96@hu1953aeff5a805c4882:fdc1016205dd405e876e37d63389fe50","requestId":"ase000e7e96@hu1953aeff5a805c4882","sessionId":"cid000b403d@dx1953aefec42b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450559}
{"recordId":"gty000b403e@dx1953aefecfab8aa532:fdc1016205dd405e876e37d63389fe50","requestId":"gty000b403e@dx1953aefecfab8aa532","sessionId":"cid000b403d@dx1953aefec42b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450560}
{"recordId":"gty000b403e@dx1953aefecfab8aa532:fdc1016205dd405e876e37d63389fe50","requestId":"gty000b403e@dx1953aefecfab8aa532","sessionId":"cid000b403d@dx1953aefec42b8aa532","eof":"1","text":"给我放一首周传雄","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1740450560}
send data finished:1740450560.1008215
{"recordId":"gty000b403e@dx1953aefecfab8aa532:fdc1016205dd405e876e37d63389fe50","requestId":"gty000b403e@dx1953aefecfab8aa532","sessionId":"cid000b403d@dx1953aefec42b8aa532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"给我放一首周传雄","intentId":"PLAY","intentName":"点歌","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"给我放一首周传雄","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"歌手名","value":"周传雄"}]}}},"timestamp":1740450560}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid7f769ef5@dx7c131b1877003eef00"}
连接正常关闭
1740450560
param:b'{\n            "auth_id": "ce6a3e9da52f463f91b054f1d054379f",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid01329e72@dx87be1b1877003eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "69b7db164eb84dd4b55aa36bcb6f8227","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b45bd@dx1953aeffa047844532"}
started:
ws start
####################
测试进行: ctm000107ac@hu17be505689e020c902#48499466.pcm
{"recordId":"ase000f0ed5@hu1953af0041905c0882:69b7db164eb84dd4b55aa36bcb6f8227","requestId":"ase000f0ed5@hu1953af0041905c0882","sessionId":"cid000b45bd@dx1953aeffa047844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450563}
{"recordId":"gty000b45be@dx1953aeffab47844532:69b7db164eb84dd4b55aa36bcb6f8227","requestId":"gty000b45be@dx1953aeffab47844532","sessionId":"cid000b45bd@dx1953aeffa047844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450565}
{"recordId":"gty000b45be@dx1953aeffab47844532:69b7db164eb84dd4b55aa36bcb6f8227","requestId":"gty000b45be@dx1953aeffab47844532","sessionId":"cid000b45bd@dx1953aeffa047844532","eof":"1","text":"给我放一首王铮亮的静静的夜晚","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1740450565}
send data finished:1740450565.4404993
{"recordId":"gty000b45be@dx1953aeffab47844532:69b7db164eb84dd4b55aa36bcb6f8227","requestId":"gty000b45be@dx1953aeffab47844532","sessionId":"cid000b45bd@dx1953aeffa047844532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"给我放一首王铮亮的静静的夜晚","intentId":"PLAY","intentName":"点歌","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"给我放一首王铮亮的静静的夜晚","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"歌曲名","value":"静静的夜晚"},{"name":"歌手名","value":"王铮亮"}]}}},"timestamp":1740450565}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidd27fd188@dxd5c31b1877053eef00"}
连接正常关闭
1740450565
param:b'{\n            "auth_id": "b38610736d354dfd8fa89b5c641267bd",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid92a6ee28@dx82871b1877053eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "119424d6dd2d4aa68d5263c1597e3a9c","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b4353@dx1953af00edeb86a532"}
started:
ws start
####################
测试进行: ctm000107af@hu17be5056b7e020c902#48499467.pcm
{"recordId":"ase000e75ab@hu1953af018bf05c3882:119424d6dd2d4aa68d5263c1597e3a9c","requestId":"ase000e75ab@hu1953af018bf05c3882","sessionId":"cid000b4353@dx1953af00edeb86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450568}
{"recordId":"gty000b4354@dx1953af00f95b86a532:119424d6dd2d4aa68d5263c1597e3a9c","requestId":"gty000b4354@dx1953af00f95b86a532","sessionId":"cid000b4353@dx1953af00edeb86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450570}
{"recordId":"gty000b4354@dx1953af00f95b86a532:119424d6dd2d4aa68d5263c1597e3a9c","requestId":"gty000b4354@dx1953af00f95b86a532","sessionId":"cid000b4353@dx1953af00edeb86a532","eof":"1","text":"给我放一首西虹市首富电影","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1740450570}
send data finished:1740450570.3920748
{"recordId":"gty000b4354@dx1953af00f95b86a532:119424d6dd2d4aa68d5263c1597e3a9c","requestId":"gty000b4354@dx1953af00f95b86a532","sessionId":"cid000b4353@dx1953af00edeb86a532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"给我放一首西虹市首富电影","intentId":"chat","intentName":"闲聊","nlg":"《西虹市首富》故事发生在《夏洛特烦恼》中的“特烦恼”之城“西虹市”。混迹于丙级业余足球队的由沈腾饰演的守门员王多鱼，因比赛失利被开除离队。正处于人生最低谷的他接受了神秘台湾财团“一个月花光十亿资金”的挑战。本以为快乐生活就此开始，王多鱼却第一次感到“花钱特烦恼”！想要人生反转走上巅峰，真的没有那么简单。","shouldEndSession":true},"nlu":{"input":"给我放一首西虹市首富电影","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1740450570}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cida982211b@dx7ab81b18770a3eef00"}
连接正常关闭
1740450570
param:b'{\n            "auth_id": "c32ab11fb2a142de9bfd4075b99b96de",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid2668350f@dxebc11b18770a3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "1d7fc6000fb248d8b1fc1f3a7975d1d0","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b45c3@dx1953af022df7844532"}
started:
ws start
####################
测试进行: ctm000107b0@hu17be5056b80020c902#48499469.pcm
{"recordId":"ase000f172e@hu1953af02c2405c0882:1d7fc6000fb248d8b1fc1f3a7975d1d0","requestId":"ase000f172e@hu1953af02c2405c0882","sessionId":"cid000b45c3@dx1953af022df7844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450573}
{"recordId":"gty000b45c4@dx1953af0239e7844532:1d7fc6000fb248d8b1fc1f3a7975d1d0","requestId":"gty000b45c4@dx1953af0239e7844532","sessionId":"cid000b45c3@dx1953af022df7844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450576}
{"recordId":"gty000b45c4@dx1953af0239e7844532:1d7fc6000fb248d8b1fc1f3a7975d1d0","requestId":"gty000b45c4@dx1953af0239e7844532","sessionId":"cid000b45c3@dx1953af022df7844532","eof":"1","text":"给我放一首程响的世界那么大还是遇见你","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1740450576}
send data finished:1740450576.8262813
{"recordId":"gty000b45c4@dx1953af0239e7844532:1d7fc6000fb248d8b1fc1f3a7975d1d0","requestId":"gty000b45c4@dx1953af0239e7844532","sessionId":"cid000b45c3@dx1953af022df7844532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"给我放一首程响的世界那么大还是遇见你","intentId":"PLAY","intentName":"点歌","nlg":"暂不支持该功能，我还在学习中。","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"给我放一首程响的世界那么大还是遇见你","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"歌曲名","value":"世界那么大还是遇见你"},{"name":"歌手名","value":"程响"}]}}},"timestamp":1740450576}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidb60361fb@dx15951b1877103eef00"}
连接正常关闭
1740450576
param:b'{\n            "auth_id": "c08eead62a7548879036f56e552f5fb3",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid5e3f7262@dx12301b1877113eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "931cc6dabc0d413e97f3bde8bfe04b85","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b4358@dx1953af03b56b86a532"}
started:
ws start
####################
测试进行: ctm000107b2@hu17be5056ddd020c902#48499470.pcm
{"recordId":"gty000b4359@dx1953af03c06b86a532:931cc6dabc0d413e97f3bde8bfe04b85","requestId":"gty000b4359@dx1953af03c06b86a532","sessionId":"cid000b4358@dx1953af03b56b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450579}
{"recordId":"gty000b4359@dx1953af03c06b86a532:931cc6dabc0d413e97f3bde8bfe04b85","requestId":"gty000b4359@dx1953af03c06b86a532","sessionId":"cid000b4358@dx1953af03b56b86a532","eof":"1","text":"太阳的年龄","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1740450579}
send data finished:1740450579.7872198
{"recordId":"gty000b4359@dx1953af03c06b86a532:931cc6dabc0d413e97f3bde8bfe04b85","requestId":"gty000b4359@dx1953af03c06b86a532","sessionId":"cid000b4358@dx1953af03b56b86a532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"太阳的年龄","intentId":"chat","intentName":"闲聊","nlg":"亲亲我，我就告诉你。","shouldEndSession":true},"nlu":{"input":"太阳的年龄","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1740450579}
{"recordId":"ase000eb7d5@hu1953af045901323882:931cc6dabc0d413e97f3bde8bfe04b85","requestId":"ase000eb7d5@hu1953af045901323882","sessionId":"cid000b4358@dx1953af03b56b86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450579}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid3e3da46f@dx87461b1877143eef00"}
连接正常关闭
1740450580
param:b'{\n            "auth_id": "bdf2394895324096b07e666c0958c91e",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid759c4039@dx01c81b1877143eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "6c10ad540bae429191ab439db28aa89b","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b435f@dx1953af0477ab86a532"}
started:
ws start
####################
测试进行: ctm000107b4@hu17be5056f76020c902#48499476.pcm
{"recordId":"gty000b4360@dx1953af0482db86a532:6c10ad540bae429191ab439db28aa89b","requestId":"gty000b4360@dx1953af0482db86a532","sessionId":"cid000b435f@dx1953af0477ab86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450583}
{"recordId":"gty000b4360@dx1953af0482db86a532:6c10ad540bae429191ab439db28aa89b","requestId":"gty000b4360@dx1953af0482db86a532","sessionId":"cid000b435f@dx1953af0477ab86a532","eof":"1","text":"凡尔赛是什么","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1740450583}
send data finished:1740450583.1333618
{"recordId":"ase000f2cd0@hu1953af051e305c2882:6c10ad540bae429191ab439db28aa89b","requestId":"ase000f2cd0@hu1953af051e305c2882","sessionId":"cid000b435f@dx1953af0477ab86a532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450583}
{"recordId":"gty000b4360@dx1953af0482db86a532:6c10ad540bae429191ab439db28aa89b","requestId":"gty000b4360@dx1953af0482db86a532","sessionId":"cid000b435f@dx1953af0477ab86a532","topic":"dm.output","skill":"词典","skillId":"wordsDictionary","speakUrl":"","error":{},"dm":{"input":"凡尔赛是什么","intentId":"MEANING_QUERY","intentName":"查询词语解释","nlg":"我知道凡尔赛哦，它的解释是：法国巴黎的卫星城。在巴黎西南15千米。人口9.4万1975年。曾为法国贵族云集之地和法兰西王朝首府。旅游胜地，有驰名世界的凡尔赛宫。","shouldEndSession":true},"nlu":{"input":"凡尔赛是什么","skill":"词典","skillId":"wordsDictionary","skillVersion":"196.0","semantics":{"request":{"slots":[{"name":"name","value":"凡尔赛"}]}}},"timestamp":1740450583}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidf34104fb@dx50131b1877173eef00"}
连接正常关闭
1740450583
param:b'{\n            "auth_id": "65838bf4a0524fd0a3d146c474de96f5",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid64760bbb@dx16f11b1877173eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "3a61209fe8a748079f53b4a3d90d1c4c","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b41ae@dx1953af053f1b8a9532"}
started:
ws start
####################
测试进行: ctm000107b6@hu17be505732e020c902#48499479.pcm
send data finished:1740450596.5989435
{"action":"error","code":"20010108","data":"","desc":"{\"code\":20010108,\"sub\":\"\"}","sid":"gty000b41af@dx1953af054a3b8a9532"}
{'action': 'error', 'code': '20010108', 'data': '', 'desc': '{"code":20010108,"sub":""}', 'sid': 'gty000b41af@dx1953af054a3b8a9532'}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid6c180d97@dx69621b1877343eef00"}
连接正常关闭
1740450612
param:b'{\n            "auth_id": "172dd588371a408c87195f83190f3437",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidcd4f48cc@dxa3d41b1877343eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "8e39436a60194d04b2acea245ce89551","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b45eb@dx1953af0c5267844532"}
started:
ws start
####################
测试进行: ctm000107b8@hu17be5057453020c902#48499481.pcm
send data finished:1740450626.4112236
{"action":"error","code":"20010108","data":"","desc":"{\"code\":20010108,\"sub\":\"\"}","sid":"gty000b45ec@dx1953af0c5be7844532"}
{'action': 'error', 'code': '20010108', 'data': '', 'desc': '{"code":20010108,"sub":""}', 'sid': 'gty000b45ec@dx1953af0c5be7844532'}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidcadf9574@dx1af81b1877513eef00"}
连接正常关闭
1740450642
param:b'{\n            "auth_id": "fcdaf9bfa01c458a9dcdd77958d10401",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidb2862af4@dx349e1b1877523eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "4a9e6ae2006c4e65834b83d910caa5fe","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b45fd@dx1953af139bf7844532"}
started:
ws start
####################
测试进行: ctm000107b9@hu17be5057641020c902#48499482.pcm
{"recordId":"ase000f5036@hu1953af1431e05c0882:4a9e6ae2006c4e65834b83d910caa5fe","requestId":"ase000f5036@hu1953af1431e05c0882","sessionId":"cid000b45fd@dx1953af139bf7844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450644}
{"recordId":"gty000b45fe@dx1953af13a707844532:4a9e6ae2006c4e65834b83d910caa5fe","requestId":"gty000b45fe@dx1953af13a707844532","sessionId":"cid000b45fd@dx1953af139bf7844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450645}
{"recordId":"gty000b45fe@dx1953af13a707844532:4a9e6ae2006c4e65834b83d910caa5fe","requestId":"gty000b45fe@dx1953af13a707844532","sessionId":"cid000b45fd@dx1953af139bf7844532","eof":"1","text":"万圣节是什么时候","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1740450645}
send data finished:1740450645.42806
{"recordId":"gty000b45fe@dx1953af13a707844532:4a9e6ae2006c4e65834b83d910caa5fe","requestId":"gty000b45fe@dx1953af13a707844532","sessionId":"cid000b45fd@dx1953af139bf7844532","topic":"dm.output","skill":"时间日期","skillId":"IFLYTEK.datetimePro","speakUrl":"","error":{},"dm":{"input":"万圣节是什么时候","intentId":"WHATDATE","intentName":"查日期","nlg":"万圣节是2025年11月1号，星期六，万圣节，乙巳年九月十二。","shouldEndSession":true},"nlu":{"input":"万圣节是什么时候","skill":"时间日期","skillId":"IFLYTEK.datetimePro","skillVersion":"474.0","semantics":{"request":{"slots":[{"name":"datetime","normValue":"{\"datetime\":\"万圣节\",\"suggestDatetime\":\"2025-11-01\"}","value":"万圣节"}]}}},"timestamp":1740450645}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid829908b1@dxc50a1b1877553eef00"}
连接正常关闭
1740450645
param:b'{\n            "auth_id": "675027a178874302ab293009cd68d9df",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid603966a7@dxaa9d1b1877553eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "a6ba84bb10ed412d9b1c1447468b8095","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b4603@dx1953af1476d7844532"}
started:
ws start
####################
测试进行: ctm000107ba@hu17be5057646020c902#48499486.pcm
{"recordId":"ase000eeee8@hu1953af14f6b1323882:a6ba84bb10ed412d9b1c1447468b8095","requestId":"ase000eeee8@hu1953af14f6b1323882","sessionId":"cid000b4603@dx1953af1476d7844532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740450648}
{"recordId":"gty000b4607@dx1953af148297844532:a6ba84bb10ed412d9b1c1447468b8095","requestId":"gty000b4607@dx1953af148297844532","sessionId":"cid000b4603@dx1953af1476d7844532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740450649}
{"recordId":"gty000b4607@dx1953af148297844532:a6ba84bb10ed412d9b1c1447468b8095","requestId":"gty000b4607@dx1953af148297844532","sessionId":"cid000b4603@dx1953af1476d7844532","eof":"1","text":"狗狗不能吃什么","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1740450649}
send data finished:1740450649.2224052
{"recordId":"gty000b4607@dx1953af148297844532:a6ba84bb10ed412d9b1c1447468b8095","requestId":"gty000b4607@dx1953af148297844532","sessionId":"cid000b4603@dx1953af1476d7844532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"狗狗不能吃什么","intentId":"chat","intentName":"闲聊","nlg":"当然是不好吃的就不能吃了啊。","shouldEndSession":true},"nlu":{"input":"狗狗不能吃什么","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1740450649}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid03213463@dx052a1b1877593eef00"}
连接正常关闭
1740450649
param:b'{\n            "auth_id": "931b4d8d5e2347c88155c115ad2bfb7c",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid522ccc01@dx5f831b1877593eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "3fab94d61184439582fd55d16ca0d998","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b438d@dx1953af156abb86a532"}
started:
ws start
####################
测试进行: ctm000107bb@hu17be5057646020c902#48499483.pcm
send data finished:1740450662.5130677
{"action":"error","code":"20010108","data":"","desc":"{\"code\":20010108,\"sub\":\"\"}","sid":"gty000b438e@dx1953af1575eb86a532"}
{'action': 'error', 'code': '20010108', 'data': '', 'desc': '{"code":20010108,"sub":""}', 'sid': 'gty000b438e@dx1953af1575eb86a532'}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid47923d15@dxabd61b1877763eef00"}
连接正常关闭
1740450678
param:b'{\n            "auth_id": "5095589446f74b009dd42b55392df66b",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidb9ec086d@dxc1e01b1877763eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "3c220be9d4ff45b8b6be3ebbd79ce3f5","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b439b@dx1953af1c69bb86a532"}
started:
ws start
####################
测试进行: ctm000107bc@hu17be5057652020c902#48499484.pcm
send data finished:1740450691.1822615
{"action":"error","code":"20010108","data":"","desc":"{\"code\":20010108,\"sub\":\"\"}","sid":"gty000b439c@dx1953af1c735b86a532"}
{'action': 'error', 'code': '20010108', 'data': '', 'desc': '{"code":20010108,"sub":""}', 'sid': 'gty000b439c@dx1953af1c735b86a532'}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid9fa4a52b@dx0eb01b1877923eef00"}
连接正常关闭
1740450706
param:b'{\n            "auth_id": "070be9eba48149868004d7b67dfe5257",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cide792ebd9@dx9d6c1b1877923eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "0cc0c1364c534c99a556e3c4b05f94d4","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b4215@dx1953af236ceb8a9532"}
started:
ws start
####################
测试进行: ctm000107bd@hu17be505775a020c902#48499488.pcm
send data finished:1740450719.8873622
{"action":"error","code":"20010108","data":"","desc":"{\"code\":20010108,\"sub\":\"\"}","sid":"gty000b4216@dx1953af23770b8a9532"}
{'action': 'error', 'code': '20010108', 'data': '', 'desc': '{"code":20010108,"sub":""}', 'sid': 'gty000b4216@dx1953af23770b8a9532'}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid4eaefd27@dx67951b1877af3eef00"}
连接正常关闭
1740450735
param:b'{\n            "auth_id": "634ce218d4284abe9bbb3bc1abbe3083",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid9d270f13@dxf2d31b1877af3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "390770938e8142eda9ae16cc3c5c0cb8","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b463d@dx1953af2a6cd7844532"}
started:
ws start
####################
测试进行: ctm000107be@hu17be505783a020c902#48499490.pcm
send data finished:1740450749.476832
{"action":"error","code":"20010108","data":"","desc":"{\"code\":20010108,\"sub\":\"\"}","sid":"gty000b463e@dx1953af2a7657844532"}
{'action': 'error', 'code': '20010108', 'data': '', 'desc': '{"code":20010108,"sub":""}', 'sid': 'gty000b463e@dx1953af2a7657844532'}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid56024f53@dxaaaa1b1877cd3eef00"}
连接正常关闭
1740450765
param:b'{\n            "auth_id": "75c58f22d52342e7b9a61e115809623e",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidf9ba55e8@dxa7221b1877cd3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "c3b849aaefe54da6966d407791950b5b","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b424d@dx1953af31a6ab8a9532"}
started:
ws start
####################
测试进行: ctm000107c1@hu17be5057d0a020c902#48499493.pcm
send data finished:1740450780.07653
1740450839
param:b'{\n            "auth_id": "6e4756feb8c74e45b51068a1bdaf56f2",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid2f7aa095@dxf3541b1878183eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "34d5cb8d0a5749de9161dc470c0111b4","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b413c@dx1953af43e82b8aa532"}
started:
ws start
####################
测试进行: ctm000107c2@hu17be5057d48020c902#48499495.pcm
send data finished:1740450855.4145615
1740450915
param:b'{\n            "auth_id": "a152faf277154bf291de4fb8328e1a6d",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid3e836296@dx50041b1878633eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "1951648c15e04f94851958d81723ad0f","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b46ea@dx1953af5657e7844532"}
started:
ws start
####################
测试进行: ctm000107c3@hu17be505814f020c902#48499499.pcm
send data finished:1740450929.4189105
{"action":"error","code":"20010108","data":"","desc":"{\"code\":20010108,\"sub\":\"\"}","sid":"gty000b46eb@dx1953af566187844532"}
{'action': 'error', 'code': '20010108', 'data': '', 'desc': '{"code":20010108,"sub":""}', 'sid': 'gty000b46eb@dx1953af566187844532'}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid69615ece@dx4d951b1878813eef00"}
连接正常关闭
1740450945
param:b'{\n            "auth_id": "23f36398dd754251b87e2194eb607a2d",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidbed8f73f@dxb3a41b1878813eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "018247e73bda4fae8eab0a9424dc8e78","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b4485@dx1953af5db2cb86a532"}
started:
ws start
####################
测试进行: ctm000107c6@hu17be50585c1020c902#48499503.pcm
send data finished:1740450958.8408167
{"action":"error","code":"20010108","data":"","desc":"{\"code\":20010108,\"sub\":\"\"}","sid":"gty000b4486@dx1953af5dbd2b86a532"}
{'action': 'error', 'code': '20010108', 'data': '', 'desc': '{"code":20010108,"sub":""}', 'sid': 'gty000b4486@dx1953af5dbd2b86a532'}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid09232f9d@dx54651b18789e3eef00"}
连接正常关闭
1740450974
param:b'{\n            "auth_id": "156539836c554a0c97cc1a3e146ea485",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid720aae57@dx38d41b18789f3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "c485e83407b24a7ca837f31437f5df9d","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b4713@dx1953af64ee87844532"}
started:
ws start
####################
测试进行: ctm000107c8@hu17be50586f7020c902#48499507.pcm
send data finished:1740450988.8685362
{"action":"error","code":"20010108","data":"","desc":"{\"code\":20010108,\"sub\":\"\"}","sid":"gty000b4714@dx1953af64f867844532"}
{'action': 'error', 'code': '20010108', 'data': '', 'desc': '{"code":20010108,"sub":""}', 'sid': 'gty000b4714@dx1953af64f867844532'}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid34b3f086@dx41da1b1878bc3eef00"}
连接正常关闭
1740451004
param:b'{\n            "auth_id": "a2aef6579eb045fa90202cace63a34e7",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid2a4f2b57@dx47551b1878bc3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "512d054b37ec4d5c8b73240e69d2a154","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b4725@dx1953af6c18f7844532"}
started:
ws start
####################
测试进行: ctm000107ca@hu17be5058b1a020c902#48499509.pcm
send data finished:1740451019.237459
{"action":"error","code":"20010108","data":"","desc":"{\"code\":20010108,\"sub\":\"\"}","sid":"gty000b4727@dx1953af6c2367844532"}
{'action': 'error', 'code': '20010108', 'data': '', 'desc': '{"code":20010108,"sub":""}', 'sid': 'gty000b4727@dx1953af6c2367844532'}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidd5ba9047@dxbe261b1878da3eef00"}
连接正常关闭
1740451034
param:b'{\n            "auth_id": "5c8c07ddb3894dcb9ef60a34aa156397",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid7e4c38e0@dx2c211b1878db3eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "d3d54f71b46841f0ae3c1dc0a3658933","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b4746@dx1953af73acc7844532"}
started:
ws start
####################
测试进行: ctm000108e1@hu17be9ea86120212902#48547802.pcm
send data finished:1740451049.3510592
{"action":"error","code":"20010108","data":"","desc":"{\"code\":20010108,\"sub\":\"\"}","sid":"gty000b4747@dx1953af73b827844532"}
{'action': 'error', 'code': '20010108', 'data': '', 'desc': '{"code":20010108,"sub":""}', 'sid': 'gty000b4747@dx1953af73b827844532'}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidd2261549@dx9b661b1878f83eef00"}
连接正常关闭
1740451064
param:b'{\n            "auth_id": "1e5c290271894569952604d198e80bac",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cide7200210@dxe5161b1878f93eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "72131efbc6d7493a9146bd41a1df9973","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b4764@dx1953af7af827844532"}
started:
ws start
####################
测试进行: ctm00010a9b@hu17be9f606a00212902#48549981.pcm
send data finished:1740451080.2962584
{"action":"error","code":"20010108","data":"","desc":"{\"code\":20010108,\"sub\":\"\"}","sid":"gty000b4765@dx1953af7b0347844532"}
{'action': 'error', 'code': '20010108', 'data': '', 'desc': '{"code":20010108,"sub":""}', 'sid': 'gty000b4765@dx1953af7b0347844532'}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid19cfe806@dx27e01b1879173eef00"}
连接正常关闭
1740451096
param:b'{\n            "auth_id": "fdb0daea939e42c4bcdf1664b85bbaf7",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid32093e5b@dxf8cb1b1879183eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "cd1903dd9b1f4a1491e1b0c9bdc36149","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b4505@dx1953af82924b86a532"}
started:
ws start
####################
测试进行: ctm00010aa1@hu17be9f606cc0212902#48549980.pcm
send data finished:1740451111.134361
{"action":"error","code":"20010108","data":"","desc":"{\"code\":20010108,\"sub\":\"\"}","sid":"gty000b4506@dx1953af829c3b86a532"}
{'action': 'error', 'code': '20010108', 'data': '', 'desc': '{"code":20010108,"sub":""}', 'sid': 'gty000b4506@dx1953af829c3b86a532'}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cidef93ea77@dxb4a51b1879363eef00"}
连接正常关闭
1740451126
param:b'{\n            "auth_id": "65aa330333a949778739c043e3ad6d51",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cidfa27cf43@dx64031b1879373eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "823a7949d1c147efab9c73db3b0f756d","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b4531@dx1953af8c4e3b86a532"}
started:
ws start
####################
测试进行: ctm00010aa3@hu17be9f606e10212902#48549982.pcm
{"recordId":"gty000b4532@dx1953af8c59cb86a532:823a7949d1c147efab9c73db3b0f756d","requestId":"gty000b4532@dx1953af8c59cb86a532","sessionId":"cid000b4531@dx1953af8c4e3b86a532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740451140}
{"recordId":"gty000b4532@dx1953af8c59cb86a532:823a7949d1c147efab9c73db3b0f756d","requestId":"gty000b4532@dx1953af8c59cb86a532","sessionId":"cid000b4531@dx1953af8c4e3b86a532","eof":"1","text":"欧元和美元的汇率是多少","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1740451140}
send data finished:1740451140.8425376
{"recordId":"gty000b4532@dx1953af8c59cb86a532:823a7949d1c147efab9c73db3b0f756d","requestId":"gty000b4532@dx1953af8c59cb86a532","sessionId":"cid000b4531@dx1953af8c4e3b86a532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"欧元和美元的汇率是多少","intentId":"chat","intentName":"闲聊","nlg":"这样高难度的问题还是给我撞上啦，换一个简单的呗。","shouldEndSession":true},"nlu":{"input":"欧元和美元的汇率是多少","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1740451141}
1740451200
param:b'{\n            "auth_id": "1bec52627fca4d4f9f116e1df375a023",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid6a88d1d4@dx4b6d1b1879803eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "4c3f218baa774d6fbbe8383e315863fa","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b426a@dx1953af9c060b8aa532"}
started:
ws start
####################
测试进行: ctm00010aa7@hu17be9f606fb0212902#48549983.pcm
{"recordId":"ase000f244b@hu1953af9ccd205c2882:4c3f218baa774d6fbbe8383e315863fa","requestId":"ase000f244b@hu1953af9ccd205c2882","sessionId":"cid000b426a@dx1953af9c060b8aa532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1740451204}
{"recordId":"gty000b426b@dx1953af9c100b8aa532:4c3f218baa774d6fbbe8383e315863fa","requestId":"gty000b426b@dx1953af9c100b8aa532","sessionId":"cid000b426a@dx1953af9c060b8aa532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1740451205}
{"recordId":"gty000b426b@dx1953af9c100b8aa532:4c3f218baa774d6fbbe8383e315863fa","requestId":"gty000b426b@dx1953af9c100b8aa532","sessionId":"cid000b426a@dx1953af9c060b8aa532","eof":"1","text":"一块钱能换多少日元","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1740451205}
send data finished:1740451205.1531234
{"recordId":"gty000b426b@dx1953af9c100b8aa532:4c3f218baa774d6fbbe8383e315863fa","requestId":"gty000b426b@dx1953af9c100b8aa532","sessionId":"cid000b426a@dx1953af9c060b8aa532","topic":"dm.output","skill":"闲聊","skillId":"iFlytekQA","speakUrl":"","error":{},"dm":{"input":"一块钱能换多少日元","intentId":"chat","intentName":"闲聊","nlg":"这要不我给你展示我的其他本领吧！","shouldEndSession":true},"nlu":{"input":"一块钱能换多少日元","skill":"闲聊","skillId":"iFlytekQA","skillVersion":"1"},"timestamp":1740451205}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid8c9064a5@dxb80a1b1879853eef00"}
连接正常关闭
1740451205
param:b'{\n            "auth_id": "b479379d2cf04423b43c9dcf384e388d",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid8b31d45c@dx4ba91b1879853eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"testasr", "params": {"mid": "c5986587a8494a57aa1d5a061e6fdc66","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000b43a8@dx1953af9d24bb8a9532"}
started:
ws start
####################
测试进行: ctm00010abb@hu17be9f607e30212902#48549988.pcm
send data finished:1740451220.8407807
1740451274
param:b'{\n            "auth_id": "bff5eab04f254f6f894426da41ce6906",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451274
param:b'{\n            "auth_id": "5621bfe872ce42f0bb64c0f7fb0c843b",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451274
param:b'{\n            "auth_id": "6b3fbcec9b744e50a68f82ec5684bb83",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451274
param:b'{\n            "auth_id": "dbd9d1ca3cdf4756a621c16a3201c1c1",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451274
param:b'{\n            "auth_id": "b5ee50b511ab4465b4ee1b88083da403",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451274
param:b'{\n            "auth_id": "c58a883241bd412fb00a43341eced8e6",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451274
param:b'{\n            "auth_id": "9577291cd4874960ab985525b9e0784b",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451274
param:b'{\n            "auth_id": "3d9e810b0f9d4291bf6c1468883305fd",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451274
param:b'{\n            "auth_id": "8d003fb6d24841898647a4e2babb67cf",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451274
param:b'{\n            "auth_id": "bcf01e27a26446dba0b6c2579deedaf0",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451274
param:b'{\n            "auth_id": "6c4464d46d804046959f8f5311cc2d2b",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451274
param:b'{\n            "auth_id": "3ca6bc53e36948cd874e219f9a471705",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451274
param:b'{\n            "auth_id": "61792d9b886543ee87b5fb40ef2e8d5c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451274
param:b'{\n            "auth_id": "f12a097365d2466394a603b82f000404",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451274
param:b'{\n            "auth_id": "0b6b636ba80d41a2a51001ca73f03abf",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451274
param:b'{\n            "auth_id": "fd88717dc2fc41d89f7ffe9b65cb0c4d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451274
param:b'{\n            "auth_id": "4533c544452441f596c34fa8608934b5",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451274
param:b'{\n            "auth_id": "58d0b71472994fb08de5ad5c90cf1dac",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451274
param:b'{\n            "auth_id": "07b09fdb5efa43b7a2b09dd21e8a486e",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451274
param:b'{\n            "auth_id": "28fc4d5450174d669bd0bd1d01fb99b9",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451274
param:b'{\n            "auth_id": "a7693b6b53f14a68814e95a4a5a4b4ca",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451274
param:b'{\n            "auth_id": "7874d50f5d624c678d1062943cbbc4b1",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451274
param:b'{\n            "auth_id": "b4aaba4b267e4c15a696928af7b586eb",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451274
param:b'{\n            "auth_id": "d0ab71e6dfe847dea5ec77d9c1d27793",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451274
param:b'{\n            "auth_id": "6c6734df124647eda862bb46271302d8",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "e00ab461c7194ebea9fedf72308a5899",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "faca8515416a40228309dc927f041f59",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "24173158b83f41be9c07ea55ef0dc8d6",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "d0f163b343ce4c05b359ee73f05e0949",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "b8f027d4151141b0a28af82f12e7e639",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "963a5dd5cb2f472885ec2173e78a7324",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "99ee095eb1ca4350b6cf6ea263395e37",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "9939afeb81eb47c2985a06c842a7dfe8",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "54b695af91374341bb9f370d2f8aa5e4",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "69f58e7012d14e3dbeb1cbde7cd8f16e",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "a7bdc04816834ab19200388bcc194baa",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "ac141574cb8c435095bd91c7038a40ca",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "2f0d2932cac045418a68940d931995bb",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "35456229c46042769b39bf4c0fe44245",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "f7e44db6f663416e8f0eafcbeec19af9",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "4b1a6f815b944e9cb7b1b8b0efe11eeb",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "8c8dfb8d2151434884b79dec9d77a3a1",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "d3ad894a79b249dd8a0234a74f50ae65",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "48c25d61b20349f9954b4d5cb9507211",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "704862d9ddb74f38980a57452d3f4dee",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "7abb306161d2447f974fda4eb5de9d33",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "82d07f6d5da441da8cabfff35614dbbe",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "af56eb4ef76f45c187fa8398bb50c4ea",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "83e3745354444e01b3e68c76f5b14441",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "7e6355819ba94e3f9b822139084e0038",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "7d632d327e2440fe845c10b6fb411d22",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "f9571703d12748e7b103454e57fc6d0f",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "8457772c934045729b1f98cfbb5cc43a",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "9eab390228e940158450f618107a97eb",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "0755a662b1be41ef8e76a24772d6587b",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "e3a6dc8ca23b46daa5c923085730442a",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "4373e88df72541fba188d72b2a211344",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "60898668665549159cfbc39ca971b8d4",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "c4c3f7f1002d4dce83316c1f91c5ed6a",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "b2edb8c5f1334980ab7882adac3e4a4c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "7d3c4ba73e1f4bfd9df7e770ecd6c74c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "e2191d40ad694e55afe7542b71002e80",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "58fbfad22d824353b3465beb2ed47564",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "c478c573263e4e1a83d58b7ba812d41d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "6db20c2d0a614f3d93be64b822f83506",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "8409f9362bba42ad915b8cb73a7c75f9",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "6272ee9f04b84c5994fa4fb5d9bbacb3",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "34f2ee3bd993474a8032080d06e025ad",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "9d163fe381304b6f827975a88198f53d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "8f67a38a9d964e5eaebe75ba05d08bec",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "a62d5084feff439a81dff2ea942f029c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "9f171c2916144abc82964979add433aa",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "4cf958d1f8ec43ffa3e0b8a30da6e759",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "812356e941984046be6ba474d66316b2",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "2e73976beeaf4658aea99e0e6358c938",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "aeb4877bc0cc44d4ba71dcaba8c562ab",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "710545d684804f209e5a1c0aea4636e7",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "493563b97d4c44dcb0a085697b2d767f",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "0915378e103e4e83bfd0f9efe8ef048a",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "3f5126e6b9ab4a9a89de99a0b863aabb",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "700641219b6c48faad725a67fcce42f6",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "dadf411c499e4ac19c36f3f28f4e58d8",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "c8df6d54f896478490a27d1342734dac",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "1fa4f86870a64509a6d1c018dd190d24",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "c68ab4ea3c2e4decb65c6928eceaac8c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "0ee126446ef942ef80f798df172b228c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "0e08f1fe31724c81ab242e4b293c87fe",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "dd2fefb73aab43a5a9cc7b9a5c1ba7ba",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "b4957e134030430c82440f1d719a776c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "d70c10001bcb44429c511a26ce474852",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "2a67714c9877403caaa34678626eef68",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "f8c186b914f546daa0e9ef8ee15803d5",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "2eb0b9bb3e7144e8870adec39df4ab6a",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "2103f0084bc344d7baba0385d6c071f1",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "f0affb18cbed46df87378d3734ec4e9d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "771a004ed35b4a41accc67ac1b26c4ae",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "0b8b7fc2edb648858deb39e68ca5488a",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "e43b4739b9a24f11b9ea072998453239",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "631de8a7f4144034acea77aa2224a556",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "39c417701a544ae4a8ed6264e1a839ac",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "2d6f2a5280cc4e0f9a57ac7525fcff06",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "69c5d469257d430db447f679da990a66",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "4f6c389371724b69a2512242fbc669e3",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "3697b586458d46129be88dc8fa4a7c03",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "4fff9180563c4ca5b80bef1d573f6e9e",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "bf43336e55884e48a944e6b132e473f9",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "2a6b8d3e7e3c43e78733e41813d9a4f3",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "1fa74480f0974988a6192d01c201746c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "bca05a2cf4ef4df8896b952fd5ce6ff1",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "d47fbace2a5d4e859a0feaf48770142a",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "9cb46a7605cc45468202d1870f142991",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "0ab1d0fe87284848a47ae55ea037646e",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "352ee37d048344e3ad34d672252e8bc9",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "5b8d97d7fa63401db75ad366eef09bfe",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "cc8bec3e041e48828dd01eb1e21b64f5",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "724dfddcd4544747ae8ff496a9de992d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "fcae10eab0ae487d830ff5016671b67e",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "15bb7092a49c4b4b8e61883dcd2826e5",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "82bbe1e74a20468c86110934b2960636",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "b1da4bc77ff14abca38d9ef1d1cbc5ab",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "d175a2d4ad44487e928bbf5dddc5f6bd",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "6ae6e321850344e9b6aa25aa5bbcd159",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "47df4d9458a9457890dda9d97eb0a697",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "a7d92a305a63411a9e64bfb90aa66a8d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "11ce4137936c4d0bb7134b0692708f6c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "7838678366034bda9c2b7d9d1f640b56",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "ee1d649797044631b6fbdb76fe297bf8",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "b1dad64de5154056a5b48f7b7444165a",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "ff8cf5f24f964ffbb89dc4f7b7683d69",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "1d182b5af4d244d4bbb997a49164c2a9",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "b9fc97b985214c3a82075209594d9203",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "ef849c1a02fc40a189119c36ff6bcc66",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "18662c5186054f13bc14839414e40134",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "63b821629f38404b8fc278954b602a77",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "55633d9ba6684138a73cf866b4cfefc5",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "511a3a80567f43438ed63cb64e0ee5c3",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "2ede6ca01a5c4c0ca56e2e89879799a9",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "bd15a77621ee46a2b58902b8906a7f5d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "ac44eb62c1404682bbf258f66697da90",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "3a4e8ab2fdae41d79aaff0e6ca92edd9",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "56c13e22bf4c4e1095400adb209786ab",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "b5182548ab5f49f79c5b35d400bbfc75",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "3bf5334c470b4cd98fb1056257f10567",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "520db63b24354df5ad9ab2a6fb05c9b2",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "b6f27e4bd0bf461aadc73ef5a16b9037",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "bf42567a346046ed80e39f51e59b56f5",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "98e19adf91574b9b8d5ec600258f4383",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "51a09783a80c48e2adc05c30be3c133b",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "391caf2e1d734d8296c3bbbe4063a87b",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "8c0ceaf8d91149e5be76b26f40f27277",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "cdd71e986ce342f8bb749a36c2934670",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "90e565a83e3e4b41b17fa500c53711c5",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "0f0382fab0a7436eb5f64a222002b3f4",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "ec03dfff8f5746dea2837927fddbadcf",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "419d39acdfc7473ebbdfae5f22f01f1e",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "beb6ad096cb14c27ada8c6a03d4ff892",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "a00cb1e7290640dbb65909f1940b3c02",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "d99b0104781842b78b90ffe47689d079",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "ee98c06475f647a8b38685e62c9601d3",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "dc774f0ef13549d49730911c9ebf1490",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "99793a2dc39648f59938e2e9069ee945",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "c7a1165de6e94138a55c585b723541d6",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "b740876a98324053b64cf2a85114c268",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "dda8d2365f094037b8ea4e2614c2cf0e",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "98e478d4129b4929af24f25c42d6c450",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "a5739f3e249a493ea070d8a0eb86367c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "8b6d738a99b54df89f3f044bd87ff7db",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "4805f3f6c190473984fe106a78bc18ad",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "b42594563dd74324b8d2366721ba1d26",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "83c732a314534edda7751086cbf080e4",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "dea87a82879e4d848fc14e02f14242ba",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "5155660e0f384ecc979b3d7d60c4feb6",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "1c6b12e2d67f4e23bac7e7527263890d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "7f526581a9a0495abb480bd855d08015",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "e5a4e01ef28545cdb5984aabc80c4686",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "11c8a6d0f6364fd48693699676e13d9d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "63b2430476a344fe947c6cf2910ec276",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "98638cbcfd1f475dbf5b29c782d22479",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "07cc2ac32db444bf8eceeca084914ad9",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "2ef0a4e7b6ea471d8320de289cbb1371",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "a3cd81815d5048bb99a0df68e461b6b7",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "e365bacd7dd2473c83cc1c8fbf6d45ca",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "fc15751abb55405da62362f616832cfb",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "c578483a95874a43848d32e42cd729c5",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "dc67831e67d244c2a77bb29d0f734ad1",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "379875d5455945a0bf4ce033c9fcec27",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "c114f58a0ba948dcb3b67e3141e10008",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "a4827c8601fe4863a629ceddef517524",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "e8ae1e7098774c44817b6b421f899010",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "256ac0bee3a84a6e9bf563991d48538a",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "37e10da273a54c168fa801f4de9377e9",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "90ee9d12e3344eb59b67ed9b649becf6",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "93aed297b14f456d817c4929b50c9149",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "54238219d628455895ecbf8a877b1888",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "268c8dd677884cecbe707580156ec4ef",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "e00641fe7e824a13850f41bcb3448b9e",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "c16d0fb2eea44dba83b2a0f94fc09a57",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "e39e7b1b9809455cb807f9aa09801402",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "45a274f4a8604c3dab4b0ab38d80328f",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "0f73dff6d963478683e0840e5acbbb89",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "df4a1f0e530a467da4008bf6b53e8cf5",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "7e51a65078584aa98348c9d45cd8c9d3",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "cc220dba929b4b03931b003b50a6b60e",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "4ef5e76f92bc42bbbdae84f17e85d42f",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "57140d5a25f74650b20a5fdef759374e",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "9086a66999e24a3ea1c277e2d12e9a4f",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "5f090c82eb4e4bc18ceb6280a2997e6f",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "d3f157cc9ca84014ac5e120ff3de50de",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "909209a630a1495c973e731c9d9b0506",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "013665db9be845a6a4ae45e5de7563eb",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "9c44c1cae73c4701a6f2bddfc7e74797",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "bc7571e0df1a4d32a96d2618c3b7ac64",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "f1dda3e46def427795a2bc76b515b9d9",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "0b767feadbe642638dd6749d55817975",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "66d9f5942911440b895f2aa324b34835",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "378d4f48923a4e7da94389082b0aeb6e",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "e6860fd933994dbca1b22977b2d6eaf2",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "054a8835e2614b4a80e3d15ae30fbcb5",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "ab14d22f69ae47e0b09b92e23a7b4aba",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "5ce027c1e50241d5a6a58de0faaa8c5c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "0fe0867d59ff48c9b1b911528459c4fd",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "58ae691a9a5b49959b54805dcf0ed021",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "69db932482994204998f4cfa70be8a24",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "6cd7991011564383a0ce206aacd5245b",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "29f5c32776d24015a7d884a76e640fb0",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "4bb48b93617b46a79b12580c4f39d712",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "b988cbd84d7647dcb86b54b2bcbdf6d5",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "af6fc71f98a44e64b8c18aae08d99937",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "fad629c9a1c74023a6a621f3bb361ae4",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "7edf1f70dda640f18ba9075594aee57c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "9c79202ee7ee48dbb63758e5152c4ae8",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "72119225bf2746de95e92662d17fcd74",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "61dcbce66c7945e894f5e6be626bedac",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "3f55bd427e644493b6c2fa23ad88efe5",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "0f9043e0b8954b4d84f1543ff06324f1",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "6214519d5a78455dbea4ff91083b96b8",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "3ede793cbee844deafd23b6b453a7b9f",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "d4e37fabf8c74d8d885e17f5c7ee4667",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "b14c7d8b23a647e1b5bbed6a2d80d49f",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "6604ccb7413a48c4a4fec61d48404e46",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "f7b0ab1d22904173a8e4dfe5c1449a46",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "bdcee27d4d454f2b979acfc9c43112c0",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "768acd14d3d84e598e96e3350c602e66",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "337cbdf509a040768bddd6088d9a4047",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "19776ea7ab45472f9774f2c149388016",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451275
param:b'{\n            "auth_id": "5006937a13684f11939a9aa576a5aa3b",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "c985edef4dd74c318c3965916fcfc0ff",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "160d7f4d39df4665b366a41c4fda5c38",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "8f2bf1ea5424423c87817c7eb016bae1",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "383ca8b1d53d48269838b9aad65357d2",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "a258e814878244e499914157b49203ab",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "145f200334164ccca18173c56ecee038",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "7a542edbed5b4a2d8d0f0eb3cb758756",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "80d4166c3ce14c66993cf91d6484458a",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "e20836d0f1ec45439ff9a01e6978c433",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "41765c0b556648f98ff2b78bd9bfcd3c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "d68c55691d7d4d7ab8a8aefdf54d86fa",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "98d1e3b3af6043aaa3dd6fd92b668e1f",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "e80bb5e33e1a4f68b28b738d5545a330",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "e878199bec5445a49e770a3d6b778211",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "ef2939dd83c347e1931425bac3c50e85",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "a7008e4f7db644e59a0209b372a1c4be",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "0f12fb2cc0004a2499d5450b7144378c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "14cb11a1a53c4f9a9b82dcea86a5584c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "7ffd2b4c225b43f097783bef974a0700",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "f88cb7b1a1874f57aafcaadc7c4d5bab",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "27ca013e03b94e25bb34260bac49713a",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "2b47dfd298134268b216747742b3ea40",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "5562ef86d19442d3b740b6dd34a9a21a",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "645903d5581b460c811e2d6478c8f720",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "da7fd41644974835a69e26731998ef17",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "80f4617370064cf9b57b36f81da48566",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "233635abe65842b7a0d9bfa35ce3377b",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "16be67a6179c41229507935ec1d02e78",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "38e3b83364dd4f7aabf960a3f45bcfa4",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "c1f1ce6762234e15863e3c15fb2bf695",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "88c8e3573941486199e252a922cfc622",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "e5f2c0ba1a8548b5952b1456a71e6edd",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "97bcf0ff21d34d8c8ec7c673e6226b69",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "d244c6618658485097a0dff976bede03",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "7c270501c55b48b8a729e86f7b6747de",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "67924e5ef15f4dccb635041d85cc971d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "f60a0c1cdb144bc7a3dddaeb09c30f55",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "2e447e5831584430a5294c66ad20f968",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "72e06ab341804183b7d221cc49bd74f6",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "c5771adfe1b941649bfd09c986c8b193",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "68f3d9281e5d4200aebf622f7c2b939c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "ae8ab74870ef4ec0b4e374bc21f28e38",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "614921a40ebf4325a72a13f5b9f1042e",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "72d0fa501c7e49eea1d12be8474d8cc1",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "98d9ca8e4e4a45029afa0f87bb709a4c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "34da3d45602d4eb4894cd60fb379aa70",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "a4e3dfb72c894b329be488064bde50d3",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "73e0bd2ff44c4cd9b0d1ac0dc6765704",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "3626848e95494aca981e9db56830157d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "49ccaaf904c84e33aef8ffed79dc73c4",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "eab2c3ad87d54ba1923c2855ba8c4407",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "cd8e2723b55943d698bf23c98ce908e7",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "62060c3876de4dbfb8bd9d275a3f6981",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "2f92c8f58ef34caab7cd3242d55b8b8d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "6c6d3d5457db422ba6428db580527bbc",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "9dcf5061ef6142438ca1f196ee7e6771",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "1615b6e4c09541d8a8de1f02a16dcb03",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "e26f6e935c43403eae8297d79796f3e4",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "d8efb047526e44b09b5b203f7c1b8dea",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "605ec98267a74527bc180baa1f2c61d3",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "fe57b285d9144662a253552432b43d98",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "ec0b3568819c4f389f5d40cb661548c2",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "6cdc1ae23f054e7a8ab0dca27692501d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "6d441ae032844649b5bb336ed2b3959c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "3fb25fb1a5fb401b97b97c5a3ad78996",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "45f689a8ba68436c9145898ad9b9f921",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "8737281581a64e93b083d5de4b62421f",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "a87bdc6030b14d378c939186fc12e4b4",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "6d7bea48de7e4906873d768d356ece13",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "3626b1286d2347f18bdf47f5d340a872",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "ef20440e964442d3b0ef305ec295543e",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "374b01d764e34f96ad33fa03a87eb202",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "8a124cc4bbc94495a74ca3c33679ed4c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "facd66fae5d348159956bf4360c886c8",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "44426e2c659c46278580cf6dfda1fc5d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "5d0cc3fa583b4edc86fd0d55de9f3deb",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "6c0b5fbface84c1ca581a37add20f3c6",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "15074dbe8dd94ecd9da02621f9f42ac9",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "1d6e67a235ee41e6b4fb5acb856386d7",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "b38cd3434dd04bafab1067fc2a3ada3c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "e4f30b201a5b4144a370c8c8f52580a1",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "3ea97df5a80f47829af4a5acfd27dc93",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "ea239d6725ad4c5992ee95f0497b9722",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "1be6e9134d9a4a09b5b0ca908dc1b6ac",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "32421bb278814e95abd1a5785586c21e",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "044185cac5d8477a8cfe23a810df3670",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "c57c010101604dbb8fe2cfc1256e8777",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "e740f7130f7a45a08b75609db03b47ff",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "bfc56e240473446bbcfe7665679b8a00",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "cb74c61ac15b480cbea89c21053d3787",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "2f7fad2345424c52a3455bfe3c8dff01",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "eb073ccbb42746508c7ede8933925cb3",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "a74a65e318294adabad33a1e39c88ecc",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "f03111f126904f0dbdddd17a05b35769",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "b6d70226ef8f4d68879ca7e52dd2ae6f",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "0a00fd5d42cb44b8aae0e325504324f3",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "4113ae20729c4f8caaed7fc761f4fb7d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "622de6d339ed40529dfed888a316ac4d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "94d08ce183734f53882d7a922d9f9f9a",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "372ec15192eb461a804bd24d14c26d4d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "5d3aad27a02743a49c72ae15a08183b6",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "3f663231d49c4bcd94cb6ee1e9f6938b",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "0a37221371994bb4a6f6682372936906",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "b60fe68c82e8401cb189843d8b6ddc96",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "4780978da2ee4205b0b665b8955bcf76",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "1037588936484f9d86fe23c85882c3db",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "f0714a1ae8884f44b3fd3fe08747f480",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "12911a9cc9bb4cfb91dbd70e1389af30",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "a6bc42f208374829ad56248faf2234ca",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "1e651a68181544d295b5e302a617b213",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "90f728bbd6d14e5b87ccfb94a8138eb2",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "a37f5b504eca477c8c6a900c6ce7a27b",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "206e313e53e440008b5b1078957db33c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "3cdc2a48645e4c1d9e0e78a97e802ef8",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "0f5c096ada8a44e198bad6c252aa73f3",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "f2d0d93d53604505bb09500e502724f9",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "1fa66bb354e2419fa53126881026960e",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "c1a1903f51ec46b5b14d1fe23dc3e304",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "ff8e56e3eac34fcba808b4b51c05c747",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "e1ae02765ae84a048862d6e1e0da375f",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "87574642fd414bef9dd19a9ebdef08aa",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "c4d87abe68e7425cb626bd8a97d6ee4a",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "6898cc3105f94d03a393afaeb5fcf6f4",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "9d9d5e36ab244187bef3a31b1ee9e610",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "89fd451d5ad34da39f6e34fb5a10bfad",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "361383ba42fa47c9971de404c6869acb",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "b1074393033c4850b00dc703fc4e34e2",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "b8b82e1191c843d88116871182d81ca8",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "887a2b384ac5455eb09d50243e285282",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "afb8fd462ca044d481136ab11ff22733",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "fefe0cadaa71478c8b5493437dcb0820",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "8bfe2402383a489c9a654409ccd89562",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "3df244ac6f3c4f30a43061d7049957bb",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "1e7dde3a3a094a8eb88cc07bc9f0a1a8",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "80d5b80f4629454c916c53791535d290",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "eb03aeacdf4f4cc3b5fc1cb8341162f8",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "479737955bfe408bb363705a72310173",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "db11d83df0984de289c3f13a05613ac0",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "00d30edadab74bd0b0da4cef475d40cc",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "d0d569315d7044b7a64cd99401ec8a63",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "972933a4eb8340b38ded9285c6a5bef5",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "9cede02e5367487db240e5fb734e1eee",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "90c108d0141e4fccb28361fc6b6806fb",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "1edd14c7cbcd45038a0dc4b6655976c0",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "56e937a3268040cb97c16b6883104c40",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "5fca9e2d78b7462e898e8143484ae49d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "1e15c1c96afe4864be8221b93efaf0fd",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "9782d57f2cdd4555ad7ab57bacfaa389",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "3f01917243374ce18301a02d524d9207",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "fe790640a2bc4dceb766d3be9bbe08a3",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "6561dc62308b4d1791cc8e2064742334",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "de44942e26234e65bb8f6ee5225b689d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "e28678de7c1e4801bab64112424653f4",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "0a0814a970e949969fef149ab2a908cd",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "c2ad3d4fa5374e9283322b26910c2208",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "8e00edae5dc74239bf2ac54fffd69793",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "843480f0d8d64cb5a8fde2e1f873ee2e",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "9f352b02aa614376833b173b1f985af3",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "a705501c05d245289bf24b1ca89d311e",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "f939021d35c3451a92d8cfa61fd89934",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "d0a18cfc6b554156aa13ece09a32a3b1",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "7f81976d99734e04b733606fc8eb6182",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "eac27349f68340cd9344b206def25b38",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "cd989cfa201f4bf78254527673793ffc",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "ca72e9b4a1d14dc288c1f60ee5918897",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "2b3e1840e69e4a7f9e0cf6b6ad92539b",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "d4629eef17f246528674dc331813f520",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "6faf9c33b954492fb66433a4b93c6867",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451276
param:b'{\n            "auth_id": "fa9145b2331646c198c952554dacf876",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "ebf0e67f5c7c4cdfa6da68f46c06e21a",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "a99076a1436d43c0858648d4e70ef050",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "ea9616fa005045b09bfff7d19141882f",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "b0d67e9b79914f72ba6a5419f98bb43c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "ac10db9018764834a4c56e3492c10236",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "1873fa59c8ab4dc09ca1a369b7377cab",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "60084a4e08b44dd5ab91e8a087ed434c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "a4f6afe7ae284ca5ae726d6f3c6a8c49",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "a702e6558d454a56a707780dc5dc29fb",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "af9a629068bc4d3bb22a91b451a99445",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "3045fe350b7d42f3aaca57f5cc664ad2",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "2e8137f64bec4a8bae6c305cb285b469",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "4692c7d71e774e0ca2839f844e79d1c5",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "f7f00b4043934037bba87794e66c8b8d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "9234ac7a0bfa445e8c7916e6330e62b1",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "1f4c82d6d22e495f9a881540eb17a25e",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "901a649ccd4e4d468336e372cd610e55",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "1b98ffa38d9e49a3bde3adac3590c55e",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "f589f308a46140b7b52532902e9e9c38",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "efb3418ee7f54e1584fec76a06e98912",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "a134e714096d459cba934ad8ebb83671",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "30092d286c58401fbadf1dd537043f29",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "ee55bee381b642f7996164a553685725",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "c686e54eac57403ca76eded2db0c04b9",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "4f7f923bd38c4f80a4fed61a68d9ded2",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "17cce1bce25b4de4bc4106d882b9cf9d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "4a7580cd0f464342863b2998a507db04",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "5c06f840a0204df1b12bdf14fb982404",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "64065a7b98a747f1b390eb58f3797afc",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "aab9025a725143259fec403e77673c1f",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "738476057b2d48858b6716a31038b4a4",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "2c00cbe39abe4eb681d65f38ca2d6940",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "5411c36c6f91414da8a2ef754414fbe1",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "7465d3148785484f9b139a901c062374",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "ca3eaa8238ff441d84963cf64e858dec",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "4ef389cdd9514868ab878fd5e834cee9",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "583e9016741f4b27b3903fff35c78d0c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "cbd18c39a2484f6195f7301001a933e6",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "752ffdfcf135464ca73086c23768e8b9",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "7b05793a4abe4849a85bfe849abfbe70",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "927af985e6c24792846addcdab82c11f",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "c53674fe4f9d4dcbbdd99cbef68f6bfc",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "979dccf3f16d4a8c839314070fdb6fa5",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "e17c657de6a3496eb6fa330e9006ad00",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "7b099cbbfd594831a655518b516cd6d3",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "61d1b0a35e65476bb102268bc666c7b4",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "e8e1f85e973a462cb875948036eb87f1",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "2107214555da4a4ca7479d32d76eb2c8",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "78202a1fd9d9405f95a8b23f9c10a85c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "26ae186bf9934a1d8447fe874542ca00",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "972358ec247e42aab8450d524f250c51",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "00b8914046614d75a0b60340cc1e0663",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "64ff1552ecea41edb941f297b60a9d16",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "d4a49a739e4f4a33a222ceec1f3e3ea9",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "dff919da7b1a4bcfbdfa9f58184efaf9",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "6aa88fe406884ad29169c64dca5a7d06",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "3cf0cf7ca9ce49c39901bf915b1ca68d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "c47eec7e4124439397cb30ab9b59f5c2",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "00e007dfe2684feb9115af377702d5fc",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "cb6fafd787b94003985edb0e061e3f9d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "b7f8ed67fd9948649a4c32b4704317d3",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "0d0c416f067e41fc81829518211f2893",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "5875b32bb3024e869f56f740d739afdf",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "d6cf927e1603497fb427b6d567aa1f94",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "ea99ec392e65481cb5aa4c5e32455c0b",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "09343452331e4592b96c76b9d898cb38",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "c96dd81f615140f983eb9419bf9627b5",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "1f4fe1741acc4e38a9dc6bc239357b9c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "14b40de529df45f39a397bdf437d630e",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "32b8ff4ffe3c4787926aac55ba75264c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "a0ab359343174df2a2a44fd3b323ca14",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "f9651bcf8c064ac69609f0b71f1cbac5",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "412c9fbc836d40478c5820b71fc8c958",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "6b60db8183884b45b456610e23392ae3",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "6430fb7e1ca644848d669056a7a84a70",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "27f5726f9b02428db26ddba74bb5f8d4",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "d9d9ffc77cfe4e1c97e62b45591d9c55",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451277
param:b'{\n            "auth_id": "18a59ae856e14464bf02c893529a2d38",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "0910a996bab7402193e9ba1478a6644f",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "13cf73df1c944cfb95b3f52ba45e37f7",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "d586bc1faa68420698bef72de6bdbc96",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "d0dc0d40cbdd40ca955056ca58dde79c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "d7fc3fe1a15e43c8b8eba4662235816e",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "7b412af6b2e548318cdcd9d1f6e18095",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "b4557f9db43a4fbe907dc36e3f4581d8",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "18729f60e4484beea1ac9416ac1bf67c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "d8fea7651ff44b67badf7ebec7900c49",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "c17c13dbc870412e9aec4676e16393ea",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "647eea6ff0514586a049cefbf6f20aa7",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "1577096d6e91435c9a9c7c75b06964f0",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "2d5521f120ea4d1d8b194ca269ef72ab",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "ad663dad75ed412da3a92e1d4f7876bb",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "51309acf89be491ab627ef6edc30fb79",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "eb02a67128604fad8d1655a7f5d2e62c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "8add8fdef9864d448dd96447421b5b1e",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "dc688a30960c4ae0afef390ba5626a4e",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "e81245cd17fa4ceab9671d37e520d520",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "4ed217524ff84551b6f7445d5db50f49",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "e6a0957e704448609d92fbd6a048662f",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "ec60d2980a53442d8d17f89d1802937e",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "b3df099db7bf467bb0285b2265daa927",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "8560c44325aa49fa8543ef63dd0c7809",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "71c0433deb424536a3be8ed436a2c26c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "9184c2e2fbcd49fcb76b48b091e3db8a",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "36e34f9ea5db49d7bec00e5e3630a986",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "94c9dc05dab34fdc82106eb7d23b24fc",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "e0f4dccf14544f039e44179290fa2fc8",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "337a1a1bb4c54878b57ed38f637b5e32",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "dbec969f3e7a410e9b96901a0aa5d8d3",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "ec1b6f9f2b4e44aeafb5e90502ba371a",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "5133a912f1f5462eae3ee5d9d952257f",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "cf70d9e791274273821ce50a2f355e04",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "5e6d7eb634a742d0912d27f7271b8394",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "43762303ae204eb59ec57def95fe72e2",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "0c5a6b5211cf48fd83d500b8ce0826f5",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "87a842669d3146488960aaa5d3e5e71c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "b33c2202c573430caa783c939785b2cb",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "bad7557526774b96a9a35ccff52b5854",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "783988192c98483fb7dee935cf24cc0f",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "1a39456438c3442b9eaf5edba5297bea",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "c73ff92d3f4c4560881a1f55683e19c1",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "d7d930c6807541e382ca74d7fc1dae24",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "b0b275fc3aa74d638b27723491ddea8a",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "7a3fa129966b4b448be9165c43dc747d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "361f4ae03d2846e791d5de87186336aa",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "f8db38f937a9455bb77f54310f91e1c6",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "d4c2d323e28c44e3a7e0369436df2789",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "f3d7bfd7b9ad4a3c8513cf32e2856c7f",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "c40284cc138046c3ad1314a44d6b4282",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "f77839b709ca414180c71d5e23a5954d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "9e6d9165df7b4424ac15af67ea8632fb",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "74ae1f09098b4e8ab36091c0a71761c4",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "3cb828d96ae54ffdb06f5fe470076396",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "9e46068e14754b5290947d5553e4d055",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "6502275f72834f3085f32da8a19c109f",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "4813e1fb048042e699dd0f96aaf003fe",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "bf91f23f4bd045c4a09893074cf4f7e9",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "adf56cbaaa3c469e9d5b563305a4c980",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "a1a3cee7615142249bd18e0b15c41464",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "e5ac881047f0418685865256c9603c86",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "da932e9e05b049e7aa68ec546d96554a",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "8d75ceafa26845e1b37d4a456dc32ee5",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "a1637bb1a17346daa655f8df878f349f",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "37a5b322db19457f9e0133300e9e3d1b",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "2143f4136f47472e864df5dee8a94488",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "33e42722870f454f8a662fb6714cb86b",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "2057f649d45b441ea38ca2acd5d4fcc7",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "bb5d563ba5d7453ca01515e7178ab385",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "9e3f1f914eca44179fce5dafb6907e73",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "57a55337dfed4bbfb87b081eaad0ba12",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "ccd9e86d1dc34ea6bae5d4124db03c8f",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "d110ba72ead147f0af9b37ecdfb9e269",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "94a23ffadd4d4eebaf922dffabd5ef35",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "1e09da0b6a544b5688aec47c9859e437",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "852a83d6743141ea996c3fc055a36495",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "74b2e522f43843a989292d5be0578ded",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "53233bea4ef947b7b49cda1eba3f8a34",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "6b49e7d1dde74c309e86ee1229158209",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "c9907cbbcf974025a10eddf8fe34feb5",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "72769f129fec48dfbeb8c99fb05fe953",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "0f6f7f5b73814de684e44f2c2806345c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "faa6af84dd2a4d3580ec23e912a32db5",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "0eb24a4a7bb447a5813a586411b5b9f2",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "6deb7f19a71842df9a7959b332d66747",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "4b0564b5189043d786144378cd49e583",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "aae17f6955434a6db44a527beeed30e6",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "2191c9f6b94b4a2ea792446e3c4e4f1c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "83bcd694c9de4238a12d8775c467889c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "adc1f08067e24e5bad20a6afd7b57c1d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "46b63c8a2ebf4ecba39b29a8ef791aab",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "24d94269cd8644f1b8f5dc7171ae753e",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "9110e0ba485b4541aadc0926f6b60cd2",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "d3fbbf618c9a4881b192d42220ba542f",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "2d7b9dc798de42128e16cb3647d88ec5",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "1413663797074989bd535c95b41ff623",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "4fb6232aaf17416891883a913913ffe1",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "0fc1e19514b64d7a9cc1fbc918b8f9bf",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "0300f029f94b4d5d8e3f013a2ab00d8c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "88f8a741071b489d977333262a0258fd",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "bf7210793eeb4b529115a9c5a300696d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "46e9f3be7cdc449ab911d687613b8e75",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "c9534974931e47ffa43782fe960e9a46",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "40d3654364714ddb9c23fdf08585f444",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "c644c373debe401e90b73a37cb6a8380",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "3d3608c9eea246deac65d3f0f8fcea6d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "c513bae48a9441618fdf4f423597ec35",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "be2de232d3fa4a5792dab1f197e5e0d4",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "c56eb81c08704313be43357061e44f1a",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "d749da99eb664f01ba40e75506c263f4",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "80dd57275fd44a6cb7bd8c1523143c77",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "497313f2898d41bfb95a56259934a9cf",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "e2ac9d98d5384201ac338d614ee84974",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "f14d8adad8a9419a8ab7c57a6731a532",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "bd385ed1f72a4082b13d769304846997",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "63e1f778c70a4ab386e30b0f84012a2c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "16ada9ebe1c74c918cf655300331a718",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "e96f96e829f049c7a99d8ec737bfe286",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "4d612aba75164c2ca29221fe4c709301",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "f0913301b8c8400c840d775237dc8b00",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "cc785b6ba58e48a69d343d6777b67740",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "f11efde80c484a9aa78d2c329cd5c091",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "e418d81a021c48c6add4e52b60141bc1",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "e297bc18600c476b8ca5b456ee294044",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "efbb8e43e94b40e3827567a103aab936",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451278
param:b'{\n            "auth_id": "29001186db3b4e5190404ec5d47c401a",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "9e42c7f869c1452eb2f5500fcf641cb4",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "798462322b31463190b601086d6c8e10",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "b7e3194e28d847f5832d276dca7959ae",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "c7186997a2244a648669acffb7979ff8",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "30d6931b0a054c5c80e093fc6bdcc05e",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "80a514138019408da90f25e0f2c4db8c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "53ca852b3aa84040945e39ea500b01c8",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "a79c7f5b08ad4d42a30e8a4da7a2a6eb",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "87fc34f2710b4475b2c5fb716b88b4b2",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "0c205a031e5f45c0baebcb52d3a78ecb",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "c4683767403a4b16988a6fe422b9b9e6",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "a904769a7ccd4cd2811498b481e40ea2",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "2ab328928dd043f3a39e6f829cac7cba",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "8b3e1aaee02347c58d181b4a359e716a",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "5e5a83af01d2459896c6b7f2a0084843",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "304dd81e4cba458380669afb10021915",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "dc3e96603c07443ab273387f9e1ee786",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "58b3a2b561dc42fc908e78998576d8b0",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "1f9fd67cf26347d5a07a7376a94cb3ba",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "d86283ed938a409a9a864bb34371d18c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "46ae3d5cc843461fa03732cc639e3bfc",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "e97452aed0ee4500ad88af95fafa026b",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "39083abfc04d430a8a71f4106174fed5",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "4e2e0c8fe69c413285694b1e934f4814",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "6a622662d3114c22aca1487999a038db",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "77885aa075a64955a7d8c5d6cfa59fb3",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "d83781d8836e43ceb225c8e395318474",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "3fb9831119de4cad80ecd8dc79198c56",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "65ec87a56b8f4edba391b0a43a0b890b",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "9d8a2c9da51747dda93db108b7650080",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "f0ba9159385341aabed8a9e59e597735",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "c6749c9a789049308aa9683b1b65276c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "5a3714ddec144bc48eb88bd01d55a6f2",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "e0a0510c95b64780ac156da730a25801",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "161db77a896243e7b236913fbbd04fb2",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "6224266889fa454cac100bfdaa90874b",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "fb9b851a05564bd38b801ae99f5cde3c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "521f0f95b12748b7bdde0759399da26c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "1e39dc416aac4a07acc7e7ca799ee607",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "f7be35ecd1684821936975542b10cac4",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "2d68fc00ff65453db618140d757ee25d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "de83809aa21e4593b604e48675be9c0f",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "dcc4d1ba8d754aee8feb81b76b28a711",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "087283f8786f46639c121d2eb8742efb",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "d68b0a9136c145cfa52748d5b93456f2",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "1ebc10bc364b451d99f76b20be707ada",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "375afb85e36f4e90883e3dfc765042e3",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "38f484c65b4945c887bfed1520495dee",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "8a9b8bb2acc3475991fa15afcfb7d0fd",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "378941fbbe5449099285e00825150f36",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "37f0e6257ef74177aab5da9a730a4a1d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "c5d84e0147be46988ee2a1007aeac0ce",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "65bfb6ec424b40fa838942962a5ce975",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "eac48e2d564b433c9bdff4f687f08184",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "241d68b6ed8b4ed281da6896fa47cb12",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "a55d3b383bca4fe5bde60295f81bd107",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "31c6ff4e262641f185699a9bf15a9c31",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "0524a8310cc44bc18d98992ab4ae4259",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "653ab2a646694741a06f469b52992eb2",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "fc16b42063ea4e61a74d6c0896268b20",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "f5f9ccfd1b4a407587c0e9a55bff6695",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "1dae0a05f06a4d6da9301669033d4cb2",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "b9673f2f2e9d47c4b379a3c1df3775f9",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "a26c1f96374c4993bfbdd208304ec410",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "b4fdf3b91d774fdcbd81536b689f9020",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "73666edd83d94ca7a758826275c1d693",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "9cfc103ab1b8438d9e2ebaf3b1a22153",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "59e64a4d64854a51873652577546cfa8",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "741d539421c04e54b92252789f2b8814",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "0e9b8902f92242238a34258609aac699",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "fdbe33ce1052432ab29be727e6d812c2",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "58774be2279645b9b838f6898695d30c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "984a0d7a3cb64290b234d0e3e7c22d17",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "ba04373581c54b1d99df5dcd4d202286",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "09bc377e180c424b869ef7bec6f37afb",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "0f231305616b4a339bb0208f5b6f3f48",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "7aa39ac29c9e412996c49f5ba27e8cfa",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "bf72a8bf69bb4eba8c10999e4ae30c7f",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "0d8b339add4e4b37bdad854e19f9f8e8",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "19d99c8c70464f6091796846daddfa3b",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "e185deec77a64b4bae33bf3cbe04e954",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "4ee565a344a44f4e8d5e23ad7410599e",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "f87169af82614b6f97cc32b5f0978e49",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "786b907278614e5fa16e7bcc02fd138b",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "42903128393d4ec7998cc671725a9f11",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "51a92279914643658ddcc94b8b799f17",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "81b1ba76d5234e22ae170153664f495d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "a3307c59b01f4de3906de5dbd96dc41e",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "a4f5ee54cfa94fcfbdefd1b9d10f1e93",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "c1bc2bb22d144dafac75e0f4d20f0021",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "a225bd8c886947378883b18130b403c5",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "55f1b48b6aa84bd694a2fa2d92863340",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "92bcd7bb26b2495a922484007c30a383",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "086f23b70f2a4f5eb7cebc0682c0f589",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "1d1e62e8c0404f27abb1a270fd57690a",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "cf690be8029942f8b02804b81824edb4",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "7b3501ba94eb4b39acdf1040d334eef6",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "90eb234e50a6440eb812b61ff74bd6c4",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "8f6d118c05014d739982877d83f93947",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "2dc8b9fb97de4484a4ca4737df025721",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "e781f406bc294b13b256c8c765e08080",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "41866c76017d45f6a8373d109791f1e9",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "6cd47d16f0f8419faf813c1f4fbddd14",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "bca8b2e862b44df7ae0e7fb8bd551a4a",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451279
param:b'{\n            "auth_id": "2770efbc91d54858995c04768556d00c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "7962e627895f4d8bb1890d6cc6a95c86",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "0791a46e0c0c4b03b29110eccf1910be",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "cc5a1361bd974c4a8a2d6cff5d3fdea3",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "6d73cfeb09ec4910a37feb132978b3b1",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "e5688c31ff20479a8ca5c5f23a66f460",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "c019fd7330594d9f998510d5cd382db2",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "7ff1fb7f5def461b9487fedde97506ed",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "eb8f8825ff6141a2ab20d38842998617",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "e977afee18b0451790fe0b6db4cb5fd7",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "8d29f1a4572244c68c6fb127677748f0",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "aa0c9069ca5c4e10bfe05bb87a7b8e46",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "4265c6911b5c40759f128f1c15568305",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "ef3aadfe8cf74997a65ebc1e901dd368",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "cebaf5e687154821be95a6c3e5f34a15",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "b83dc52e3f8f4375b71d1fb4dfd1b4b7",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "3ea566b4c6664e47abab1c2c94d66a52",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "5cce5b5bbe31492591a1ef6f04e7d7fb",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "49b638f6a78e4e66b2c7f3ec43edf6c4",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "22746ff93a1947b3aa31e0f37bee04c6",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "bd5588ad4ee84f9d9ca875c7f9657e9f",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "cbe9e54b02194f6995f4e3baf158d30b",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "cb0aa859ff4c4a8893515d218722111f",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "db5bf12de667428f97f68dd196162e14",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "3041283f0ebf46d9b40bec67333f338e",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "d8a3bc2bca7c47b39ca295a51b7a1525",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "c7e93e7dc81f43aa860d90a3199d92ee",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "9cf1cef151c7410db51c607a1f6b8b8b",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "1dcf8ca1b7654755860ebf37f6cfa2be",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "7d7864f2f98a4de7afb0a636ea73de52",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "92056ab7e4c84030914fb1356ec04942",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "2fb2fbad43454e0dabe4dfc2f60aec5a",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "48dcae188bdf4824a850c30b87b1758a",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "526b9ec344cb4b2d82616f43b26062fb",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "28e27f765cf04903b1e63f502fa3cdfb",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "c5994d8e42934f498e5c1cdc57e5cb65",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "3c84bfc4f23c4ee5a582ea2857a7617a",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "05456c1069c24d43955df1a1e7beaf2c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "1971d39e764c4dacaf763caa87c933cd",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "d5621c2474de417db82fbe5381b4bd03",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "81c1e32095844cb297e3ad4ebb1cc19c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "74d22fa391b842258889ea304ee6cfe9",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "576833d3263949a3bf14b831cd5f3338",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "9abee747aceb4e379b01df93e339659c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "a58c5363c2b245a396a27df0927a6751",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "fb6197a03b3f468b9b6e0d6651f4f3b3",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "32691f71e12c461faffe3af54d0c9e5e",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "88129bceef6942eebc838e7d54f6369a",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "cc808b9950ad44159a4c3474de041d35",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "c22b968da6ec480da8b0bce7838fb91d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "057acb61dee74ae789b5fd66e008f3f5",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "14dd298bf00c47189a21a0a433ae0227",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "174ba0d8853643ad9d0f32d4f0f5677f",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "3e1e3d2e72cd42cba5e1413a98f7760a",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "afdd5bd638e745a393505d263530121d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "de51df0df07549afa3130cb626ba7c79",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "b249024a63c8472f8b23a3f314aa4bf2",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "b2199991db0547b48ff9cf4578f07498",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "355fb129e4154329851548ebd2b82263",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "e1d188ecc4764c24820e311bc1221c0d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "9257cc0dfcc3421d82d367ede63e06d1",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "6cd7c1a2379b411ba230d898ed7c1f9d",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "ab99c5ba722941338d703b6768540022",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "86847334a2c14d25a79a565feec9a399",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "d6a1955966464a56b6c49a5b495337b0",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "45aefd70a1e446f8a66f3f03d7347349",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "8077b766f0374bc0921fea5e688cea62",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "dda641590c0f453f86257195542132bb",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "1b1c7e89eb044e5dbb82dcfd4a06cb55",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "7d31ad0a7d7b4d889cf6e697b8c8794f",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "8b685592443f4077bda0bebd424b362f",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "627704054deb4398bb1bf173c81fc16a",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "5ae81c49460e46fbb231320ffea27e5f",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "ee500ca832da4a40b88115b799bcc841",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "8909359d9caf4f16b7b54e1cfef7f048",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "d51a2bf4db1e4eb4bb8ae781a760bc5f",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "59550bc34b5042f59b5173a08700a02a",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "24361384f6894f3dbe231975efe486a7",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "837c5953fa424bdabf4fcbfe558e2bef",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "36af4bb1b7e64e03b097e093844fa0c1",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "c87a09d538644a54bc5c30764e5249b8",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "cd62798975514e18bdbed5c554a3691c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "b805622fe59f44ae93463ee31b14d837",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "db1730de84644faa9681af94d41f7fe4",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "ca957b074596444a9a402b6017a7e30a",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "fcea95a3657f4835a9390b43c46d6acf",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "a14b96bdf8be44cfbea6a6e3b9972eeb",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "bef7c299cfbc421cb1bbb388356a1460",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "14f6be68727545629ddf4ede9d1c40a4",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "07ed62fab0654f8eabdeb8f10ac9eda3",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "a71709e4ee784932bf41a38dca85e98c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "58328a3951cf49e8a50c5ca704efe0f0",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "a612a6f18d0a439eabe09fdd6b701283",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "cda8c250dc5546b89adf9b5ef67df7f5",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "1e8feeb536aa414d84850e4aad57f264",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451280
param:b'{\n            "auth_id": "301c88c2264c41ed89d3ce8e709625b4",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451281
param:b'{\n            "auth_id": "7ec3e579d44c482489720b8c4fd09bc0",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451281
param:b'{\n            "auth_id": "a545e577b72a414ebba71603c15ad45a",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451281
param:b'{\n            "auth_id": "07756add65e74d4a830ed8955af2a85c",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451281
param:b'{\n            "auth_id": "7c4fc0b1f04b42da9468e46a1130d556",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451281
param:b'{\n            "auth_id": "30885b39b68f42c4bdcb2f3e253234c5",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451281
param:b'{\n            "auth_id": "93b9fe2fcda34b6591a8ce8f7188b620",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451281
param:b'{\n            "auth_id": "d99bfee73a834ef094d49a1f2092e0bc",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
1740451281
param:b'{\n            "auth_id": "a293871a1d1a4516ade9eef4f59716e0",\n            "data_type": "audio"\n        }'
连接异常：[Errno 11001] getaddrinfo failed
#####运行完成,点击查看结果按钮查看结果!##########
