1751444226
param:b'{\n            "auth_id": "5864fe08bed64c44ab75bd6a0e052e8c",\n            "data_type": "audio"\n        }'
{"action":"connected","cid":"cid3baea427@dx19f31bc037023eef00","code":"0","data":"","desc":"success"}
b'{"action":"start","scene":"mqasr", "params": {"mid": "d01529f1f10f49e99c54591cb9d7ca18","asr":{"eos":500,"enable_vpr_rec": true,"enable_vpr_rec_info": true,"enable_vpr_cluster": true,"vpr_rec":{"group_id":"abc_123"},"encoding": "raw","channels": 1,"bit_depth": 16,"sample_rate": 16000,"pd":"midea|midea-dragon"},"nlu":{"vcn": "x2_xiaojuan","pitch": 60,"speed": 60,"volume": 50,"tts_aue": "raw"}}}'
{"action":"started","code":"0","data":"","desc":"success","sid":"cid000bffe2@dx197ca3601edb8a9532"}
started:
ws start
####################
测试进行: ctm000dde24@hu19347966faf04e0902#117600897.wav
{"recordId":"gty000bffe4@dx197ca3602a9b8a9532:d01529f1f10f49e99c54591cb9d7ca18","requestId":"gty000bffe4@dx197ca3602a9b8a9532","sessionId":"cid000bffe2@dx197ca3601edb8a9532","vadEndState":1,"vadState":1,"topic":"asr.vad.end","timestamp":1751444229}
{"recordId":"gty000bffe4@dx197ca3602a9b8a9532:d01529f1f10f49e99c54591cb9d7ca18","requestId":"gty000bffe4@dx197ca3602a9b8a9532","sessionId":"cid000bffe2@dx197ca3601edb8a9532","eof":"1","text":"音量调到10","oneshot_asr":"${oneshot_asr}","pinyin":"","topic":"asr.speech.result","age":"adult","ageScore":"","gender":"male","genderScore":"","language_class":"mandarin","timestamp":1751444229}
{"recordId":"ase000e1128@hu197ca360ec905c2882:d01529f1f10f49e99c54591cb9d7ca18","requestId":"ase000e1128@hu197ca360ec905c2882","sessionId":"cid000bffe2@dx197ca3601edb8a9532","topic":"asrplus.voiceprint.result","speakerLabels":[],"timestamp":1751444229}
send data finished:1751444230.057166
{"recordId":"gty000bffe4@dx197ca3602a9b8a9532:d01529f1f10f49e99c54591cb9d7ca18","requestId":"gty000bffe4@dx197ca3602a9b8a9532","sessionId":"cid000bffe2@dx197ca3601edb8a9532","topic":"dm.output","skill":"音乐","skillId":"2019040400000411","speakUrl":"","error":{},"dm":{"input":"音量调到10","intentId":"INSTRUCTION","intentName":"音乐控制","nlg":"","widget":{"content":[]},"shouldEndSession":true},"nlu":{"input":"音量调到10","skill":"音乐","skillId":"2019040400000411","skillVersion":"118","semantics":{"request":{"slots":[{"name":"级数","value":"10"},{"name":"操作","value":"volume_select"}]}}},"timestamp":1751444230}
发送结束标识
发送断开连接标识
{"action":"closed","code":"0","data":"","desc":"success","sid":"cid2316522f@dx1b1f1bc037063eef00"}
连接正常关闭
#####运行完成,点击查看结果按钮查看结果!##########
